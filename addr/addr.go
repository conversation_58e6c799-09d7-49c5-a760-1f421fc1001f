// 统一封装服务访问地址获取。比如redis、mysql等访问是需要
// 目前支持设置hosts\bns两种方式访问
package addr

import (
	"errors"
	"fmt"
	"math/rand"
	"strings"
	"time"
)

const (
	REQUEST_METHOD_HOST = 1
	REQUEST_METHOD_BNS  = 2
)

// 目前只支持host和bns两种访问方式，后续可以扩展
type Addr struct {
	// 访问方式
	ReqMethod int
	// 通过ip:port组随机返回
	Hosts []string
	// 通过bns访问
	BnsName string
}

type HostInfo struct {
	Host string
	Port string
	Bns  string
}

func NewAddrByHost(hosts []string) (*Addr, error) {
	if len(hosts) < 1 {
		return nil, errors.New("host set error")
	}

	for _, v := range hosts {
		if strings.Count(v, ":") != 1 {
			return nil, errors.New("host set error")
		}
	}

	return &Addr{REQUEST_METHOD_HOST, hosts, ""}, nil
}

func NewAddrByBns(bns string) (*Addr, error) {
	if bns == "" {
		return nil, errors.New("bns name set error")
	}

	return &Addr{REQUEST_METHOD_BNS, nil, bns}, nil
}

func (r *Addr) GetAddr() (*HostInfo, error) {
	switch r.ReqMethod {
	case REQUEST_METHOD_HOST:
		return r.getAddrByHost()
	case REQUEST_METHOD_BNS:
		return r.getAddrByBns()
	}

	return nil, errors.New("get addr method set error")
}

// 暂未做处理
// 后续可以对Host方式支持异常节点临时封禁和bns方式支持示例cache及临时封禁等
func (r *Addr) SetOk(hostInfo *HostInfo) error {
	return nil
}

func (r *Addr) SetFail(hostInfo *HostInfo) error {
	if r.ReqMethod == REQUEST_METHOD_BNS {
		NewBnsService().FlushInstance(r.BnsName)
	}

	return nil
}

func (r *Addr) hostToMap(host string) map[string]string {
	m := make(map[string]string)
	m["host"] = ""
	m["port"] = ""

	s := strings.Split(host, ":")
	if len(s) == 2 {
		m["host"] = s[0]
		m["port"] = s[1]
	}

	return m
}

func (r *Addr) getAddrByHost() (*HostInfo, error) {
	if len(r.Hosts) < 1 {
		return nil, errors.New("hosts unset")
	}

	// 随机吐一个
	rand.Seed(time.Now().UnixNano())
	index := rand.Int() % len(r.Hosts)

	hm := r.hostToMap(r.Hosts[index])
	if len(hm) != 2 {
		return nil, errors.New("hosts set error")
	}

	host, ok1 := hm["host"]
	port, ok2 := hm["port"]
	if !ok1 || !ok2 {
		return nil, errors.New("hosts set error")
	}

	return &HostInfo{host, port, ""}, nil
}

func (r *Addr) getAddrByBns() (*HostInfo, error) {
	if r.BnsName == "" {
		return nil, errors.New("bns name is empty")
	}

	ins, err := NewBnsService().GetInstance(r.BnsName)
	if err != nil {
		return nil, fmt.Errorf("bns get instance fail.bns:%s err:%v", r.BnsName, err)
	}

	hm := r.hostToMap(ins)
	if len(hm) != 2 {
		return nil, errors.New("hosts is error")
	}
	host, ok1 := hm["host"]
	port, ok2 := hm["port"]
	if !ok1 || !ok2 {
		return nil, errors.New("hosts is error")
	}

	return &HostInfo{host, port, r.BnsName}, nil
}
