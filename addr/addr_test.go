// 统一封装服务访问地址获取。比如redis、mysql等访问是需要
// 目前支持设置hosts、bns两种方式访问，后面有需求时支持bns
package addr

import (
	"fmt"
	"testing"
)

func TestGetAddr(t *testing.T) {
	hosts := []string{"***************:8980"}
	ha, err := NewAddrByHost(hosts)
	if err != nil {
		fmt.Println(err)
		return
	}

	h, err := ha.GetAddr()
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(h)

	ba, err := NewAddrByBns("group.opera-default-XassetRadar-all-tc.xuper.all")
	if err != nil {
		fmt.Println(err)
		return
	}

	h, err = ba.GetAddr()
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(h)
}
