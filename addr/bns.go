package addr

import (
	"bytes"
	"fmt"
	"math/rand"
	"os/exec"
	"sync"
	"time"

	bns "icode.baidu.com/baidu/go-lib/bns/bns_client"
)

const (
	// cache有效期，单位s
	INSTANCE_CACHE_EXPIRE_S = 300
)

type bnsMapService struct {
	instances sync.Map
	flushTime sync.Map
}

var bnsMap *bnsMapService

func NewBnsService() *bnsMapService {
	if bnsMap == nil {
		bnsMap = &bnsMapService{}
	}

	return bnsMap
}

/* GetBnsInstances - get instance by bns service name
 *
 * Params:
 *     - service: name of service
 *     - options: options for get_instance_by_service, e.g., ["-i", "-a"]
 *
 * Returns:
 *   (instances, error)
 */
func GetBnsInstances(service string) ([]string, error) {
	// assemble params
	options := []string{"-i", "-p", "-s"}
	params := append(options, service)

	// get instance with cmd "get_instance_by_service"
	out, err := exec.Command("get_instance_by_service", params...).Output()
	if err != nil {
		return nil, err
	}

	// split instance names
	byteInstances := bytes.Split(out, []byte{'\n'})

	instances := make([]string, 0)
	for _, instance := range byteInstances {
		if len(instance) < 1 {
			continue
		}

		tmpArr := bytes.Split(bytes.Trim(instance, " "), []byte{' '})
		if len(tmpArr) != 4 {
			continue
		}

		if bytes.Equal(tmpArr[3], []byte{'0'}) {
			instances = append(instances, fmt.Sprintf("%s:%s", tmpArr[1], tmpArr[2]))
		}
	}

	return instances, nil
}

func (t *bnsMapService) GetAddrList(bnsName string) ([]string, error) {
	var err error
	serverList := []string{}

	// 检测cache有效期
	t.checkFlushCache(bnsName)

	// 检测cahce是否存在
	value, isExist := t.instances.Load(bnsName)
	if isExist {
		list, ok := value.([]string)
		if ok {
			serverList = list
		}
	}

	if !isExist || len(serverList) < 1 {
		serverList, err = GetBnsInstances(bnsName)
		if err != nil {
			serverList, err = bns.GetAddr(bns.NewClient(), bnsName)
		}
		if err != nil {
			return nil, err
		}
		if len(serverList) < 1 {
			return nil, fmt.Errorf("this bns no instance.bns:%s", bnsName)
		}

		t.instances.Store(bnsName, serverList)
	}

	if len(serverList) < 1 {
		return nil, fmt.Errorf("don't have available instance.bns:%s", bnsName)
	}

	return serverList, nil
}

func (t *bnsMapService) GetInstance(bnsName string) (string, error) {
	serverList, err := t.GetAddrList(bnsName)
	if err != nil {
		return "", err
	}

	rand.Seed(time.Now().UnixNano())
	r := rand.Uint32()
	index := r % uint32(len(serverList))
	server := serverList[int(index)]
	return server, nil
}

// 业务发现异常示例后调用刷新chche，及时剔除掉已下线实例
func (t *bnsMapService) FlushInstance(bnsName string) {
	t.instances.Delete(bnsName)
}

// 防止bns新增实例后业务无感知，cache过去后会被自动刷新
func (t *bnsMapService) checkFlushCache(bnsName string) {
	value, isExist := t.flushTime.Load(bnsName)
	if isExist {
		t, ok := value.(time.Time)
		if ok && time.Now().Unix()-t.Unix() < INSTANCE_CACHE_EXPIRE_S {
			// 有效
			return
		}
	}

	// 无效或者过期
	t.FlushInstance(bnsName)
	t.flushTime.Store(bnsName, time.Now())
}
