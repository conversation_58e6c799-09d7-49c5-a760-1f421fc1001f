package addr

import (
	"fmt"
	"testing"
	"time"
)

func TestGetBnsInstances(t *testing.T) {
	list, err := GetBnsInstances("group.opera-default-XassetRadar-all-tc.xuper.all")
	fmt.Println(list, err)
}

func TestGetAddrList(t *testing.T) {
	h, e := NewBnsService().GetAddrList("group.opera-default-XassetRadar-all-tc.xuper.all")
	if e != nil {
		fmt.Println(e)
		return
	}

	for i := 0; i < 60; i++ {
		go func() {
			h, _ := NewBnsService().GetAddrList("group.opera-default-XassetRadar-all-tc.xuper.all")
			fmt.Println(h)
			a, _ := NewBnsService().GetInstance("group.opera-default-XassetRadar-all-tc.xuper.all")
			fmt.Println(a)
		}()

		time.Sleep(1 * time.Second)
	}

	time.Sleep(1 * time.Second)
	fmt.Println(h)
}

func TestGetInstance(t *testing.T) {
	h, e := NewBnsService().GetInstance("group.opera-default-XassetRadar-all-tc.xuper.all")
	if e != nil {
		fmt.Println(e)
		return
	}

	fmt.Println(h)
}

func TestFlushInstance(t *testing.T) {
	bns := "group.opera-default-XassetRadar-all-tc.xuper.all"
	go func() {
		h, e := NewBnsService().GetInstance(bns)
		if e != nil {
			fmt.Println(e)
			return
		}

		fmt.Println(h)
	}()

	go func() {
		h, e := NewBnsService().GetInstance(bns)
		if e != nil {
			fmt.Println(e)
			return
		}

		fmt.Println(h)
	}()

	//go NewBnsService().FlushInstance(bns)

	go func() {
		h, e := NewBnsService().GetInstance(bns)
		if e != nil {
			fmt.Println(e)
			return
		}

		fmt.Println(h)
	}()

	NewBnsService().FlushInstance(bns)

	time.Sleep(3 * time.Second)
}
