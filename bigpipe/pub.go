package bigpipe

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"net/url"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type BigpilePubConf struct {
	User  string
	Topic string
	Token string
}

type BigpipePubCli struct {
	addr    *addr.Addr
	pubConf *BigpilePubConf
}

// {"topic_msgid":152,"pipelet":1,"status":0}
type PubMsgResp struct {
	TopicMsgId int64 `json:"topic_msgid"`
	Pipelet    int   `json:"pipelet"`
	Status     int   `json:"status"`
}

func NewBigpipePubCli(adr *addr.Addr, cfg *BigpilePubConf) *BigpipePubCli {
	return &BigpipePubCli{adr, cfg}
}

func (t *BigpipePubCli) PubMsg(msg string) (*PubMsgResp, *RequestRes, error) {
	if err := t.init(); err != nil {
		return nil, nil, err
	}
	if msg == "" {
		return nil, nil, fmt.Errorf("param error")
	}

	v := url.Values{}
	expires := time.Now().Unix() + int64(60*5)
	v.Set("expires", fmt.Sprintf("%d", expires))
	v.Set("username", t.pubConf.User)
	v.Set("sign", t.sign(expires))
	v.Set("method", "publish")
	v.Set("assigntype", "2")
	v.Set("msg_guid", fmt.Sprintf("%d", expires))
	v.Set("message", msg)
	body := v.Encode()

	uri := fmt.Sprintf("/rest/pipe/%s", t.pubConf.Topic)
	reqRes, err := t.doRequest(uri, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request bigpipe pub msg fail.err:%v", err)
	}

	var pubRes PubMsgResp
	err = json.Unmarshal([]byte(reqRes.Body), &pubRes)
	if err != nil {
		return nil, nil, fmt.Errorf("unmarshal bigpipe resp body fail.err:%v body:%s", err, reqRes.Body)
	}

	return &pubRes, reqRes, nil
}

// post
func (t *BigpipePubCli) doRequest(api string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}

	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	header["Expect"] = ""
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request bigpipe fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode != 200 {
		return result, fmt.Errorf("request bigpipe http code != 200.url:%s http_code:%d, body:%s",
			reqUrl, resp.StatusCode, result.Body)
	}

	return result, nil
}

func (t *BigpipePubCli) sign(expires int64) string {
	signStr := fmt.Sprintf("%d%s", expires, t.pubConf.Token)
	h := md5.New()
	io.WriteString(h, signStr)
	return fmt.Sprintf("%x", h.Sum(nil))
}

func (t *BigpipePubCli) init() error {
	if t == nil || t.addr == nil || t.pubConf == nil {
		return fmt.Errorf("not init")
	}

	return nil
}
