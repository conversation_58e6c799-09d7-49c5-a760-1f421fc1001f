package bigpipe

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
)

func TestPubMsg(t *testing.T) {
	cli := NewBigpipePubCli(GetAddr(), GetPubConf())
	msg := `{"version":0,"msg":"{\"user_id\":1,\"asset_id\":2,\"shard_id\":3,\"app_id\":4}"}`
	res, resp, err := cli.PubMsg(msg)
	fmt.Println(res, resp, err)
}

func GetAddr() *addr.Addr {
	bns := "group.opera-yq02-bigpipeGW-all-yqdisk2.Bigpipe.all:http"
	a, _ := addr.NewAddrByBns(bns)
	return a
}

func GetPubConf() *BigpilePubConf {
	return &BigpilePubConf{
		User:  "xasset-topic",
		Topic: "xasset-baidu-grant-behavior",
		Token: "xxx",
	}
}
