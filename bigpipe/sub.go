package bigpipe

import (
	"encoding/json"
	"fmt"
	"net/url"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type BigpipeSubConf struct {
	Queue string
	Token string
}

type BigpipeSubCli struct {
	addr    *addr.Addr
	subConf *BigpipeSubConf
}

// {"messages":[{"data":"test-message-5","msgid":"xasset-queue|xasset-behavior|2|1|1"}],"status":0}
type MsgNode struct {
	Data  string `json:"data"`
	MsgId string `json:"msgid"`
}
type SubMsgResp struct {
	Messages []*MsgNode `json:"messages"`
	Status   int        `json:"status"`
}

// {"status":0}
type AckMsgResp struct {
	Status int `json:"status"`
}

func NewBigpipeSubCli(adr *addr.Addr, cfg *BigpipeSubConf) *BigpipeSubCli {
	return &BigpipeSubCli{adr, cfg}
}

func (t *BigpipeSubCli) SubMsg() (*SubMsgResp, *RequestRes, error) {
	if err := t.init(); err != nil {
		return nil, nil, err
	}

	// method=receive&token=$token
	v := url.Values{}
	v.Set("method", "receive")
	v.Set("token", t.subConf.Token)
	body := v.Encode()

	uri := fmt.Sprintf("/rest/queue/%s", t.subConf.Queue)
	reqRes, err := t.doRequest(uri, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request bigpipe sub msg fail.err:%v", err)
	}

	var subRes SubMsgResp
	err = json.Unmarshal([]byte(reqRes.Body), &subRes)
	if err != nil {
		return nil, nil, fmt.Errorf("unmarshal bigpipe resp body fail.err:%v body:%s", err, reqRes.Body)
	}

	return &subRes, reqRes, nil
}

func (t *BigpipeSubCli) AckMsg(msgId string) (*AckMsgResp, *RequestRes, error) {
	if err := t.init(); err != nil {
		return nil, nil, err
	}
	if msgId == "" {
		return nil, nil, fmt.Errorf("param error")
	}

	// method=ack&token=$token&msgid=$1
	v := url.Values{}
	v.Set("method", "ack")
	v.Set("token", t.subConf.Token)
	v.Set("msgid", msgId)
	body := v.Encode()

	uri := fmt.Sprintf("/rest/queue/%s", t.subConf.Queue)
	reqRes, err := t.doRequest(uri, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request bigpipe ack msg fail.err:%v", err)
	}

	var ackRes AckMsgResp
	err = json.Unmarshal([]byte(reqRes.Body), &ackRes)
	if err != nil {
		return nil, nil, fmt.Errorf("unmarshal bigpipe resp body fail.err:%v body:%s", err, reqRes.Body)
	}

	return &ackRes, reqRes, nil
}

// post
func (t *BigpipeSubCli) doRequest(api string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}

	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	//header["Expect"] = ""
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request bigpipe fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode != 200 {
		return result, fmt.Errorf("request bigpipe http code != 200.url:%s http_code:%d, body:%s",
			reqUrl, resp.StatusCode, result.Body)
	}

	return result, nil
}

func (t *BigpipeSubCli) init() error {
	if t == nil || t.addr == nil || t.subConf == nil {
		return fmt.Errorf("not init")
	}

	return nil
}
