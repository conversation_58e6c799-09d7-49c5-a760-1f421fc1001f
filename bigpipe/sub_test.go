package bigpipe

import (
	"fmt"
	"testing"
)

func TestBatchSubMsg(t *testing.T) {
	//for i := 0; i < 1000; i++ {
	//	TestSubMsg(t)
	//}
}

func TestSubMsg(t *testing.T) {
	cli := NewBigpipeSubCli(GetAddr(), GetSubConf())
	res, resp, err := cli.SubMsg()
	fmt.Println(res, resp, err)

	if err != nil || res == nil {
		return
	}

	if len(res.Messages) > 1 {
		ackRes, resp, err := cli.AckMsg(res.Messages[0].MsgId)
		fmt.Println(ackRes, resp, err)
	}
}

func GetSubConf() *BigpipeSubConf {
	return &BigpipeSubConf{
		Queue: "xasset-grant-sync-baiduapp-queue",
		Token: "xxx",
	}
}
