package audit

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type AuditClient struct {
	Host    string
	AppCode string
	Token   string
}

func NewAuditClient(host, appCode, token string) *AuditClient {
	return &AuditClient{
		Host:    host,
		AppCode: appCode,
		Token:   token,
	}
}

// Submit 提交审核
//  id: 资源唯一ID
//  url: 图片链接
//  ext: 扩展信息，透传字段
func (t *AuditClient) Submit(id, url, ext string) error {
	if len(id) == 0 || len(url) == 0 {
		return fmt.Errorf("empty param")
	}

	param := submitParam{
		AppCode:      t.AppCode,
		ResourceId:   id,
		ResourceUrl:  url,
		Base64Data:   "",
		ResourceType: "IMAGE",
		ExtraInfo:    ext,
		AuditInfo: auditInfo{
			Plat:   AuditPlat,
			Labels: VICLables,
		},
	}

	body, _ := json.Marshal(param)
	resp, err := t.doPost(apiSubmit, nil, string(body))
	if err != nil {
		return err
	}

	return parseSubmitResp(resp)
}

func parseSubmitResp(raw *httpclient.HttpResponse) error {
	if raw.StatusCode != http.StatusOK {
		if len(raw.Body) == 0 {
			return fmt.Errorf("got empty body from accessToken, status code: %d", raw.StatusCode)
		}
		resp := struct {
			Code      string `json:"code"`
			Message   string `json:"message"`
			RequestId string `json:"requestId"`
		}{}
		if err := json.Unmarshal(raw.Body, &resp); err != nil {
			return fmt.Errorf("failed to parse err body of audit.submit, err: %v, body: [%s]",
				err, string(raw.Body))
		}
		return fmt.Errorf("failed to submit audit task, http: %d, Code: %s, Msg: %s",
			raw.StatusCode, resp.Code, resp.Message)
	}

	return nil
}

// Query deprecated
func (t *AuditClient) Query(resourceIds []string) ([]AuditQueryResp, error) {
	if len(resourceIds) == 0 {
		return nil, fmt.Errorf("empty param")
	}

	query := map[string]string{
		"appCode":     t.AppCode,
		"resourceIds": strings.Join(resourceIds, ","),
	}

	resp, err := t.doGet(apiQuery, query)
	if err != nil {
		return nil, err
	}

	return parseQueryResp(resp)
}

func parseQueryResp(raw *httpclient.HttpResponse) ([]AuditQueryResp, error) {
	if raw.StatusCode != http.StatusOK {
		if len(raw.Body) == 0 {
			return nil, fmt.Errorf("got empty body from accessToken, status code: %d", raw.StatusCode)
		}
		resp := struct {
			Code      string `json:"code"`
			Message   string `json:"message"`
			RequestId string `json:"requestId"`
		}{}
		if err := json.Unmarshal(raw.Body, &resp); err != nil {
			return nil, fmt.Errorf("failed to parse err body of audit.detail, err: %v, body: [%s]",
				err, string(raw.Body))
		}
		return nil, fmt.Errorf("failed to query audit detail, http: %d, Code: %s, Msg: %s",
			raw.StatusCode, resp.Code, resp.Message)
	}

	resp := struct {
		Data []AuditQueryResp `json:"data"`
	}{}
	if err := json.Unmarshal(raw.Body, &resp); err != nil {
		return nil, fmt.Errorf("failed to parse body, err: %v, body: [%s]", err, string(raw.Body))
	}

	return resp.Data, nil
}

// doGet http get
func (t *AuditClient) doGet(api string, param map[string]string) (*httpclient.HttpResponse, error) {
	reqUrl := fmt.Sprintf("https://%s%s", t.Host, api)
	url, err := url.Parse(reqUrl)
	if err != nil {
		return nil, fmt.Errorf("invalid url: %s, err: %v", reqUrl, err)
	}
	query := url.Query()
	for k, v := range param {
		query.Set(k, v)
	}
	url.RawQuery = query.Encode()

	header := map[string]string{
		"Content-Type":  "application/json",
		"x-audit-token": t.Token,
	}
	resp, err := httpclient.Get(url.String(), header, ReqConnTimeoutMs, ReqRWTimeoutMs, nil)
	if err != nil {
		return nil, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}

	return &resp, nil
}

// doPost http post
func (t *AuditClient) doPost(api string, param map[string]string, data string, timeout ...time.Duration) (*httpclient.HttpResponse, error) {
	reqUrl := fmt.Sprintf("https://%s%s", t.Host, api)
	url, err := url.Parse(reqUrl)
	if err != nil {
		return nil, fmt.Errorf("invalid url: %s, err: %v", reqUrl, err)
	}
	query := url.Query()
	for k, v := range param {
		query.Set(k, v)
	}
	url.RawQuery = query.Encode()

	header := map[string]string{
		"Content-Type":  "application/json",
		"x-audit-token": t.Token,
	}
	var reqTimeout time.Duration = ReqRWTimeoutMs
	if len(timeout) > 0 {
		reqTimeout = timeout[0]
	}
	resp, err := httpclient.Post(url.String(), header, ReqConnTimeoutMs, reqTimeout, data, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to request ernie url:%s err:%v", reqUrl, err)
	}

	return &resp, nil
}
