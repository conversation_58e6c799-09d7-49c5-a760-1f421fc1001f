package audit

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000
)

const (
	apiSubmit = "/api/v1/audit/submit"
	apiQuery  = "/api/v1/audit/detail"
)

const (
	AuditPlat = "VIC"
)

type submitParam struct {
	AppCode      string    `json:"appCode"`               // 应用code
	ResourceId   string    `json:"resourceId"`            // 业务方进审物料唯一标识
	ResourceUrl  string    `json:"resourceUrl,omitempty"` // 业务方进审物料url，如果不传url，就需要传base64
	Base64Data   string    `json:"base64Data,omitempty"`  // 业务方进审物料的base64数据，如果不传base64，就需要传url
	ResourceType string    `json:"resourceType"`          // 进审的物料类型，参考【物料类型列表】
	ExtraInfo    string    `json:"extraInfo"`             // 透传字段
	AuditInfo    auditInfo `json:"auditInfo"`
}

type auditInfo struct {
	Plat   string   `json:"plat"`   // 使用算子审核平台，参考【算子审核平台】
	Labels []string `json:"labels"` // label字段不同审核平台有不同的体系，vic和eop填写的是算子列表，而vcr填的是模板id
}

type AuditQueryResp struct {
	ResourceId   string `json:"resourceId"`
	ResourceType string `json:"resourceType"`
	Status       string `json:"status"`
	AuditResullt string `json:"auditResullt"`
}
