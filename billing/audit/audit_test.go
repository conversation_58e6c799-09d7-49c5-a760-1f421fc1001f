package audit

import (
	"fmt"
	"testing"
)

const (
	testHost    = "risk-audit-service.dev.weiyun.baidu.com"
	testAppCode = "xuperasset"
	testToken   = ""
)

func TestSubmit(t *testing.T) {
	url := "https://pics2.baidu.com/feed/43a7d933c895d143805252b68b7cc10e5baf0793.jpeg"

	cli := NewAuditClient(testHost, testAppCode, testToken)
	err := cli.Submit("resouce111", url, "")
	if err != nil {
		t.Fatal(err)
	}
}

func TestQuery(t *testing.T) {
	cli := NewAuditClient(testHost, testAppCode, testToken)
	resp, err := cli.Query([]string{"resouce111"})
	if err != nil {
		t.Fatal(err)
	}

	fmt.Print(resp)
}
