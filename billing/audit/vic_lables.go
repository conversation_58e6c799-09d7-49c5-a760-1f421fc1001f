package audit

import "fmt"

const (
	pornLable     = "FEATURE_VIS_IMG_PORNFILTER_GPU_V18_A10"
	politicsLable = "FEATURE_VIS_IMG_POLITICS_GPU_V2_A10"

	VicPass   = "pass"
	VIcReview = "review"
	VicBlock  = "block"
)

var (
	VICLables = []string{pornLable, politicsLable}
)

type VicAuditResult struct {
	Result []struct {
		Probability float64 `json:"probability"`
		ClassName   string  `json:"class_name"`
	} `json:"result"`
	Conclusion string `json:"conclusion"`
}

// VicAuditPass 判断图片审查结果
//  不同的算子判断标准可能不同
func VicAuditPass(lable string, result *VicAuditResult) error {
	switch lable {
	case pornLable:
		return vicPornPass(result)
	case politicsLable:
		return vicPoliticsPass(result)
	}

	return fmt.Errorf("unsupport vic lable: %s", lable)
}

func vicPornPass(result *VicAuditResult) error {
	if result.Conclusion != VicPass {
		return fmt.Errorf("expected: %s, but got: %s, details: %+v", VicPass, result.Conclusion, result.Result)
	}

	return nil
}

func vicPoliticsPass(result *VicAuditResult) error {
	// 目前判断标准一致
	return vicPornPass(result)
}
