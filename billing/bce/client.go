package bce

import (
	"bytes"
	"fmt"
	"io"
	"io/ioutil"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/http"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/util"
)

type Client struct {
	*BceClientConfiguration
	Signer auth.Signer // the sign algorithm
}

func NewClient(conf *BceClientConfiguration) *Client {
	return &Client{
		BceClientConfiguration: conf,
		Signer:                 &auth.BceV1Signer{},
	}
}

func (c *Client) buildHttpRequest(request *BceRequest) {
	// Construct the http request instance for the special fields
	request.BuildHttpRequest()

	// Set the client specific configurations
	request.SetEndpoint(c.Endpoint)
	if request.Protocol() == "" {
		request.SetProtocol(DEFAULT_PROTOCOL)
	}
	if c.Timeout == 0 {
		request.SetTimeout(DEFAULT_CONNECTION_TIMEOUT)
	} else {
		request.SetTimeout(c.Timeout)
	}

	// Set the BCE request headers
	request.SetHeader(http.HOST, request.Host())
	request.SetHeader("X-Subuser-Support", "true")
	request.SetHeader(http.BCE_DATE, util.FormatISO8601Date(util.NowUTCSeconds()))
	credentials := request.Credentials()
	if credentials == nil {
		credentials = c.Credentials
	}
	if credentials != nil {
		c.Signer.Sign(&request.Request, credentials, c.SignOption)
	}
}

func (c *Client) SendRequest(req *BceRequest, resp *BceResponse) error {
	// Return client error if it is not nil
	if req.ClientError() != nil {
		return req.ClientError()
	}

	// Build the http request and prepare to send
	c.buildHttpRequest(req)

	// Send request with the given retry policy
	retries := 0
	if req.Body() != nil {
		defer req.Body().Close() // Manually close the ReadCloser body for retry
	}

	for {
		// The request body should be temporarily saved if retry to send the http request
		var retryBuf bytes.Buffer
		var teeReader io.Reader
		if req.Body() != nil {
			teeReader = io.TeeReader(req.Body(), &retryBuf)
			req.Request.SetBody(ioutil.NopCloser(teeReader))
		}
		httpResp, err := http.Execute(&req.Request)
		if err != nil {
			if c.Retry.ShouldRetry(err, retries) {
				delay_in_mills := c.Retry.GetDelayBeforeNextRetryInMillis(err, retries)
				time.Sleep(delay_in_mills)
			} else {
				return &BceClientError{
					fmt.Sprintf("execute http request failed! Retried %d times, error: %v",
						retries, err)}
			}
			retries++
			if req.Body() != nil {
				ioutil.ReadAll(teeReader)
				req.Request.SetBody(ioutil.NopCloser(&retryBuf))
			}
			continue
		}
		resp.SetRequestId(req.Header(http.BCE_REQUEST_ID))
		resp.SetHttpResponse(httpResp)
		resp.ParseResponse()

		if c.Logger != nil {
			c.Logger.Log("%-4s %s%s [status:%d] [elapse:%s] [requestId:%s]",
				req.Method(), req.Host(), req.Uri(), resp.StatusCode(), resp.ElapsedTime().String(), resp.requestId)
		}

		if resp.IsFail() {
			err := resp.ServiceError()
			if c.Retry.ShouldRetry(err, retries) {
				delay_in_mills := c.Retry.GetDelayBeforeNextRetryInMillis(err, retries)
				time.Sleep(delay_in_mills)
			} else {
				return err
			}
			retries++
			if req.Body() != nil {
				ioutil.ReadAll(teeReader)
				req.Request.SetBody(ioutil.NopCloser(&retryBuf))
			}
			continue
		}
		return nil
	}
}
