package bce

import (
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/util"
)

// Constants and default values for the package bce
const (
	DEFAULT_PROTOCOL           = "http"
	DEFAULT_CONTENT_TYPE       = "application/json;charset=utf-8"
	DEFAULT_CONNECTION_TIMEOUT = 15 * 1000
)

var (
	DEFAULT_USER_AGENT   string
	DEFAULT_RETRY_POLICY = NewBackOffRetryPolicy(3, 20000, 300)
)

func init() {
}

// BaseClientConfiguration defines the config components structure.
type BceClientConfiguration struct {
	Endpoint    string
	Credentials *auth.BceCredentials
	SignOption  *auth.SignOptions
	Retry       RetryPolicy
	Timeout     int
	Logger      util.Logger
}
