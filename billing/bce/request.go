package bce

import (
	"bytes"
	"fmt"
	"io"
	"io/ioutil"
	"os"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/http"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/util"
)

// Body defines the data structure used in BCE request.
// Every BCE request that sets the body field must set its content-length and content-md5 headers
// to ensure the correctness of the body content forcely, and users can also set the content-sha256
// header to strengthen the correctness with the "SetHeader" method.
type Body struct {
	stream     io.ReadCloser
	size       int64
	contentMD5 string
}

func (b *Body) Stream() io.ReadCloser { return b.stream }

func (b *Body) SetStream(stream io.ReadCloser) { b.stream = stream }

func (b *Body) Size() int64 { return b.size }

func (b *Body) ContentMD5() string { return b.contentMD5 }

// NewBodyFromBytes - build a Body object from the byte stream to be used in the http request, it
// calculates the content-md5 of the byte stream and store the size as well as the stream.
//
// PARAMS:
//     - stream: byte stream
// RETURNS:
//     - *Body: the return Body object
//     - error: error if any specific error occurs
func NewBodyFromBytes(stream []byte) (*Body, error) {
	buf := bytes.NewBuffer(stream)
	size := int64(buf.Len())
	contentMD5, err := util.CalculateContentMD5(buf, size)
	if err != nil {
		return nil, err
	}
	buf = bytes.NewBuffer(stream)
	return &Body{ioutil.NopCloser(buf), size, contentMD5}, nil
}

// NewBodyFromString - build a Body object from the string to be used in the http request, it
// calculates the content-md5 of the byte stream and store the size as well as the stream.
//
// PARAMS:
//     - str: the input string
// RETURNS:
//     - *Body: the return Body object
//     - error: error if any specific error occurs
func NewBodyFromString(str string) (*Body, error) {
	buf := bytes.NewBufferString(str)
	size := int64(len(str))
	contentMD5, err := util.CalculateContentMD5(buf, size)
	if err != nil {
		return nil, err
	}
	buf = bytes.NewBufferString(str)
	return &Body{ioutil.NopCloser(buf), size, contentMD5}, nil
}

// NewBodyFromFile - build a Body object from the given file name to be used in the http request,
// it calculates the content-md5 of the byte stream and store the size as well as the stream.
//
// PARAMS:
//     - fname: the given file name
// RETURNS:
//     - *Body: the return Body object
//     - error: error if any specific error occurs
func NewBodyFromFile(fname string) (*Body, error) {
	file, err := os.Open(fname)
	if err != nil {
		return nil, err
	}
	fileInfo, infoErr := file.Stat()
	if infoErr != nil {
		return nil, infoErr
	}
	contentMD5, md5Err := util.CalculateContentMD5(file, fileInfo.Size())
	if md5Err != nil {
		return nil, md5Err
	}
	if _, err = file.Seek(0, 0); err != nil {
		return nil, err
	}
	return &Body{file, fileInfo.Size(), contentMD5}, nil
}

// NewBodyFromSectionFile - build a Body object from the given file pointer with offset and size.
// It calculates the content-md5 of the given content and store the size as well as the stream.
//
// PARAMS:
//     - file: the input file pointer
//     - off: offset of current section body
//     - size: current section body size
// RETURNS:
//     - *Body: the return Body object
//     - error: error if any specific error occurs
func NewBodyFromSectionFile(file *os.File, off, size int64) (*Body, error) {
	if _, err := file.Seek(off, 0); err != nil {
		return nil, err
	}
	contentMD5, md5Err := util.CalculateContentMD5(file, size)
	if md5Err != nil {
		return nil, md5Err
	}
	if _, err := file.Seek(0, 0); err != nil {
		return nil, err
	}
	section := io.NewSectionReader(file, off, size)
	return &Body{ioutil.NopCloser(section), size, contentMD5}, nil
}

// NewBodyFromSizedReader - build a Body object from the given reader with size.
// It calculates the content-md5 of the given content and store the size as well as the stream.
//
// PARAMS:
//     - r: the input reader
//     - size: the size to be read from the input reader which must be <= reader size
// RETURNS:
//     - *Body: the return Body object
//     - error: error if any specific error occurs
func NewBodyFromSizedReader(r io.Reader, size int64) (*Body, error) {
	var err error
	var buf1, buf2 bytes.Buffer
	tee := io.TeeReader(r, &buf1)
	readerSize, err := io.Copy(&buf2, tee)
	if err != nil {
		return nil, err
	}
	if readerSize < size {
		return nil, NewBceClientError("given size can't be bigger than the reader actual size")
	}
	contentMD5 := ""
	if size >= 0 {
		contentMD5, err = util.CalculateContentMD5(&buf1, size)
	}
	if err != nil {
		return nil, err
	}
	stream := io.LimitReader(&buf2, size)
	return &Body{ioutil.NopCloser(stream), size, contentMD5}, nil
}

// BceRequest defines the request structure for accessing BCE services
type BceRequest struct {
	http.Request
	requestId   string
	tokenId     string
	clientError *BceClientError
	credentials *auth.BceCredentials
}

func (b *BceRequest) RequestId() string { return b.requestId }

func (b *BceRequest) SetRequestId(val string) { b.requestId = val }

func (b *BceRequest) SetCredentials(val *auth.BceCredentials) { b.credentials = val }

func (b *BceRequest) Credentials() *auth.BceCredentials { return b.credentials }

func (b *BceRequest) SetTokenId(val string) { b.tokenId = val }

func (b *BceRequest) ClientError() *BceClientError { return b.clientError }

func (b *BceRequest) SetClientError(err *BceClientError) { b.clientError = err }

func (b *BceRequest) SetBody(body *Body) { // override SetBody derived from http.Request
	b.Request.SetBody(body.Stream())
	b.SetLength(body.Size()) // set field of "net/http.Request.ContentLength"
	if body.Size() > 0 {
		b.SetHeader(http.CONTENT_MD5, body.ContentMD5())
		b.SetHeader(http.CONTENT_LENGTH, fmt.Sprintf("%d", body.Size()))
	}
}

func (b *BceRequest) BuildHttpRequest() {
	// Only need to build the specific `requestId` field for BCE, other fields are same as the
	// `http.Request` as well as its methods.
	if len(b.requestId) == 0 {
		// Construct the request ID with UUID
		b.requestId = util.NewRequestId()
	}
	if len(b.tokenId) != 0 {
		// Construct the request ID with UUID
		b.SetHeader(http.AUTH_TOKEN, b.tokenId)
	}
	b.SetHeader(http.BCE_REQUEST_ID, b.requestId)
}

func (b *BceRequest) String() string {
	requestIdStr := "requestId=" + b.requestId
	if b.clientError != nil {
		return requestIdStr + ", client error: " + b.ClientError().Error()
	}
	return requestIdStr + "\n" + b.Request.String()
}
