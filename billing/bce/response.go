package bce

import (
	"bytes"
	"encoding/json"
	"io"
	"io/ioutil"
	"strings"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/http"
)

// BceResponse defines the response structure for receiving BCE services response.
type BceResponse struct {
	statusCode   int
	statusText   string
	requestId    string
	debugId      string
	response     *http.Response
	serviceError *BceServiceError
}

func (r *BceResponse) IsFail() bool {
	return r.response.StatusCode() >= 400
}

func (r *BceResponse) StatusCode() int {
	return r.statusCode
}

func (r *BceResponse) StatusText() string {
	return r.statusText
}
func (r *BceResponse) SetRequestId(requestId string) {
	r.requestId = requestId
}

func (r *BceResponse) RequestId() string {
	return r.requestId
}

func (r *BceResponse) DebugId() string {
	return r.debugId
}

func (r *BceResponse) Header(key string) string {
	return r.response.GetHeader(key)
}

func (r *BceResponse) Headers() map[string]string {
	return r.response.GetHeaders()
}

func (r *BceResponse) Body() io.ReadCloser {
	return r.response.Body()
}

func (r *BceResponse) SetHttpResponse(response *http.Response) {
	r.response = response
}

func (r *BceResponse) ElapsedTime() time.Duration {
	return r.response.ElapsedTime()
}

func (r *BceResponse) ServiceError() *BceServiceError {
	return r.serviceError
}

func (r *BceResponse) ParseResponse() {
	r.statusCode = r.response.StatusCode()
	r.statusText = r.response.StatusText()
	if r.IsFail() {
		r.serviceError = NewBceServiceError("", r.statusText, r.requestId, r.statusCode)

		// First try to read the error `Code' and `Message' from body
		rawBody, _ := ioutil.ReadAll(r.Body())
		defer r.Body().Close()
		if len(rawBody) != 0 {
			jsonDecoder := json.NewDecoder(bytes.NewBuffer(rawBody))
			if err := jsonDecoder.Decode(r.serviceError); err != nil {
				r.serviceError = NewBceServiceError(
					EMALFORMED_JSON,
					"Service json error message decode failed",
					r.requestId,
					r.statusCode)
			}
			return
		}

		// Then guess the `Message' from by the return status code
		switch r.statusCode {
		case 400:
			r.serviceError.Code = EINVALID_HTTP_REQUEST
		case 403:
			r.serviceError.Code = EACCESS_DENIED
		case 412:
			r.serviceError.Code = EPRECONDITION_FAILED
		case 500:
			r.serviceError.Code = EINTERNAL_ERROR
		default:
			words := strings.Split(r.statusText, " ")
			r.serviceError.Code = strings.Join(words[1:], "")
		}
	}
}

func (r *BceResponse) ParseJsonBody(result interface{}) error {
	defer r.Body().Close()
	jsonDecoder := json.NewDecoder(r.Body())
	return jsonDecoder.Decode(result)
}
