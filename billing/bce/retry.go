package bce

import (
	"net"
	"net/http"
	"time"
)

// RetryPolicy defines the two methods to retry for sending request.
type RetryPolicy interface {
	ShouldRetry(BceError, int) bool
	GetDelayBeforeNextRetryInMillis(BceError, int) time.Duration
}

// NoRetryPolicy just does not retry.
type NoRetryPolicy struct{}

func (*NoRetryPolicy) ShouldRetry(err BceError, attempts int) bool {
	return false
}

func (*NoRetryPolicy) GetDelayBeforeNextRetryInMillis(
	err BceError, attempts int) time.Duration {
	return 0 * time.Millisecond
}

func NewNoRetryPolicy() *NoRetryPolicy {
	return &NoRetryPolicy{}
}

// BackOffRetryPolicy implements a policy that retries with exponential back-off strategy.
// This policy will keep retrying until the maximum number of retries is reached. The delay time
// will be a fixed interval for the first time then 2 * interval for the second, 4 * internal for
// the third, and so on.
// In general, the delay time will be 2^number_of_retries_attempted*interval. When a maximum of
// delay time is specified, the delay time will never exceed this limit.
type BackOffRetryPolicy struct {
	maxErrorRetry        int
	maxDelayInMillis     int64
	baseIntervalInMillis int64
}

func (b *BackOffRetryPolicy) ShouldRetry(err BceError, attempts int) bool {
	// Do not retry any more when retry the max times
	if attempts >= b.maxErrorRetry {
		return false
	}

	// Always retry on IO error
	if _, ok := err.(net.Error); ok {
		return true
	}

	// Only retry on a service error
	if realErr, ok := err.(*BceServiceError); ok {
		switch realErr.StatusCode {
		case http.StatusInternalServerError:
			return true
		case http.StatusBadGateway:
			return true
		case http.StatusServiceUnavailable:
			return true
		case http.StatusBadRequest:
			if realErr.Code != "Http400" {
				return false
			}
			return true
		}

		if realErr.Code == EREQUEST_EXPIRED {
			return true
		}
	}
	return false
}

func (b *BackOffRetryPolicy) GetDelayBeforeNextRetryInMillis(
	err BceError, attempts int) time.Duration {
	if attempts < 0 {
		return 0 * time.Millisecond
	}
	delayInMillis := (1 << uint64(attempts)) * b.baseIntervalInMillis
	if delayInMillis > b.maxDelayInMillis {
		return time.Duration(b.maxDelayInMillis) * time.Millisecond
	}
	return time.Duration(delayInMillis) * time.Millisecond
}

func NewBackOffRetryPolicy(maxRetry int, maxDelay, base int64) *BackOffRetryPolicy {
	return &BackOffRetryPolicy{maxRetry, maxDelay, base}
}
