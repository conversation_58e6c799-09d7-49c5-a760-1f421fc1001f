package billchargesrv

import (
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/util"
)

const (
	QueryUsageDataApi = "/v1/resourceusage/usage/data/trail"
)

type BillChargeSrvClientConfiguration struct {
	Endpoint    string
	Retry       bce.RetryPolicy
	SignOption  *auth.SignOptions
	Credentials auth.BceCredentials
	Timeout     int
	Logger      util.Logger
}

func (c *BillChargeSrvClientConfiguration) Valid() error {
	if len(c.Endpoint) == 0 {
		return fmt.Errorf("missing endpoint")
	}
	if len(c.Credentials.AccessKeyId) == 0 ||
		len(c.Credentials.SecretAccessKey) == 0 {
		return fmt.Errorf("invalid credentials")
	}
	return nil
}

type QueryUsageDataParam struct {
	AccountId   string    `json:"accountId"`
	InstanceId  string    `json:"instanceId"`
	ServiceName string    `json:"serviceName"`
	Region      string    `json:"region"`
	ChargeItem  string    `json:"chargeItem"`
	StartTime   string    `json:"startTime"`
	EndTime     string    `json:"endTime"`
	UsageMeta   UsageMeta `json:"usageMeta"`
}

type UsageMeta struct {
	MinuteReducer string `json:"minuteReducer"`
	DayReducer    string `json:"dayReducer"`
	MonthReducer  string `json:"monthReducer"`
	RemoveZero    string `json:"removeZero"`
}

type UsageData struct {
	Amount   int64    `json:"amount"`
	Unit     string   `json:"unit"`
	TimeSpan TimeSpan `json:"timeSpan"`
}

type TimeSpan struct {
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
}

type QueryUsageDataResp []UsageData
