package bus

import (
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bus/model"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/http"
)

type Client struct {
	*bce.Client
}

func NewClient(conf *BusClientConfiguration) (*Client, error) {
	if err := conf.Valid(); err != nil {
		return nil, fmt.Errorf("BusClientConfiguration invalid, error: %v", err)
	}
	if conf.SignOption == nil {
		conf.SignOption = &auth.SignOptions{
			HeadersToSign: auth.DEFAULT_HEADERS_TO_SIGN,
			ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS,
			IsBce:         true,
		}
	}
	if conf.Retry == nil {
		conf.Retry = bce.DEFAULT_RETRY_POLICY
	}
	bceConf := bce.BceClientConfiguration{
		Endpoint:    conf.Endpoint,
		Credentials: &conf.Credentials,
		SignOption:  conf.SignOption,
		Retry:       conf.Retry,
		Timeout:     conf.Timeout,
		Logger:      conf.Logger,
	}
	bceClient := bce.NewClient(&bceConf)
	client := Client{
		Client: bceClient,
	}
	return &client, nil
}

// Query 查询已注册的 endpoint
//  @serviceId: 产品线服务名，比如 XUPERASSET
//  @region: 可选，默认global
//  @living: 是否在线。目前请填 true
func (c *Client) Query(serviceId, region string, living bool) ([]model.BceService, error) {
	if len(serviceId) == 0 {
		return nil, errors.New("param error")
	}
	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri(BusCommonApi)
	bceRequest.SetMethod(http.GET)
	bceRequest.SetParam("type", "console")
	bceRequest.SetParam("serviceId", serviceId)
	if len(region) > 0 {
		bceRequest.SetParam("region", region)
	}
	if living {
		bceRequest.SetParam("livingStatus", "0")
	} else {
		bceRequest.SetParam("livingStatus", "1")
	}
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()

	result := new(model.BusQueryResult)
	if err := resp.ParseJsonBody(result); err != nil {
		return nil, err
	}
	return result.BceServices, nil
}

// Register 注册 endpoint
//  注册操作支持幂等性，region,endpoint,port三者为联合主键
//  @serviceId: 产品线服务名，比如 XUPERASSET
//  @region: 选填，后端不分region时不填
//  @endpoint: 域名或IP+端口，不支持BNS
func (c *Client) Register(serviceId, region, endpoint string) error {
	if len(serviceId) == 0 {
		return errors.New("param serviceId error")
	}
	if len(endpoint) == 0 {
		return errors.New("param endpoint error")
	}

	reqBody := map[string]string{
		"type":      "console",
		"serviceId": serviceId,
		"endpoint":  endpoint,
		"region":    region,
	}
	reqBodyStr, _ := json.Marshal(reqBody)
	body, err := bce.NewBodyFromBytes(reqBodyStr)
	if err != nil {
		return fmt.Errorf("new body from bytes error: %v", err)
	}

	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri(BusCommonApi)
	bceRequest.SetMethod(http.PUT)
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	bceRequest.SetBody(body)

	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return err
	}
	defer func() { resp.Body().Close() }()

	// 没有返回值

	return nil
}

// UnRegister 下线已注册的 endpoint
//  @serviceId: 产品线服务名，比如 XUPERASSET
//  @region: 选填，后端不分region时不填
//  @living: 是否在线，调用UnRegister会使endpoint下线
func (c *Client) UnRegister(serviceId, endpoint string) error {
	if len(serviceId) == 0 {
		return errors.New("param error")
	}
	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri(BusCommonApi)
	bceRequest.SetMethod(http.DELETE)
	bceRequest.SetParam("type", "console")
	bceRequest.SetParam("serviceId", serviceId)
	bceRequest.SetParam("endpoint", endpoint)

	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return err
	}
	defer func() { resp.Body().Close() }()

	// 没有返回值

	return nil
}
