package bus

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/iam"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/testings"
)

// TestQuery bus服务办公网无法访问，请找一台B区的机器，比如开发机
func TestQuery(t *testing.T) {
	iamConf := iam.IamClientConfiguration{
		Endpoint: "http://iam.bj.internal-qasandbox.baidu-int.com",
		UserName: "xuperasset2",
		Password: "SRPA4SEbWuGO5usSCklz9voaIq1krcyI",
		Domain:   "Default",
		Logger:   testings.NewLogger(),
	}
	iamCli := iam.NewClient(&iamConf)
	accessKey, err := iamCli.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}

	conf := BusClientConfiguration{
		Endpoint: "http://register.internal-qasandbox.baidu-int.com:8985",
		Credentials: auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
		Logger: testings.NewLogger(),
	}
	cli, err := NewClient(&conf)
	if err != nil {
		t.Fatal(err)
	}
	svcs, err := cli.Query("XUPERASSET", "", true)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println(testings.ToJSON(svcs))
}

func TestRegister(t *testing.T) {
	iamConf := iam.IamClientConfiguration{
		Endpoint: "http://iam.bj.internal-qasandbox.baidu-int.com",
		UserName: "xuperasset2",
		Password: "SRPA4SEbWuGO5usSCklz9voaIq1krcyI",
		Domain:   "Default",
	}
	iamCli := iam.NewClient(&iamConf)
	accessKey, err := iamCli.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}

	conf := BusClientConfiguration{
		Endpoint: "http://register.internal-qasandbox.baidu-int.com:8985",
		Credentials: auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
		Retry: bce.NewNoRetryPolicy(), // TODO: drop
	}
	cli, err := NewClient(&conf)
	if err != nil {
		t.Fatal(err)
	}
	if err = cli.Register("XUPERASSET", "", "njjs-bdchain00-node02.njjs:8300"); err != nil {
		t.Fatal(err)
	}
	fmt.Println("register succ.")
}

func TestUnRegister(t *testing.T) {
	iamConf := iam.IamClientConfiguration{
		Endpoint: "http://iam.bj.internal-qasandbox.baidu-int.com",
		UserName: "xuperasset2",
		Password: "SRPA4SEbWuGO5usSCklz9voaIq1krcyI",
		Domain:   "Default",
	}
	iamCli := iam.NewClient(&iamConf)
	accessKey, err := iamCli.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}

	conf := BusClientConfiguration{
		Endpoint: "http://register.internal-qasandbox.baidu-int.com:8985",
		Credentials: auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
	}
	cli, err := NewClient(&conf)
	if err != nil {
		t.Fatal(err)
	}
	if err = cli.UnRegister("XUPERASSET", "njjs-bdchain00-node02.njjs:8300"); err != nil {
		t.Fatal(err)
	}
	fmt.Println("succ")
}
