package cashier

import (
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/http"
)

type Client struct {
	*bce.Client
}

func NewClient(conf *ClientConfiguration) (*Client, error) {
	if err := conf.Valid(); err != nil {
		return nil, fmt.Errorf("ClientConfiguration invalid, error: %v", err)
	}
	if conf.SignOption == nil {
		conf.SignOption = &auth.SignOptions{
			HeadersToSign: auth.DEFAULT_HEADERS_TO_SIGN,
			ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS,
			IsBce:         true,
		}
	}
	if conf.Retry == nil {
		conf.Retry = bce.DEFAULT_RETRY_POLICY
	}
	bceConf := bce.BceClientConfiguration{
		Endpoint:    conf.Endpoint,
		Credentials: &conf.Credentials,
		SignOption:  conf.SignOption,
		Retry:       conf.Retry,
		Timeout:     conf.Timeout,
		Logger:      conf.Logger,
	}
	bceClient := bce.NewClient(&bceConf)
	client := Client{
		Client: bceClient,
	}

	return &client, nil
}

func (t *Client) GetConsolePayInfo(param *GetConsolePayInfoRequest) (*GetConsolePayInfoResp, error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetMethod(http.POST)
	bceRequest.SetUri(GetConsolePayInfoApi)
	bceRequest.BuildHttpRequest()
	bceRequest.SetHeader(http.HOST, bceRequest.Host())
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	bodyJson, err := json.Marshal(&param)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal GetConsolePayInfoRequest, err: %v", err)
	}
	b, err := bce.NewBodyFromBytes(bodyJson)
	if err != nil {
		return nil, err
	}
	bceRequest.SetBody(b)
	resp := &bce.BceResponse{}
	if err := t.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	statusResponse := new(GetConsolePayInfoResp)
	if err := resp.ParseJsonBody(statusResponse); err != nil {
		return nil, err
	}
	return statusResponse, nil
}
