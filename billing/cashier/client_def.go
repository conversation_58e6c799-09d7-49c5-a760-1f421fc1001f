package cashier

import (
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/util"
)

const (
	GetConsolePayInfoApi = "/v1/cashier/payment/console"
)

const (
	CurrencyRMB        = "RMB"
	CharsetUTF8        = "UTF-8"
	PayFundTypeCash    = "CASH"
	PayChannelSmartApp = "BAIDU_CASHIER_SA"
	PayChannelH5       = "BAIDU_CASHIER_H5"
)

type ClientConfiguration struct {
	Endpoint    string
	Retry       bce.RetryPolicy
	SignOption  *auth.SignOptions
	Credentials auth.BceCredentials
	Timeout     int
	Logger      util.Logger
}

func (c *ClientConfiguration) Valid() error {
	if len(c.Endpoint) == 0 {
		return fmt.Errorf("missing endpoint")
	}
	if len(c.Credentials.AccessKeyId) == 0 || len(c.Credentials.SecretAccessKey) == 0 {
		return fmt.Errorf("missing credentials")
	}
	return nil
}

type GetConsolePayInfoRequest struct {
	OrderId         string           `json:"orderId"`
	PayUserId       string           `json:"payUserId"`
	OrderDesc       string           `json:"orderDesc"`
	Currency        string           `json:"currency"`
	Charset         string           `json:"charset"`
	PayItems        []ConsolePayItem `json:"payItems"`
	FrontSuccessUrl string           `json:"frontSuccessUrl"`
	FrontFailureUrl string           `json:"frontFailureUrl"`
}

type ConsolePayItem struct {
	PayFundType string  `json:"payFundType"`
	PayChannel  string  `json:"payChannel"`
	PayAmount   float64 `json:"payAmount"` // todo bigdemical
}

type GetConsolePayInfoResp struct {
	OrderId string          `json:"orderId"`
	Status  string          `json:"status"`
	Details []ExecuteDetail `json:"details"`
}

type ExecuteDetail struct {
	Status     string                 `json:"status"`
	PayChannel string                 `json:"payChannel"`
	Charset    string                 `json:"charset"`
	Url        string                 `json:"url"`
	Method     string                 `json:"method"`
	Parameters map[string]interface{} `json:"parameters"`
}
