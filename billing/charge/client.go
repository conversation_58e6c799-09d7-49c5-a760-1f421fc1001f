package charge

import (
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/charge/model"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/http"
)

type Client struct {
	*bce.Client
}

func NewClient(conf *ChargeClientConfiguration) (*Client, error) {
	if err := conf.Valid(); err != nil {
		return nil, fmt.Errorf("ChargeClientConfiguration invalid, error: %v", err)
	}
	if conf.SignOption == nil {
		conf.SignOption = &auth.SignOptions{
			HeadersToSign: auth.DEFAULT_HEADERS_TO_SIGN,
			ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS,
			IsBce:         true,
		}
	}
	if conf.Retry == nil {
		conf.Retry = bce.DEFAULT_RETRY_POLICY
	}
	bceConf := bce.BceClientConfiguration{
		Endpoint:    conf.Endpoint,
		Credentials: &conf.Credentials,
		SignOption:  conf.SignOption,
		Retry:       conf.Retry,
		Timeout:     conf.Timeout,
		Logger:      conf.Logger,
	}
	bceClient := bce.NewClient(&bceConf)
	client := Client{
		Client: bceClient,
	}
	return &client, nil
}

func (c *Client) PushChargeData(request *model.ChargeRequest) error {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri(ChargeApi)
	bceRequest.SetMethod(http.POST)
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	dataByte, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("unmarshal data[%v] err: %v", request, err)
	}
	body, err := bce.NewBodyFromBytes(dataByte)
	if err != nil {
		return fmt.Errorf("NewBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return err
	}
	defer func() { resp.Body().Close() }()

	return nil
}
