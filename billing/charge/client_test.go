package charge

import (
	"fmt"
	"testing"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/charge/model"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/iam"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/testings"
)

func makeClient(t *testing.T) *Client {
	iamConf := iam.IamClientConfiguration{
		Endpoint: "http://iam.bj.internal-qasandbox.baidu-int.com",
		UserName: "xuperasset2",
		Password: "SRPA4SEbWuGO5usSCklz9voaIq1krcyI",
		Domain:   "Default",
	}
	iamCli := iam.NewClient(&iamConf)
	accessKey, err := iamCli.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}
	conf := ChargeClientConfiguration{
		Endpoint: "http://proxy.internal-qasandbox.bce-billing.baidu-int.com",
		Credentials: auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
	}
	cli, err := NewClient(&conf)
	if err != nil {
		t.Fatal(err)
	}

	return cli
}

func TestPushChargeData(t *testing.T) {
	cli := makeClient(t)

	req := model.ChargeRequest{
		UserChargeDatas: []model.UserChargeData{
			{
				Scope:  "BCE_XUPERASSET",
				UserId: "c875bb65d6b24184800364f27b1409ae",
				MsgId:  "c875bb65d6b24184800364f27b1409ae-2",
				MetricData: []model.MetricItem{
					{
						MetricName: "BDY-DJFWF-LS",
						Dimensions: []model.Dimension{
							{
								Name:  "region",
								Value: "global",
							},
							{
								Name:  "instanceID",
								Value: "",
							},
						},
						StatisticValues: model.StatisticItem{
							Sum:  10,
							Unit: "Count",
						},
						TimeStamp: uint64(time.Now().Unix()),
					},
				},
			},
		},
	}

	fmt.Println(testings.ToJSON(&req))

	if err := cli.PushChargeData(&req); err != nil {
		t.Fatal(err)
	}
}
