package model

type ChargeRequest struct {
	UserChargeDatas []UserChargeData `json:"userChargeDatas"`
}

type UserChargeData struct {
	Scope      string       `json:"scope"`      // BCE_产品名，必须加上BCE_前缀
	UserId     string       `json:"userId"`     // 用户accountId
	MsgId      string       `json:"msgId"`      // 去重消息msgid
	MetricData []MetricItem `json:"metricData"` // 用量数据，数组长度建议100以下
}

type MetricItem struct {
	MetricName      string        `json:"metricName"`      // 计费项（计费项系统服务名）
	Dimensions      []Dimension   `json:"dimensions"`      // 必须有region，实例产品有instanceID
	StatisticValues StatisticItem `json:"statisticValues"` // 用量
	TimeStamp       uint64        `json:"timestamp"`       // 秒，用量对应的时间点，推送延迟要1h内
	ChargeType      string        `json:"chargeType"`      // 计收类型，非必填
	Tag             bool          `json:"tag"`             // 补推标记，默认不是补推数据
}

type StatisticItem struct {
	Sum  int    `json:"sum"`  // 推送的用量大小
	Unit string `json:"unit"` // 推送的单位 Minutes|Bytes|Count|Byte|Bytes*Seconds|day
}

type Dimension struct {
	Name  string `json:"name"`  // 配置名称
	Value string `json:"value"` // 配置值
}
