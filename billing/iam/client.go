package iam

import (
	"encoding/json"
	"fmt"
	net "net/http"
	"strings"
	"sync"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/http"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/util"
)

const (
	Version = "v3"
)

type IamClientConfiguration struct {
	Endpoint   string
	UserName   string
	Password   string
	Domain     string
	SignOption *auth.SignOptions
	Retry      bce.RetryPolicy
	Timeout    int
	Logger     util.Logger
}

// Client is the general interface which can perform sending request. Different service
// will define its own client in case of specific extension.

var interval, _ = time.ParseDuration("5m")

// Client defines the general client to access the BCE services.
type Client struct {
	*bce.Client
	UserName   string
	Password   string
	Domain     string
	xAuthToken string
	Token      *Token
	tokenMutex sync.Mutex
}

func NewClient(conf *IamClientConfiguration) *Client {
	bceConf := bce.BceClientConfiguration{
		Endpoint:   conf.Endpoint,
		SignOption: conf.SignOption,
		Retry:      conf.Retry,
		Timeout:    conf.Timeout,
		Logger:     conf.Logger,
	}
	if bceConf.Retry == nil {
		bceConf.Retry = bce.DEFAULT_RETRY_POLICY
	}
	if bceConf.Timeout == 0 {
		bceConf.Timeout = bce.DEFAULT_CONNECTION_TIMEOUT
	}
	bceClient := bce.NewClient(&bceConf)
	client := &Client{
		Client:   bceClient,
		UserName: conf.UserName,
		Password: conf.Password,
		Domain:   conf.Domain,
	}
	return client
}

func (c *Client) ValidatorRequest(req *net.Request) (token *Token, err error) {

	signatureValidator, err := c.getSignValidator(req)
	if err != nil {
		return nil, fmt.Errorf("getSignValidator faild err: %v", err)
	}
	tokenId, err := c.getConsoleTokenID()
	if err != nil {
		return nil, fmt.Errorf("getConsoleTokenID faild err: %v", err)
	}
	token, err = c.signatureValidate(signatureValidator, tokenId)
	if err != nil {
		return nil, fmt.Errorf("signatureValidate faild err: %v", err)
	}
	return token, nil
}

func (c *Client) getSignValidator(req *net.Request) (signatureValidator *SignatureValidator, err error) {

	headers := map[string]string{}
	params := map[string]string{}
	headers[http.HOST] = req.Host
	for key, value := range req.Header {
		if value == nil || len(value) <= 0 {
			continue
		}
		headers[key] = value[0]
	}
	for key, value := range req.URL.Query() {
		if value == nil || len(value) <= 0 {
			continue
		}
		params[key] = value[0]
	}

	if err := req.ParseForm(); err != nil {
		return nil, fmt.Errorf("req.ParseForm field ")
	}

	signatureValidator = &SignatureValidator{
		Auth: Auth{
			Authorization: req.Header.Get(http.AUTHORIZATION),
			Request: Request{
				Method:  req.Method,
				Uri:     req.URL.Path,
				Headers: headers,
				Params:  params,
			},
		},
	}
	// if req.Header.Get(SECURITY_TOKEN_HEADER) != "" {
	// 	signatureValidator.Auth.SecurityToken = req.Header.Get(SECURITY_TOKEN_HEADER)
	// }
	return signatureValidator, nil
}

func (c *Client) signatureValidate(signatureValidator *SignatureValidator, tokenId string) (token *Token, err error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetProtocol("https")
	bceRequest.SetHeader("X-Auth-Token", tokenId)
	// todo subuserEnabled 没有赋值
	bceRequest.SetHeader("X-Subuser-Support", "true")
	bceRequest.SetUri(fmt.Sprintf("/%s/BCE-CRED/accesskeys", Version))
	bceRequest.SetMethod(http.POST)
	dataByte, err := json.Marshal(&signatureValidator)
	if err != nil {
		return nil, fmt.Errorf("unmarshal data[%v] err: %v", signatureValidator, err)
	}
	body, err := bce.NewBodyFromBytes(dataByte)
	if err != nil {
		return nil, fmt.Errorf("NewBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	tokenWrapper := &TokenWrapper{}
	if err := resp.ParseJsonBody(tokenWrapper); err != nil {
		return nil, err
	}
	newTokenId := resp.Header("X-Subject-Token")
	if newTokenId == "" {
		return nil, fmt.Errorf("auth faild, tokenId is empty,err:%v", err)
	}
	tokenWrapper.Token.ID = newTokenId
	return &tokenWrapper.Token, nil
}

func (c *Client) getConsoleTokenID() (tokenId string, err error) {
	token, err := c.getConsoleToken()
	if err != nil {
		return "", fmt.Errorf("getConsoleToken faild err: %v", err)
	}
	return token.ID, nil
}

func (c *Client) GetConsoleTokenID() (tokenId string, err error) {
	return c.getConsoleTokenID()
}

func (c *Client) GetAccessKeys(userId string, isNeedCreateWhenEmpty bool) (accessKeys *AccessKeys, err error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetProtocol("https")
	bceRequest.SetTokenId(c.xAuthToken)
	bceRequest.SetUri(fmt.Sprintf("/%s/users/%s/accesskeys", Version, userId))
	bceRequest.SetMethod(http.GET)

	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	accessKeys = &AccessKeys{}
	if err := resp.ParseJsonBody(accessKeys); err != nil {
		return nil, err
	}
	if isNeedCreateWhenEmpty && accessKeys.AccessKeys == nil && len(accessKeys.AccessKeys) == 0 {
		return c.CreateAccessKeys(userId)
	}
	return accessKeys, nil
}

func (c *Client) CreateUserAccessKeys(userId string) (accessKeys *AccessKeys, err error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetProtocol("https")
	bceRequest.SetUri(fmt.Sprintf("/%s/users/%s/accesskeys", Version, userId))
	bceRequest.SetMethod(http.POST)

	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	accessKeys = &AccessKeys{}
	if err := resp.ParseJsonBody(accessKeys); err != nil {
		return nil, err
	}
	return accessKeys, nil
}

func (c *Client) CreateAccessKeys(userId string) (accessKeys *AccessKeys, err error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetProtocol("https")
	bceRequest.SetUri(fmt.Sprintf("/%s/users/%s/accesskeys", Version, userId))
	bceRequest.SetMethod(http.POST)

	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	accessKeys = &AccessKeys{}
	if err := resp.ParseJsonBody(accessKeys); err != nil {
		return nil, err
	}
	return accessKeys, nil
}

func (c *Client) GetAccessKey() (accessKey *AccessKey, err error) {
	if c.Credentials != nil {
		accessKey := &AccessKey{}
		accessKey.Access = c.Credentials.AccessKeyId
		accessKey.Secret = c.Credentials.SecretAccessKey
		return accessKey, nil
	}
	token, err := c.getConsoleToken()
	if err != nil {
		return nil, err
	}
	accessKeys, err := c.GetAccessKeys(token.User.ID, true)
	if err != nil {
		return nil, err
	}

	aks := accessKeys.AccessKeys
	if len(aks) == 0 {
		return nil, fmt.Errorf("got 0 access keys from iam")
	}

	// 2023.05.25 智能云服务号AK安全合规治理
	//  1、iam 要求改用 ALTAK*** 格式的 AK，所以这里优先查找此类 AK 返回
	// 	2、如果找不到，那就返回第1个，防止业务中断
	for i, ak := range aks {
		if strings.HasPrefix(ak.Access, "ALTAK") {
			return &aks[i], nil
		}
	}

	return &aks[0], err
}

func (c *Client) GetConsoleToken() (token *Token, err error) {
	return c.getConsoleToken()
}

func (c *Client) getConsoleToken() (token *Token, err error) {
	if c.Token != nil && time.Now().Add(interval).Before(c.Token.ExpiresAt) {
		return c.Token, nil
	}
	c.tokenMutex.Lock()
	defer c.tokenMutex.Unlock()
	// 双重判断
	if c.Token != nil && time.Now().Add(interval).Before(c.Token.ExpiresAt) {
		return c.Token, nil
	}
	methods := []string{
		"password",
	}
	auth := Authentication{
		Identity: Identity{
			Token:   nil,
			Methods: methods,
			Password: Password{
				User: PasswordUser{
					Domain: Domain{
						Name: c.Domain,
					},
					Name:     c.UserName,
					Password: c.Password,
				},
			},
		},
		Scope: Scope{
			Domain: Domain{
				ID: "default",
			},
			Project: nil,
		},
	}
	bceRequest := &bce.BceRequest{}
	bceRequest.SetProtocol("https")
	bceRequest.SetUri(fmt.Sprintf("/%s/auth/tokens", Version))
	bceRequest.SetMethod(http.POST)
	authBody := map[string]interface{}{
		"auth": auth,
	}
	dataByte, err := json.Marshal(&authBody)
	if err != nil {
		return nil, fmt.Errorf("unmarshal data[%v] err: %v", auth, err)
	}
	body, err := bce.NewBodyFromBytes(dataByte)
	if err != nil {
		return nil, fmt.Errorf("NewBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	tokenWrapper := &TokenWrapper{}
	if err := resp.ParseJsonBody(tokenWrapper); err != nil {
		return nil, err
	}
	token = &tokenWrapper.Token
	tokenId := resp.Header("X-Subject-Token")
	if tokenId == "" {
		return nil, fmt.Errorf("auth faild, tokenId is empty,err:%v", err)
	}
	token.ID = tokenId
	c.xAuthToken = tokenId
	c.Token = token
	return token, nil
}

// func (c *Client) Verify(req *net.Request, permissionRequest PermissionRequest) (verifyResult *VerifyResult, err error) {
// 	userId := req.Header.Get(http.INT_USER)
// 	if userId == "" {
// 		return nil, fmt.Errorf("%s from header is empty", http.INT_USER)
// 	}
// 	return c.VerifyWithUserId(userId, permissionRequest)

// }

func (c *Client) VerifyWithUserId(userId string, permissionRequest PermissionRequest) (verifyResult *VerifyResult, err error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetProtocol("https")
	bceRequest.SetUri("/v3/users/" + userId + "/permissions")
	bceRequest.SetMethod(http.POST)
	tokenId, err := c.getConsoleTokenID()
	if err != nil {
		return nil, fmt.Errorf("getConsoleTokenID faild, error: %v", err)
	}
	bceRequest.SetHeader("X-Auth-Token", tokenId)
	bceRequest.SetHeader("X-Subuser-Support", "true")
	dataByte, err := json.Marshal(&permissionRequest)
	if err != nil {
		return nil, fmt.Errorf("unmarshal data[%v] err: %v", permissionRequest, err)
	}
	body, err := bce.NewBodyFromBytes(dataByte)
	if err != nil {
		return nil, fmt.Errorf("NewBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	tokenAndVerifyResult := &TokenAndVerifyResult{}
	if err := resp.ParseJsonBody(tokenAndVerifyResult); err != nil {
		return nil, err
	}
	return &tokenAndVerifyResult.VerifyResult, nil

}

func (c *Client) AuthAndVerify(req *net.Request, permissionRequest PermissionRequest) (tokenAndVerifyResult *TokenAndVerifyResult, err error) {
	authPermissionRequest := newAuthPermissionRequest(permissionRequest)

	signatureValidator, err := c.getSignValidator(req)
	if err != nil {
		return nil, fmt.Errorf("getSignValidator faild err: %v", err)
	}
	authPermissionRequest.Auth = signatureValidator.Auth
	tokenId, err := c.getConsoleTokenID()
	if err != nil {
		return nil, fmt.Errorf("getConsoleTokenID faild err: %v", err)
	}
	return c.authAndVerify(authPermissionRequest, tokenId)
}

func (c *Client) AuthAndVerifyWithToken(req *net.Request, permissionRequest PermissionRequest, tokenId string) (tokenAndVerifyResult *TokenAndVerifyResult, err error) {
	authPermissionRequest := newAuthPermissionRequest(permissionRequest)

	signatureValidator, err := c.getSignValidator(req)
	if err != nil {
		return nil, fmt.Errorf("getSignValidator faild err: %v", err)
	}
	authPermissionRequest.Auth = signatureValidator.Auth
	return c.authAndVerify(authPermissionRequest, tokenId)
}

func (c *Client) authAndVerify(authPermissionRequest *AuthPermissionRequest, tokenId string) (tokenAndVerifyResult *TokenAndVerifyResult, err error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetProtocol("https")
	bceRequest.SetUri(fmt.Sprintf("/%s/BCE-CRED/permissions", Version))
	bceRequest.SetMethod(http.POST)
	if tokenId != "" {
		bceRequest.SetHeader("X-Auth-Token", tokenId)
	}

	bceRequest.SetHeader("X-Subuser-Support", "true")
	dataByte, err := json.Marshal(&authPermissionRequest)
	if err != nil {
		return nil, fmt.Errorf("unmarshal data[%v] err: %v", authPermissionRequest, err)
	}
	body, err := bce.NewBodyFromBytes(dataByte)
	if err != nil {
		return nil, fmt.Errorf("NewBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	tokenAndVerifyResult = &TokenAndVerifyResult{}
	if err := resp.ParseJsonBody(tokenAndVerifyResult); err != nil {
		return nil, err
	}
	return tokenAndVerifyResult, nil
}

func newAuthPermissionRequest(permissionRequest PermissionRequest) *AuthPermissionRequest {
	authPermissionRequest := AuthPermissionRequest{}
	authPermissionRequest.Service = permissionRequest.Service
	authPermissionRequest.Region = permissionRequest.Region
	authPermissionRequest.Resource = permissionRequest.Resource
	authPermissionRequest.ResourceOwner = permissionRequest.ResourceOwner
	authPermissionRequest.RequestContext = permissionRequest.RequestContext
	authPermissionRequest.Permission = permissionRequest.Permission
	return &authPermissionRequest
}

// BindVirtualAccount bind iam virtual account
func (c *Client) BindVirtualAccount(param *BindAccountParam) (*BindAccountResp, error) {
	_, err := c.getConsoleToken()
	if err != nil {
		return nil, err
	}

	bceRequest := &bce.BceRequest{}
	bceRequest.SetProtocol("https")
	bceRequest.SetTokenId(c.xAuthToken)
	bceRequest.SetUri(ApiBindAccount)
	bceRequest.SetMethod(http.POST)

	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	bceRequest.SetHeader("X-Auth-Token", c.xAuthToken)

	bodyJson, err := json.Marshal(param)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal BindAccountParam, err: %v", err)
	}
	b, err := bce.NewBodyFromBytes(bodyJson)
	if err != nil {
		return nil, err
	}
	bceRequest.SetBody(b)

	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	bindAccountResp := new(BindAccountResp)
	if err := resp.ParseJsonBody(bindAccountResp); err != nil {
		return nil, err
	}
	return bindAccountResp, nil
}

// QueryAccountBriefInfo query account brief info
func (c *Client) QueryAccountBriefInfo(param *QueryAccountBriefInfoParam) (*AccountBriefInfo, error) {
	_, err := c.getConsoleToken()
	if err != nil {
		return nil, err
	}

	bceRequest := &bce.BceRequest{}
	bceRequest.SetProtocol("https")
	bceRequest.SetTokenId(c.xAuthToken)
	bceRequest.SetUri(ApiQueryAccountBriefInfo)
	bceRequest.SetMethod(http.POST)

	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	bceRequest.SetHeader("X-Auth-Token", c.xAuthToken)

	bodyJson, err := json.Marshal(param)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal QueryAccountBriefInfoParam, err: %v", err)
	}
	b, err := bce.NewBodyFromBytes(bodyJson)
	if err != nil {
		return nil, err
	}
	bceRequest.SetBody(b)

	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	briefInfo := new(AccountBriefInfo)
	if err := resp.ParseJsonBody(briefInfo); err != nil {
		return nil, err
	}
	return briefInfo, nil
}

// CreateVirtualAccount Deprecated
func (c *Client) CreateVirtualAccount(param *CreateAccountParam) (*CreateAccountResp, error) {
	_, err := c.getConsoleToken()
	if err != nil {
		return nil, err
	}

	bceRequest := &bce.BceRequest{}
	bceRequest.SetProtocol("https")
	bceRequest.SetTokenId(c.xAuthToken)
	bceRequest.SetUri(ApiCreateAccount)
	bceRequest.SetMethod(http.POST)

	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	bceRequest.SetHeader("X-Auth-Token", c.xAuthToken)

	bodyJson, err := json.Marshal(param)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal CreateAccountParam, err: %v", err)
	}
	b, err := bce.NewBodyFromBytes(bodyJson)
	if err != nil {
		return nil, err
	}
	bceRequest.SetBody(b)

	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	createAccountResp := new(CreateAccountResp)
	if err := resp.ParseJsonBody(createAccountResp); err != nil {
		return nil, err
	}
	return createAccountResp, nil
}

// ActivateVirtualAccount Deprecated
func (c *Client) ActivateVirtualAccount(param *ActivateAccountParam) (*ActivateAccountResp, error) {
	_, err := c.getConsoleToken()
	if err != nil {
		return nil, err
	}

	bceRequest := &bce.BceRequest{}
	bceRequest.SetProtocol("https")
	bceRequest.SetUri(ApiActivateAccount)
	bceRequest.SetMethod(http.POST)

	bceRequest.SetHeader("X-Auth-Token", c.xAuthToken)
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)

	bodyJson, err := json.Marshal(param)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal ActivateAccountParam, err: %v", err)
	}
	b, err := bce.NewBodyFromBytes(bodyJson)
	if err != nil {
		return nil, err
	}
	bceRequest.SetBody(b)

	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	accountResp := new(ActivateAccountResp)
	if err := resp.ParseJsonBody(accountResp); err != nil {
		return nil, err
	}
	return accountResp, nil
}

func (c *Client) QueryVirtualAccount(param *QueryAccountParam) (*QueryAccountResp, error) {
	_, err := c.getConsoleToken()
	if err != nil {
		return nil, err
	}

	api := ApiQueryAccount
	bceRequest := &bce.BceRequest{}

	if param.AccountId != "" {
		bceRequest.SetParam("accountId", param.AccountId)
	} else if param.AccountName != "" {
		bceRequest.SetParam("accountName", param.AccountName)
	} else if param.ThirdPartType != "" {
		bceRequest.SetParams(map[string]string{
			"thirdPartType": param.ThirdPartType,
			"thirdPartId":   param.ThirdPartId,
		})
	} else {
		return nil, fmt.Errorf("invalid parameter")
	}

	bceRequest.SetProtocol("https")
	bceRequest.SetUri(api)
	bceRequest.SetMethod(http.GET)

	bceRequest.SetHeader("X-Auth-Token", c.xAuthToken)
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)

	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	accountResp := new(QueryAccountResp)
	if err := resp.ParseJsonBody(accountResp); err != nil {
		return nil, err
	}
	return accountResp, nil
}
