package iam

import "time"

const (
	// SECURITY_TOKEN_HEADER      = "X-Baidu-Int-Security-Token"
	// SECURITY_TOKEN_QUERYSTRING = "X-Baidu-Int-Security-Token"

	EffectAllow       = "ALLOW"
	EffectDeny        = "DENY"
	EffectDefaultDeny = "DEFAULT_DENY"
)

const (
	ApiBindAccount           = "/v3/virtual/account/bind"
	ApiQueryAccountBriefInfo = "/v3/virtual/account/briefInfo"
	ApiCreateAccount         = "/v3/virtual/account/create"
	ApiActivateAccount       = "/v3/virtual/account/activate"
	ApiQueryAccount          = "/v3/virtual/account/detail"
)

const (
	ThirdPartTypePass = "PASSPORT"
	ThirdPartTypeUC   = "UC"
)

// ============================ AccessKey ===================================

type AccessKeys struct {
	AccessKeys []AccessKey `json:"accesskeys"`
}

type AccessKey struct {
	CredentialId string    `json:"credential_id"`
	Access       string    `json:"access"`
	Secret       string    `json:"secret"`
	ProjectId    string    `json:"project_id"`
	UserId       string    `json:"user_id"`
	Enabled      bool      `json:"enabled"`
	Downloaded   bool      `json:"downloaded"`
	CreateTime   string    `json:"modify_time"`
	Description  string    `json:"description"`
	Trust        TrustRoot `json:"trust"`
}

type TrustRoot struct {
	Trust Trust `json:"trust"`
}

type Trust struct {
	Id            string      `json:"id"`
	TrustRoles    []TrustRole `json:"roles"`
	TrustorUserId string      `json:"trustor_user_id"`
	TrusteeUserId string      `json:"trustee_user_id"`
	ProjectId     string      `json:"project_id"`
	Impersonation bool        `json:"impersonation"`
	DeletedAt     string      `json:"deleted_at"`
	ExpiresAt     string      `json:"expires_at"`
	RemainingUses int         `json:"remaining_uses"`
}

type TrustRole struct {
	TrustId string `json:"trustId"`
	RoleId  string `json:"id"`
}

// ============================ Authentication ===================================

type Authentication struct {
	Identity Identity `json:"identity"`
	Scope    Scope    `json:"scope"`
}

type Identity struct {
	Password Password       `json:"password"`
	Token    *IdentityToken `json:"token,omitempty"`
	Methods  []string       `json:"methods"`
}

type Scope struct {
	Domain  Domain   `json:"domain"`
	Project *Project `json:"project,omitempty"`
}

type Password struct {
	User PasswordUser `json:"user"`
}

type IdentityToken struct {
	ID string `json:"id"`
}

type PasswordUser struct {
	ID       string `json:"id,omitempty"`
	Name     string `json:"name"`
	Password string `json:"password"`
	Domain   Domain `json:"domain"`
}

// ============================ PermissionRequest ===================================

type PermissionRequest struct {
	Service        string         `json:"service"`
	Region         string         `json:"region"`
	Resource       string         `json:"resource"`
	ResourceOwner  string         `json:"resource_owner"`
	Permission     []string       `json:"permission"`
	RequestContext RequestContext `json:"request_context"`
}

type RequestContext struct {
	IpAddress  string                 `json:"ip_address"`
	Referer    string                 `json:"referer"`
	Conditions map[string]interface{} `json:"conditions"`
}

type AuthPermissionRequest struct {
	PermissionRequest
	Auth Auth `json:"auth"`
}

// ============================ SignatureValidator ===================================

type SignatureValidator struct {
	Auth Auth `json:"auth"`
}

type Auth struct {
	Authorization string  `json:"authorization"`
	Request       Request `json:"request"`
	SecurityToken string  `json:"security_token,omitempty"`
}

type Request struct {
	Method  string            `json:"method"`
	Uri     string            `json:"uri"`
	Params  map[string]string `json:"params"`
	Headers map[string]string `json:"headers"`
}

// ============================ Token ===================================

type TokenWrapper struct {
	Token Token `json:"token"`
}

type Token struct {
	ID         string     `json:"id"`
	ExpiresAt  time.Time  `json:"expires_at"`
	IssuedAt   time.Time  `json:"issued_at"`
	Methods    []string   `json:"methods"`
	Domain     Domain     `json:"domain"`
	User       User       `json:"user"`
	Federation Federation `json:"federation"`
	Roles      []Role     `json:"roles"`
	Catalog    []Service  `json:"catalog"`
	Project    Project    `json:"project"`
}

type Domain struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type UserDomain struct {
	ID           string       `json:"id"`
	Name         string       `json:"name"`
	Organization Organization `json:"organization"`
}

type Organization struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	MasterId string `json:"master_id"`
}

type Project struct {
	ID     string `json:"id,omitempty"`
	Name   string `json:"name"`
	Domain Domain `json:"domain"`
}

type User struct {
	ID     string     `json:"id,omitempty"`
	Name   string     `json:"name"`
	Domain UserDomain `json:"domain"`
}

type Role struct {
	ID   string `json:"id"`
	Type string `json:"type"`
}

type Endpoint struct {
	ID               string `json:"id"`
	Region           string `json:"region"`
	Url              string `json:"url"`
	Enabled          bool   `json:"enabled"`
	LegacyEndpointId string `json:"legacy_endpoint_id"`
	Iface            string `json:"interface"`
}

type Federation struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	DomainId string `json:"domain_id"`
}

type Service struct {
	ID        string     `json:"id"`
	Type      string     `json:"type"`
	Endpoints []Endpoint `json:"endpoints"`
}

// ============================ VerifyResult ===================================

type VerifyResult struct {
	Effect string `json:"effect"`
	Id     string `json:"id"`
	Eid    string `json:"eid"`
}

type TokenAndVerifyResult struct {
	Token        Token        `json:"token"`
	VerifyResult VerifyResult `json:"verify_result"`
}

// ============================ VirtualAccount ===================================

type BindAccountParam struct {
	ThirdPartType string `json:"thirdPartType,omitempty"`
	ThirdPartId   string `json:"thirdPartId,omitempty"`
}

type BindAccountResp struct {
	AccountName   string `json:"accountName"`
	AccountId     string `json:"accountId"`
	ThirdPartType string `json:"thirdPartType,omitempty"`
	ThirdPartId   string `json:"thirdPartId,omitempty"`
	Credential    struct {
		SessionToken    string `json:"sessionToken"`
		Expiration      string `json:"expiration"`
		AccessKeyId     string `json:"accessKeyId"`
		SecretAccessKey string `json:"secretAccessKey"`
		RoleId          string `json:"roleId"`
	} `json:"credential"`
	RegisterTime string `json:"register_time"`
	ActivateTime string `json:"activate_time"`
}

type QueryAccountBriefInfoParam struct {
	ThirdPartType string `json:"thirdPartType,omitempty"`
	ThirdPartId   string `json:"thirdPartId,omitempty"`
}

type AccountBriefInfo struct {
	AccountId     string `json:"accountId"`
	ThirdPartType string `json:"thirdPartType"`
	ThirdPartId   string `json:"thirdPartId"`
}

type VirtualAccount struct {
	Name string `json:"name"`
}

type CreateAccountParam struct {
	Account       VirtualAccount `json:"account"`
	ThirdPartType string         `json:"thirdPartType,omitempty"`
	ThirdPartId   string         `json:"thirdPartId,omitempty"`
	Email         string         `json:"email,omitempty"`
	MobilePhone   string         `json:"mobilePhone,omitempty"`
	Finance       bool           `json:"finance,omitempty"`
	Description   string         `json:"description,omitempty"`
}

type CreateAccountResp struct {
	Source        string      `json:"source"`
	ThirdPartType string      `json:"thirdPartType"`
	ThirdPartId   string      `json:"thirdPartId"`
	Account       AccountInfo `json:"account"`
}

type AccountInfo struct {
	Name         string      `json:"name"`
	DomainId     string      `json:"domain_id"`
	DomainName   string      `json:"domain_name"`
	Email        string      `json:"email"`
	MobilePhone  string      `json:"mobile_phone"`
	RegisterTime string      `json:"register_time"`
	ActivateTime string      `json:"activate_time"`
	ModifyTime   string      `json:"modify_time"`
	Description  interface{} `json:"description"`
}

type ActivateAccountParam struct {
	StsRoleName     string `json:"stsRoleName"`
	ServicePolicyId string `json:"servicePolicyId"`
	ServiceId       string `json:"serviceId"`
	AccountId       string `json:"accountId"`
	MobilePhone     string `json:"mobilePhone"`
	Email           string `json:"email"`
}

type ActivateAccountResp struct {
	Account AccountInfo `json:"account"`
}

type QueryAccountParam struct {
	AccountId     string `json:"accountId"`
	AccountName   string `json:"accountName"`
	ThirdPartType string `json:"thirdPartType,omitempty"`
	ThirdPartId   string `json:"thirdPartId,omitempty"`
}

type QueryAccountResp struct {
	Source  string      `json:"source"`
	Account AccountInfo `json:"account"`
}
