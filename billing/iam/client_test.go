package iam

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/testings"
)

func TestGetAKSK(t *testing.T) {
	conf := IamClientConfiguration{
		Endpoint: "http://iam.bj.internal-qasandbox.baidu-int.com",
		UserName: "xuperasset2",
		Password: "SRPA4SEbWuGO5usSCklz9voaIq1krcyI",
		Domain:   "Default",
		Retry:    &bce.NoRetryPolicy{},
		Timeout:  bce.DEFAULT_CONNECTION_TIMEOUT,
		Logger:   testings.NewLogger(),
	}
	iamClient := NewClient(&conf)
	accessKey, err := iamClient.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println(testings.ToJSON(accessKey))
}
