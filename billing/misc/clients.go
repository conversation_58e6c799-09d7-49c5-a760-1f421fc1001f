package misc

import (
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/iam"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/order"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/packagesvc"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/sts"
)

func newIamClient(t *testing.T) *iam.Client {
	iamConf := iam.IamClientConfiguration{
		Endpoint: "http://iam.bj.internal-qasandbox.baidu-int.com",
		UserName: "xuperasset2",
		Password: "SRPA4SEbWuGO5usSCklz9voaIq1krcyI",
		Domain:   "Default",
	}
	return iam.NewClient(&iamConf)
}

func newStsClient(t *testing.T) *sts.Client {
	iamCli := newIamClient(t)

	accessKey, err := iamCli.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}
	stsConf := sts.StsClientConfiguration{
		Endpoint: "http://sts.bj.internal-qasandbox.baidu-int.com:8586",
		Credentials: auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
	}
	stsCli, err := sts.NewClient(&stsConf)
	if err != nil {
		t.Fatal(err)
	}

	return stsCli
}

func newOrderClient(t *testing.T, accountId string) *order.Client {
	stsCli := newStsClient(t)

	cred, err := stsCli.AssumeRole(accountId, "BceServiceRole_xuperasset2")
	if err != nil {
		t.Fatal(err)
	}
	conf := order.OrderClientConfiguration{
		Endpoint: "http://order.internal-qasandbox.bce-internal.baidu-int.com",
		Credentials: auth.BceCredentials{
			AccessKeyId:     cred.AccessKeyId,
			SecretAccessKey: cred.AccessKeySecret,
			SessionToken:    cred.SessionToken,
		},
	}
	cli, err := order.NewClient(&conf)
	if err != nil {
		t.Fatal(err)
	}

	return cli
}

func newPackageClient(t *testing.T) *packagesvc.Client {
	iamCli := newIamClient(t)

	accessKey, err := iamCli.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}

	conf := packagesvc.PackageSvcClientConfiguration{
		Endpoint: "http://bjyz-y22-sandbox001.bjyz.baidu.com:8666",
		Credentials: auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
	}
	cli, err := packagesvc.NewClient(&conf)
	if err != nil {
		t.Fatal(err)
	}

	return cli
}
