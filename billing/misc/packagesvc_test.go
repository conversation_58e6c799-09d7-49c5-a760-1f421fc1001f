package misc

import (
	"fmt"
	"testing"

	ordermodel "icode.baidu.com/baidu/blockchain/xasset-golib/billing/order/model"
	pkgmodel "icode.baidu.com/baidu/blockchain/xasset-golib/billing/packagesvc/model"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/testings"
)

const (
	FlavorCount         = "count"
	FlavorSpecification = "Specification_0"
)

var (
	PackageSpecMapping = map[string]string{
		"10CB":   "10",
		"100CB":  "100",
		"1000CB": "1000",
		"1WCB":   "10000",
		"5WCB":   "50000",
		"10WCB":  "100000",
	}
)

// getPackageIfExisted 找到 order 创建的所有量包, 返回 pkgName -> itemKey
func getPackageIfExisted(t *testing.T, accountId, orderId string) map[string]string {
	pkgCli := newPackageClient(t)

	opt := pkgmodel.AcctPackagesOptions{
		AccountId:   accountId,
		ServiceType: "XUPERASSET",
		Regin:       "global",
		PageNo:      1,
		PageSize:    100,
	}
	accPkgsResp, err := pkgCli.GetPackagesByAccount(&opt)
	if err != nil {
		t.Fatal(err)
	}

	pkgMap := make(map[string]string)
	for _, p := range accPkgsResp.Result {
		if p.OrderId == orderId {
			pkgMap[p.PackageName] = p.OrderItemKey
		}
	}

	return pkgMap
}

// TestCreatePackageAndBind 为预付费订单创建量包，并绑定到订单上
func TestCreatePackageAndBind(t *testing.T) {
	accountId := "c3e4b456820c498fb70a18fe1f5782f5"

	orderCli := newOrderClient(t, accountId)
	pkgCli := newPackageClient(t)

	orderId := "c68c61e9d6fc47508f6812383ea4f87d"
	order, err := orderCli.GetOrder(orderId)
	if err != nil {
		t.Fatal(err)
	}

	pkgMap := getPackageIfExisted(t, order.AccountId, order.Uuid)
	if len(pkgMap) > 0 {
		fmt.Println("=== package already created")
	} else {
		fmt.Println("=== package creating...")
		order4Package, err := packOrder4PackageCreation(order)
		if err != nil {
			t.Fatal(err)
		}
		pkgs, err := pkgCli.CreatePackages(order4Package)
		if err != nil {
			t.Fatal(err)
		}
		for _, pkg := range pkgs {
			pkgMap[pkg.PackageName] = pkg.OrderItemKey
		}
	}

	var resourceMapping []ordermodel.ResourceMapping
	for pkgName, itemKey := range pkgMap {
		resourceMapping = append(resourceMapping, ordermodel.ResourceMapping{
			Key:    itemKey,
			Id:     pkgName,
			Status: ordermodel.RUNNING,
		})
	}

	fmt.Println(testings.ToJSON(resourceMapping))

	updateReq := ordermodel.UpdateOrderRequest{
		OrderStatus: ordermodel.CREATED,
		Resources:   resourceMapping,
	}

	_, err = orderCli.UpdateOrder(orderId, &updateReq)
	if err != nil {
		t.Fatal(err)
	}
}

func packOrder4PackageCreation(req *pkgmodel.CreatePackageRequest) (*pkgmodel.CreatePackageRequest, error) {
	newItems := make([]pkgmodel.OrderItem, 0, len(req.Items))

	for _, item := range req.Items {
		if countValue := flavorExists(item.Flavor, FlavorCount); len(countValue) > 0 {
			// 已经存在 count 项了，无需处理
			newItems = append(newItems, item)
			continue
		}

		// 找到 "Specification_0" 这一项，取其中的数字构建新的 flavor
		specValue := flavorExists(item.Flavor, FlavorSpecification)
		if len(specValue) == 0 {
			return nil, fmt.Errorf("order items should have Specification_0 flavor")
		}

		packageCount, exist := PackageSpecMapping[specValue]
		if !exist {
			return nil, fmt.Errorf("missing package specification: %s", specValue)
		}

		newItem := item
		newItem.Flavor = append(newItem.Flavor, pkgmodel.Flavor{
			Name:  FlavorCount,
			Value: packageCount,
			Scale: 1,
		})
		newItems = append(newItems, newItem)
	}

	resp := *req
	resp.Items = newItems
	return &resp, nil
}

// flavorExists 判断是否存在待查询的 flavor，存在则返回值，不存在返回空
func flavorExists(flavors []pkgmodel.Flavor, name string) string {
	for _, flavor := range flavors {
		if flavor.Name == name {
			return flavor.Value
		}
	}

	return ""
}
