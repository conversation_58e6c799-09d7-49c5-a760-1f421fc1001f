package notification

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/http"
)

type Client struct {
	*bce.Client
}

func NewClient(conf *NotificationClientConfiguration) (*Client, error) {
	if err := conf.Valid(); err != nil {
		return nil, fmt.Errorf("NotificationClientConfiguration invalid, error: %v", err)
	}
	if conf.SignOption == nil {
		conf.SignOption = &auth.SignOptions{
			HeadersToSign: auth.DEFAULT_HEADERS_TO_SIGN,
			ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS,
			IsBce:         true,
		}
	}
	if conf.Retry == nil {
		conf.Retry = bce.DEFAULT_RETRY_POLICY
	}
	bceConf := bce.BceClientConfiguration{
		Endpoint:    conf.Endpoint,
		Credentials: &conf.Credentials,
		SignOption:  conf.SignOption,
		Retry:       conf.Retry,
		Timeout:     conf.Timeout,
		Logger:      conf.Logger,
	}
	bceClient := bce.NewClient(&bceConf)
	client := Client{
		Client: bceClient,
	}

	return &client, nil
}

func (n *Client) CreateTask(opt *CreateNotifyTaskOption) (*NotifyResponse, error) {
	if len(opt.Name) == 0 || len(opt.Receiver) == 0 ||
		len(opt.ReceiverType) == 0 || len(opt.TemplateId) == 0 {
		return nil, errors.New("param error")
	}
	bceRequest := &bce.BceRequest{}
	bceRequest.SetMethod(http.POST)
	bceRequest.SetUri(NotifyTaskApi)
	bceRequest.BuildHttpRequest()
	bceRequest.SetHeader(http.HOST, bceRequest.Host())
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	var contentStr string
	if len(opt.ContentVar) != 0 {
		contentJosn, err := json.Marshal(opt.ContentVar)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal contentvar, err: %v", err)
		}
		contentStr = string(contentJosn)
	}
	receivers := strings.Join(opt.Receiver, ",")
	body := CreateNotifyTaskBody{
		Name:         opt.Name,
		Id:           uuid.NewString(),
		TemplateId:   opt.TemplateId,
		ContentVar:   contentStr,
		Attachments:  opt.Attachments,
		Receiver:     receivers,
		ReceiverType: opt.ReceiverType,
	}
	bodyJson, err := json.Marshal(&body)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal CreateNotifyTaskBody, err: %v", err)
	}
	b, err := bce.NewBodyFromBytes(bodyJson)
	if err != nil {
		return nil, err
	}
	bceRequest.SetBody(b)
	resp := &bce.BceResponse{}
	if err := n.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	notifyResponse := new(NotifyResponse)
	if err := resp.ParseJsonBody(notifyResponse); err != nil {
		return nil, err
	}
	return notifyResponse, nil
}

func (n *Client) QueryTask(applyId string) (*NotifyResponse, error) {
	if len(applyId) == 0 {
		return nil, errors.New("param error")
	}
	bceRequest := &bce.BceRequest{}
	bceRequest.SetMethod(http.GET)
	bceRequest.SetUri(NotifyTaskApi)
	bceRequest.BuildHttpRequest()
	bceRequest.SetHeader(http.HOST, bceRequest.Host())
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	bceRequest.SetParam("applyId", applyId)
	resp := &bce.BceResponse{}
	if err := n.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	notifyResponse := new(NotifyResponse)
	if err := resp.ParseJsonBody(notifyResponse); err != nil {
		return nil, err
	}
	return notifyResponse, nil
}
