package notification

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/iam"
)

func TestCreateTask(t *testing.T) {
	iamConf := iam.IamClientConfiguration{
		Endpoint: "http://iam.bj.internal-qasandbox.baidu-int.com",
		UserName: "xuperasset2",
		Password: "SRPA4SEbWuGO5usSCklz9voaIq1krcyI",
		Domain:   "Default",
	}
	iamCli := iam.NewClient(&iamConf)
	accessKey, err := iamCli.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}
	conf := NotificationClientConfiguration{
		Endpoint: "http://gzns-store-sandbox107.gzns.baidu.com:8681",
		Credentials: auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
	}
	cli, err := NewClient(&conf)
	if err != nil {
		t.Fatal(err)
	}
	opt := CreateNotifyTaskOption{
		Name:         "test2",
		TemplateId:   "Tpl_98cf13ed-c9ad-4d5f-bc21-773f899ee501",
		Receiver:     []string{"d018e3cf5ad04985b6f57ff9ccee4362"},
		ReceiverType: ReceiverUserId,
	}
	info, err := cli.CreateTask(&opt)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(info)
}

func TestQueryTask(t *testing.T) {
	iamConf := iam.IamClientConfiguration{
		Endpoint: "http://iam.bj.internal-qasandbox.baidu-int.com",
		UserName: "xuperasset2",
		Password: "SRPA4SEbWuGO5usSCklz9voaIq1krcyI",
		Domain:   "Default",
	}
	iamCli := iam.NewClient(&iamConf)
	accessKey, err := iamCli.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}
	conf := NotificationClientConfiguration{
		Endpoint: "http://gzns-store-sandbox107.gzns.baidu.com:8681",
		Credentials: auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
	}
	cli, err := NewClient(&conf)
	if err != nil {
		t.Fatal(err)
	}
	info, err := cli.QueryTask("39d10d5e-60bd-4e3c-9a1a-f6b8a12391d1")
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(info)
}
