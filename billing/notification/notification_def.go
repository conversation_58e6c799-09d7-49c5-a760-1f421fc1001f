package notification

import (
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/util"
)

const (
	NotifyTaskApi = "/v1/task"
)

type NotifyStatus string

const (
	NotificationNew        NotifyStatus = "NEW"
	NotificationProcessing NotifyStatus = "PROCESSING"
	NotificationWaiting    NotifyStatus = "WAITING"
	NotificationDone       NotifyStatus = "DONE"
	NotificationError      NotifyStatus = "ERROR"
)

type ReceiverType string

const (
	ReceiverUserId    ReceiverType = "UserId"
	ReceiverMobile    ReceiverType = "Mobile"
	ReceiverEmail     ReceiverType = "Email"
	ReceiverUserGroup ReceiverType = "UserGroup"
)

type NotificationClientConfiguration struct {
	Endpoint    string
	Retry       bce.RetryPolicy
	SignOption  *auth.SignOptions
	Credentials auth.BceCredentials
	Timeout     int
	Logger      util.Logger
}

func (c *NotificationClientConfiguration) Valid() error {
	if len(c.Endpoint) == 0 {
		return fmt.Errorf("missing endpoint")
	}
	if len(c.Credentials.AccessKeyId) == 0 || len(c.Credentials.SecretAccessKey) == 0 {
		return fmt.Errorf("missing credentials")
	}
	return nil
}

type Task struct {
	Id     string
	Status NotifyStatus
}

type NotifyResponse struct {
	ApplyId string
	Status  NotifyStatus
	Tasks   []Task
}

type Attachment struct {
	Name string `json:"name"`
	Data string `json:"data"`
}

type CreateNotifyTaskOption struct {
	Name         string
	TemplateId   string
	ContentVar   map[string]string
	Attachments  []Attachment
	Receiver     []string
	ReceiverType ReceiverType
}

type CreateNotifyTaskBody struct {
	Name         string       `json:"name"`
	Id           string       `json:"id"`
	TemplateId   string       `json:"templateId"`
	ContentVar   string       `json:"contentVar"`
	Attachments  []Attachment `json:"attachments"`
	Receiver     string       `json:"receiver"`
	ReceiverType ReceiverType `json:"receiverType"`
}
