package order

import (
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/http"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/order/model"
)

type Client struct {
	*bce.Client
}

func NewClient(conf *OrderClientConfiguration) (*Client, error) {
	if err := conf.Valid(); err != nil {
		return nil, fmt.Errorf("OrderClientConfiguration invalid, error: %v", err)
	}
	if conf.SignOption == nil {
		conf.SignOption = &auth.SignOptions{
			HeadersToSign: auth.DEFAULT_HEADERS_TO_SIGN,
			ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS,
			IsBce:         true,
		}
	}
	if conf.Retry == nil {
		conf.Retry = bce.DEFAULT_RETRY_POLICY
	}
	bceConf := bce.BceClientConfiguration{
		Endpoint:    conf.Endpoint,
		Credentials: &conf.Credentials,
		SignOption:  conf.SignOption,
		Retry:       conf.Retry,
		Timeout:     conf.Timeout,
		Logger:      conf.Logger,
	}
	bceClient := bce.NewClient(&bceConf)
	client := Client{
		Client: bceClient,
	}
	return &client, nil
}

func (c *Client) UpdateOrder(uuid string, updateOrderRequest *model.UpdateOrderRequest) (order *model.Order, err error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri("/orders/" + uuid)
	bceRequest.SetMethod(http.PUT)
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	dataByte, err := json.Marshal(updateOrderRequest)
	if err != nil {
		return nil, fmt.Errorf("unmarshal data[%v] err: %v", updateOrderRequest, err)
	}
	body, err := bce.NewBodyFromBytes(dataByte)
	if err != nil {
		return nil, fmt.Errorf("NewBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()

	order = &model.Order{}
	if err := resp.ParseJsonBody(order); err != nil {
		return nil, err
	}
	return order, nil
}

func (c *Client) GetOrder(uuid string) (order *model.Order, err error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetCredentials(c.Credentials)
	bceRequest.SetUri(GetOrderApi + "/" + uuid)
	bceRequest.SetMethod(http.GET)
	bceRequest.BuildHttpRequest()
	bceRequest.SetHeader(http.HOST, bceRequest.Host())
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	if len(c.Credentials.SessionToken) != 0 {
		bceRequest.SetHeader(http.BCE_SECURITY_TOKEN, c.Credentials.SessionToken)
	}
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	order = &model.Order{}
	if err := resp.ParseJsonBody(order); err != nil {
		return nil, err
	}
	return order, nil
}

func (c *Client) ListOrders(request *model.ListOrdersRequest) (orders []model.Order, err error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetCredentials(c.Credentials)
	bceRequest.SetUri(ListOrdersApi)
	bceRequest.SetMethod(http.POST)
	bceRequest.SetHeader(http.HOST, bceRequest.Host())
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	if len(c.Credentials.SessionToken) != 0 {
		bceRequest.SetHeader(http.BCE_SECURITY_TOKEN, c.Credentials.SessionToken)
	}
	dataByte, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("marshal data[%v] err: %v", request, err)
	}
	body, err := bce.NewBodyFromBytes(dataByte)
	if err != nil {
		return nil, fmt.Errorf("NewBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	var res model.ListOrdersResponse
	if err := resp.ParseJsonBody(&res); err != nil {
		return nil, err
	}
	return res.Orders, nil
}

func (c *Client) RefundOrder(request *model.RefundOrderRequest) (*model.Order, error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri("/orders")
	bceRequest.SetParam("refund", "true")
	bceRequest.SetMethod(http.POST)
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	dataByte, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("marshal data[%v] err: %v", request, err)
	}
	body, err := bce.NewBodyFromBytes(dataByte)
	if err != nil {
		return nil, fmt.Errorf("NewBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	refundResp := &model.Order{}
	if err := resp.ParseJsonBody(refundResp); err != nil {
		return nil, err
	}
	return refundResp, nil
}

func (c *Client) CreateOrder(request *model.CreateOrderRequest) (*model.Order, error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri("/orders")
	bceRequest.SetMethod(http.POST)
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	dataByte, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("marshal data[%v] err: %v", request, err)
	}
	body, err := bce.NewBodyFromBytes(dataByte)
	if err != nil {
		return nil, fmt.Errorf("NewBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	refundResp := &model.Order{}
	if err := resp.ParseJsonBody(refundResp); err != nil {
		return nil, err
	}
	return refundResp, nil
}
