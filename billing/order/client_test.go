package order

import (
	"encoding/json"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/iam"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/order/model"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/sts"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/testings"
)

func makeClient(t *testing.T, accountId string) *Client {
	iamConf := iam.IamClientConfiguration{
		Endpoint: "http://iam.bj.internal-qasandbox.baidu-int.com",
		UserName: "console_chpc",
		Password: "B1vbpPvAxkpEW2ehYIvjfZnJ5WFgU6H2",
		Domain:   "Default",
	}
	iamCli := iam.NewClient(&iamConf)
	accessKey, err := iamCli.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}
	stsConf := sts.StsClientConfiguration{
		Endpoint: "http://sts.bj.internal-qasandbox.baidu-int.com:8586",
		Credentials: auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
	}
	stsCli, err := sts.NewClient(&stsConf)
	if err != nil {
		t.Fatal(err)
	}
	cred, err := stsCli.AssumeRole(accountId, "BceServiceRole_console_chpc")
	if err != nil {
		t.Fatal(err)
	}
	conf := OrderClientConfiguration{
		Endpoint: "http://order.internal-qasandbox.bce-internal.baidu-int.com",
		Credentials: auth.BceCredentials{
			AccessKeyId:     cred.AccessKeyId,
			SecretAccessKey: cred.AccessKeySecret,
			SessionToken:    cred.SessionToken,
		},
	}
	cli, err := NewClient(&conf)
	if err != nil {
		t.Fatal(err)
	}

	return cli
}

func TestQueryOrder(t *testing.T) {
	accountId := "5c0e1ef53266427ba025ae4eb6ee3a3d"

	cli := makeClient(t, accountId)

	order, err := cli.GetOrder("47224e21129f44b7926b614b5c065c22")
	if err != nil {
		t.Fatal(err)
	}

	str, _ := json.MarshalIndent(order, "", "\t")
	fmt.Println(string(str))
}

func TestUpdateOrder(t *testing.T) {
	accountId := "5c0e1ef53266427ba025ae4eb6ee3a3d"

	cli := makeClient(t, accountId)

	req := model.UpdateOrderRequest{
		OrderStatus: model.CREATED,
	}

	order, err := cli.UpdateOrder("47224e21129f44b7926b614b5c065c22", &req)
	if err != nil {
		t.Fatal(err)
	}

	_ = order
}

func TestListOrders(t *testing.T) {
	accountId := "e0fc06abc4f741cd9a9cdb994f71c59c"
	cli := makeClient(t, accountId)
	req := model.ListOrdersRequest{
		ProductType: "prepay",
		OrderType:   "REFUND",
		Begin:       1,
		Limit:       10,
	}
	orders, err := cli.ListOrders(&req)
	if err != nil {
		t.Fatal(err)
	}
	os := testings.ToJSON(orders)
	fmt.Println(os)
}
