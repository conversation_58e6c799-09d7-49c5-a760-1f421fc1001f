package model

type OrderItem struct {
	Region                  string             `json:"region"`
	LogicalZone             string             `json:"logicalZone"`
	Key                     string             `json:"key"`
	Extra                   string             `json:"extra"`
	UnitPrice               float64            `json:"unitPrice"`
	Price                   float64            `json:"price"`
	PricingDetail           PricingDetail      `json:"pricingDetail"`
	ResourceIdAndFees       []ResourceIdAndFee `json:"resourceIdAndFees"`
	Count                   int                `json:"count"`
	AlterCount              int                `json:"alterCount"`
	Time                    float64            `json:"time"`
	TimeUnit                string             `json:"timeUnit"`
	ChargePlanStartTime     string             `json:"chargePlanStartTime"`
	Flavor                  []Flavor           `json:"flavor"`
	SubItems                []OrderItem        `json:"subItems"`
	ReleaseTime             string             `json:"releaseTime"`
	SecurityGroupId         string             `json:"securityGroupId"`
	Fee                     Fee                `json:"fee"`
	ServiceType             string             `json:"serviceType"`
	CustomPrice             float64            `json:"customPrice"`
	ProductType             string             `json:"productType"`
	UnitPriceShow           string             `json:"unitPriceShow"`
	UnitCount               int                `json:"unitCount"`
	ChargeType              []string           `json:"chargeType"`
	Configuration           []string           `json:"configuration"`
	SubProductType          string             `json:"subProductType"`
	RawUnitPreTimePrice     float64            `json:"rawUnitPreTimePrice"`
	ChargeTime              float64            `json:"chargeTime"`
	PaymentMethod           PaymentMethod      `json:"paymentMethod"`
	ResourceIds             []string           `json:"resourceIds"`
	ShortIds                []string           `json:"shortIds"`
	ResourceStartTime       string             `json:"resourceStartTime"`
	ResourceEndTime         string             `json:"resourceEndTime"`
	Uuid                    string             `json:"uuid"`
	ResourceActiveTime      string             `json:"resourceActiveTime"`
	CombinedServiceType     string             `json:"combinedServiceType"`
	ResourceMappings        []ResourceMapping  `json:"resourceMappings"`
	DiscountFlag            bool               `json:"discountFlag"`
	DiscountWithCouponPrice float64            `json:"discountWithCouponPrice"`
	BidModel                string             `json:"bidModel"`
	BidPrice                float64            `json:"bidPrice"`
}

type PricingDetail struct {
	Cpt2Price      Cpt2Price      `json:"cpt2Price"`
	Cpt1Price      float64        `json:"cpt1Price"`
	Policy         string         `json:"policy"`
	AlterPriceList AlterPriceList `json:"alterPriceList"`
}

type Cpt2Price struct {
	PreTimePrice float64 `json:"preTimePrice"`
	ChargeTime   int     `json:"chargeTime"`
}

type AlterPriceList struct {
	AlterPrices []AlterPrice `json:"alterPrices"`
}

type AlterPrice struct {
	Num   int     `json:"num"`
	Price float64 `json:"price"`
}

type ResourceIdAndFee struct {
	ResourceId string `json:"resourceId"`
	Fee        Fee    `json:"fee"`
}

type ResourceMapping struct {
	Key         string         `json:"key"`
	Id          string         `json:"id"`
	ShortId     string         `json:"shortId,omitempty"`
	Status      ResourceStatus `json:"status"`
	ExpiredTime string         `json:"expiredTime"`
}

type Fee struct {
	/**定价(不考虑折扣和代金券优惠)**/
	Price float64 `json:"price"`

	/**目录价**/
	CatalogPrice float64 `json:"catalogPrice"`

	/**实付现金额度**/
	Cash float64 `json:"cash"`

	/**实付代金券额度**/
	Coupon float64 `json:"coupon"`

	/**实付折扣券额度**/
	DiscountCoupon float64 `json:"discountCoupon"`

	/**折扣额度**/
	DiscountAmount float64 `json:"discountAmount"`

	/**支付给代理商的返点额度**/
	RebateOut float64 `json:"rebateOut"`

	/**代理商自己消费的返点额度**/
	RebateIn float64 `json:"rebateIn"`

	/**代理商代付现金额度**/
	AgentCash float64 `json:"agentCash"`

	/**代理商代付返点额度**/
	AgentRebateIn float64 `json:"agentRebateIn"`

	/** 现金等价物
	 * 最终为 cash + rebateIn + agentCash + agentRebateIn
	 */
	CashEquivalents float64 `json:"cashEquivalents"`

	/**
	 * 实际退现金额，当Finance无法退出cash这么多金额时，此字段记录实际能退的额度，正常情况下为null
	 */
	RefundCash float64 `json:"refundCash"`

	/**
	 * 实际退代金券额，当Finance无法退出coupon这么多金额时，此字段记录实际能退的额度，正常情况下为null
	 */
	RefundCoupon float64 `json:"refundCoupon"`

	/**退款时计收的手续费，其中现金部分**/
	CashFee float64 `json:"cashFee"`

	/**退款时计收的手续费，其中代金券部分**/
	CouponFee float64 `json:"couponFee"`

	/**退款时计收的手续费，其中返点部分**/
	RebateInFee float64 `json:"rebateInFee"`

	/**退款时计收的手续费，其中代付现金部分**/
	AgentCashFee float64 `json:"agentCashFee"`

	/**退款时计收的手续费，其中代付返点部分**/
	AgentRebateInFee float64 `json:"agentRebateInFee"`
}

type PaymentMethod struct {
	DiscountRate    int          `json:"discountRate"`
	Coupons         []CouponInfo `json:"coupons"`
	DiscountCoupons []CouponInfo `json:"discountCoupons"`
}

type CouponInfo struct {
	CouponId string  `json:"couponId"`
	Amount   float64 `json:"amount"`
}

/*type Flavor struct {
	flavor Flavors `json:"flavor"`
}
*/

type Flavor struct {
	Name  string  `json:"name"`
	Value string  `json:"value"`
	Scale float64 `json:"scale"`
}

type OrderUuidResult struct {
	OrderId string `json:"orderId,omitempty"`
}

type UpdateOrderRequest struct {
	OrderStatus     OrderStatus              `json:"status"` // 订单执行器里更新
	AgentPayStatus  AgentPayStatus           `json:"agentPayStatus,omitempty"`
	AgentPayComment string                   `json:"agentPayComment,omitempty"`
	PurchaseTime    string                   `json:"purchaseTime,omitempty"`
	FailReason      string                   `json:"failReason,omitempty"`
	ServiceType     string                   `json:"serviceType,omitempty"`
	FavourablePrice float64                  `json:"favourablePrice,omitempty"`
	Price           float64                  `json:"price,omitempty"`
	Discount        int                      `json:"discount,omitempty"`
	WhatIf          bool                     `json:"whatIf,omitempty"`
	Items           []OrderItemUpdateRequest `json:"items,omitempty"`
	Resources       []ResourceMapping        `json:"resources"` // 订单执行器里更新
}

type OrderItemUpdateRequest struct {
	Key           string              `json:"key"`
	Flavor        []ChargeItem        `json:"flavor"`
	PaymentMethod PaymentMethod       `json:"paymentMethod"`
	PricingDetail PricingPolicyHolder `json:"pricingDetail"`
}

type PricingPolicyHolder struct {
	Policy string `json:"policy"`
}
type ChargeItem struct {
	Name  string  `json:"name"`
	Value string  `json:"value"`
	Scale float64 `json:"scale"`
}

type Order struct {
	BaseOrderId      string         `json:"baseOrderId"`
	Uuid             string         `json:"uuid"`
	Type             string         `json:"type"`
	PackageId        string         `json:"packageId"`
	PayAgentId       string         `json:"payAgentId"`
	UserId           string         `json:"userId"`
	ServiceType      string         `json:"serviceType"`
	ProductType      string         `json:"productType"`
	SubProductType   string         `json:"subProductType"`
	Items            []OrderItem    `json:"items"`
	AccountId        string         `json:"accountId"`
	Fee              Fee            `json:"fee"`
	Price            float64        `json:"price"`
	Status           OrderStatus    `json:"status"`
	CreateTime       string         `json:"createTime"`
	PurchaseTime     string         `json:"purchaseTime"`
	UpdateTime       string         `json:"updateTime"`
	ActiveTime       string         `json:"activeTime"`
	ResourceIds      []string       `json:"resourceIds"`
	RebateOutAgentId string         `json:"rebateOutAgentId"`
	PayChannel       PayChannel     `json:"payChannel"`
	SpecificType     int            `json:"specificType"`
	BatchId          int64          `json:"batchId"`
	Source           string         `json:"source"`
	AgentPayStatus   AgentPayStatus `json:"agentPayStatus"`
	AgentPayComment  string         `json:"agentPayComment"`
	PayExpireTime    string         `json:"payExpireTime"`
	CampaignId       string         `json:"campaignId"`
	Remark           string         `json:"remark"`
}

type ListOrdersRequest struct {
	AccountId string `json:"accountId,omitempty"` // 用户id
	OrderType string `json:"orderType,omitempty"` // 订单类型。订单类型包括:NEW,RENEW,SHIFT_CHARGE,DILATATION,SHRINKAGE,RESIZE,REFUND,TRANSFER_IN,TO_PREPAY, TO_POSTPAY
	Status    string `json:"status,omitempty"`    // 订单状态。包括NEED_PURCHASE,CANCELLED, DEFERRED_CREATE, READY_FOR_CREATE,CREATING,CREATED,CREATE_FAILED, EXPIRED,REFUND_SUCC,REFUND_FAILED
	// StatusList  []string `json:"statusList"`  // 待筛选的订单状态的列表（该字段只有 advancedList 接口里可用， simpleQueryList 接口里不可用）
	ServiceType string `json:"serviceType,omitempty"` // 产品类型
	ProductType string `json:"productType,omitempty"` // 按productType字段查询订单
	StartTime   string `json:"startTime,omitempty"`   // 订单创建时间下限
	EndTime     string `json:"endTime,omitempty"`     // 订单创建时间上限
	Begin       int    `json:"begin"`                 // 从第begin条数据开始返回(从1开始计数)
	Limit       int    `json:"limit"`                 // 每次查询返回的结果数目上限
}

type ListOrdersResponse struct {
	Size   int     `json:"size"`
	Begin  int     `json:"begin"`
	Orders []Order `json:"orders"`
}

type CreateOrderRequest struct {
	AccountId      string            `json:"accountId"`             // 订单所属用户(支出账号)uuid
	UserId         string            `json:"userId,omitempty"`      // 用户/操作人的IAM账号id (可选)
	ServiceType    string            `json:"serviceType"`           // 产品类型
	ProductType    string            `json:"productType"`           // 产品付费类型
	SubProductType string            `json:"subProductType"`        // 产品子付费类型
	Source         string            `json:"source,omitempty"`      // 订单来源, 空串(或不传)代表console创建
	CampaignId     string            `json:"campaignId,omitempty"`  // 活动订单需要填写(如果需要发活动返点，必须在此输入对应的活动id)
	ClientToken    string            `json:"clientToken,omitempty"` // 业务幂等标识，代表本次下单行为
	Items          []CreateOrderItem `json:"items"`                 // 订单项
}

type CreateOrderItem struct {
	Key                 string          `json:"key,omitempty"`            // 用于区分订单项的标识。同一订单的多个订单项必须使用不同的key值。(必填)
	ServiceType         string          `json:"serviceType"`              // 服务类型(必填)
	ProductType         string          `json:"productType"`              // 产品类型，prepay，postpay（必填）
	SubProductType      string          `json:"subProductType,omitempty"` // 子产品类型，project，package等
	Region              string          `json:"region"`                   // 产品地域. cn-n1/bj/hk/gz/global选其一
	Flavor              []Flavor        `json:"flavor,omitempty"`         // 产品的配置, 将影响订单的价格
	Extra               string          `json:"extra,omitempty"`          // 杂项，供业务方使用
	Time                int64           `json:"time,omitempty"`           // 表示购买的资源时长。subProductType为project时time必须为1。(预付费订单必填)
	TimeUnit            string          `json:"timeUnit,omitempty"`       // 预付费订单购买时长的单位, 默认为MONTH. 可选值(MILLISECOND/SECOND/MINUTE/HOUR/DAY/WEEK/MONTH/YEAR)
	PricingDetail       []PricingDetail `json:"pricingDetail,omitempty"`  // （仅在item_campaign类活动订单生效）订单item的优惠方式，用来设置billing优惠策略
	ChargePlanStartTime string          `json:"chargePlanStartTime"`      // 用于设置charge plan开始时间，用于指定计收时间
	ResourceActiveTime  string          `json:"resourceActiveTime"`       // 用于设置资源激活时间。在生成资源的时候，资源信息里的 startTime 会使用这个值，量包抵扣的时候，会根据这个值决定啥时候的用量可以抵扣
	Count               int             `json:"count,omitempty"`          // 购买该配置资源实例个数(必填，最小值为1)
	CustomPrice         float64         `json:"customPrice,omitempty"`    // 自定义价格
	DiscountFlag        bool            `json:"discountFlag,omitempty"`   // 是否能通过billing使用折扣
}
