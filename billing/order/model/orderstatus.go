package model

import (
	"bytes"
	"encoding/json"
)

type OrderStatus int

// iota 初始化后会自动递增
const (
	NEED_PURCHASE OrderStatus = iota
	NEED_CONFIRM
	CONFIRMED
	CANCELLED
	// 以下状态总体上只有order和order-execute能设置

	DEFERRED_CREATE
	READY_FOR_CREATE
	CREATING
	CREATED
	CREATE_FAILED
	EXPIRED
	REFUND_SUCC
	REFUND_FAILED
	FINISHED

	// 千秋新状态
	CANCELLING
	EXPIRING
	NEED_APPROVE
	APPROVING
	APPROVED
	APPROVE_FAILED
)

func (s OrderStatus) String() string {
	switch s {
	case NEED_PURCHASE:
		return "NEED_PURCHASE"
	case NEED_CONFIRM:
		return "NEED_CONFIRM"
	case CONFIRMED:
		return "CONFIRMED"
	case CANCELLED:
		return "CANCELLED"
	case DEFERRED_CREATE:
		return "DEFERRED_CREATE"
	case READY_FOR_CREATE:
		return "READY_FOR_CREATE"
	case CREATING:
		return "CREATING"
	case CREATED:
		return "CREATED"
	case CREATE_FAILED:
		return "CREATE_FAILED"
	case EXPIRED:
		return "EXPIRED"
	case REFUND_SUCC:
		return "REFUND_SUCC"
	case REFUND_FAILED:
		return "REFUND_FAILED"
	case FINISHED:
		return "FINISHED"
	case CANCELLING:
		return "CANCELLING"
	case EXPIRING:
		return "EXPIRING"
	case NEED_APPROVE:
		return "NEED_APPROVE"
	case APPROVING:
		return "APPROVING"
	case APPROVED:
		return "APPROVED"
	case APPROVE_FAILED:
		return "APPROVE_FAILED"
	default:
		return "Unknow"
	}
}

var toID = map[string]OrderStatus{
	"NEED_PURCHASE":    NEED_PURCHASE,
	"NEED_CONFIRM":     NEED_CONFIRM,
	"CONFIRMED":        CONFIRMED,
	"CANCELLED":        CANCELLED,
	"DEFERRED_CREATE":  DEFERRED_CREATE,
	"READY_FOR_CREATE": READY_FOR_CREATE,
	"CREATING":         CREATING,
	"CREATED":          CREATED,
	"CREATE_FAILED":    CREATE_FAILED,
	"EXPIRED":          EXPIRED,
	"REFUND_SUCC":      REFUND_SUCC,
	"REFUND_FAILED":    REFUND_FAILED,
	"FINISHED":         FINISHED,
	"CANCELLING":       CANCELLING,
	"EXPIRING":         EXPIRING,
	"NEED_APPROVE":     NEED_APPROVE,
	"APPROVING":        APPROVING,
	"APPROVED":         APPROVED,
	"APPROVE_FAILED":   APPROVE_FAILED,
}

func (s OrderStatus) MarshalJSON() ([]byte, error) {
	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(s.String())
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

// UnmarshalJSON unmashals a quoted json string to the enum value
func (s *OrderStatus) UnmarshalJSON(b []byte) error {
	var j string
	err := json.Unmarshal(b, &j)
	if err != nil {
		return err
	}
	// Note that if the string cannot be found then it will be set to the zero value, 'Created' in this case.
	*s = toID[j]
	return nil
}

type PayChannel int

const (
	CASH   PayChannel = iota // 现金支付
	CREDIT                   // 账期支付
)

var PayChannelToID = map[string]PayChannel{
	"CASH":   CASH,
	"CREDIT": CREDIT,
}

func (c PayChannel) String() string {
	switch c {
	case CASH:
		return "CASH"
	case CREDIT:
		return "CREDIT"
	default:
		return "Unknow"
	}
}

func (s PayChannel) MarshalJSON() ([]byte, error) {
	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(s.String())
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

// UnmarshalJSON unmashals a quoted json string to the enum value
func (s *PayChannel) UnmarshalJSON(b []byte) error {
	var j string
	err := json.Unmarshal(b, &j)
	if err != nil {
		return err
	}
	// Note that if the string cannot be found then it will be set to the zero value, 'Created' in this case.
	*s = PayChannelToID[j]
	return nil
}
