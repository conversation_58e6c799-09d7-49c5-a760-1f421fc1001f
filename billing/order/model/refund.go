package model

const (
	ReleasePolicyImmediately = "RESOURCE_REFUND_IMMEDIATELY" // 资源退款，立即释放
	ReleasePolicyRecycle     = "RESOURCE_REFUND"             // 资源退款，进回收站

	RefundTypeAll     = "ALL"             // 全生命周期
	RefundTypePart    = "PART"            // 部分生命周期
	RefundTypePackage = "PACKAGE"         // 量包
	RefundTypeStorage = "STORAGE_PACKAGE" // 存储包
)

type RefundOrderRequest struct {
	Uuid             string `json:"uuid"`                 // 资源uuid
	UserId           string `json:"userId"`               // 用户/操作人的iam账号id
	FeeRate          int    `json:"feeRate"`              // 退款手续费,默认50%
	RefundReason     string `json:"refundReason"`         // 退款原因
	Extra            string `json:"extra"`                // 杂项
	Source           string `json:"source"`               // 退款来源，默认是console创建
	ReleasePolicy    string `json:"releasePolicy"`        // 资源释放策略
	RefundType       string `json:"refundType,omitempty"` // 退款类型，使用此值必须联系billing
	ExpectExpireTime string `json:"expectExpireTime"`     // 期望退款时间,dateTime
}
