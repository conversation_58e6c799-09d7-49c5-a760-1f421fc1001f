package model

import (
	"bytes"
	"encoding/json"
)

type ResourceDestroyRequest struct {
	AccountId string           `json:"accountId"`
	Identify  ResourceIdentify `json:"identify"`
}

type ResourceIdentify struct {
	ServiceType string `json:"serviceType"`
	Region      string `json:"region"`
	Name        string `json:"name"`
}

type Response struct {
	Status ResultStatus `json:"status"`
	Time   string       `json:"time"`
}

type ResultStatus int

const (
	DONE ResultStatus = iota
	DOING
	SUCCESS
	FAILURE
)

var ResultStatusToID = map[string]ResultStatus{
	"DONE":    DONE,
	"DOING":   DOING,
	"SUCCESS": SUCCESS,
	"FAILURE": FAILURE,
}

func (s ResultStatus) String() string {
	switch s {
	case DONE:
		return "DONE"
	case DOING:
		return "DOING"
	case SUCCESS:
		return "SUCCESS"
	case FAILURE:
		return "FAILURE"
	default:
		return "Unknow"
	}
}

func (s ResultStatus) MarshalJSON() ([]byte, error) {
	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(s.String())
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

// UnmarshalJSON unmashals a quoted json string to the enum value
func (s *ResultStatus) UnmarshalJSON(b []byte) error {
	var j string
	err := json.Unmarshal(b, &j)
	if err != nil {
		return err
	}
	// Note that if the string cannot be found then it will be set to the zero value, 'Created' in this case.
	*s = ResultStatusToID[j]
	return nil
}
