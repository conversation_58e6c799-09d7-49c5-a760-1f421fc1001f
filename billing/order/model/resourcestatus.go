package model

import (
	"bytes"
	"encoding/json"
)

type ResourceStatus int

// iota 初始化后会自动递增
const (
	INIT ResourceStatus = iota
	RUNNING
	PAUSED
	STOPPED
	DESTROYED
	UNKNOWN
	CLEAR
	SCRAP
	HOLD
	DESTROYING
)

func (s ResourceStatus) String() string {
	switch s {
	case INIT:
		return "INIT"
	case RUNNING:
		return "RUNNING"
	case PAUSED:
		return "PAUSED"
	case STOPPED:
		return "STOPPED"
	case DESTROYED:
		return "DESTROYED"
	case UNKNOWN:
		return "UNKNOWN"
	case CLEAR:
		return "CLEAR"
	case SCRAP:
		return "SCRAP"
	case HOLD:
		return "HOLD"
	case DESTROYING:
		return "DESTROYING"
	default:
		return "Unknow"
	}
}

var ResourceStatusToID = map[string]ResourceStatus{
	"INIT":       INIT,
	"RUNNING":    RUNNING,
	"PAUSED":     PAUSED,
	"STOPPED":    STOPPED,
	"DESTROYED":  DESTROYED,
	"UNKNOWN":    UNKNOW<PERSON>,
	"<PERSON><PERSON><PERSON>":      <PERSON><PERSON><PERSON>,
	"SCRAP":      SCRAP,
	"HOLD":       HOLD,
	"DESTROYING": DESTROYING,
}

func (s ResourceStatus) MarshalJSON() ([]byte, error) {
	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(s.String())
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

// UnmarshalJSON unmashals a quoted json string to the enum value
func (s *ResourceStatus) UnmarshalJSON(b []byte) error {
	var j string
	err := json.Unmarshal(b, &j)
	if err != nil {
		return err
	}
	// Note that if the string cannot be found then it will be set to the zero value, 'Created' in this case.
	*s = ResourceStatusToID[j]
	return nil
}

type AgentPayStatus int

const (
	AGENT_PAY_ASK    AgentPayStatus = iota // 请求代理商代付
	AGENT_PAY_ACCEPT                       // 代理商接受代付
	AGENT_PAY_DENY                         // 代理商拒绝支付
)

func (s AgentPayStatus) String() string {
	switch s {
	case AGENT_PAY_ASK:
		return "AGENT_PAY_ASK"
	case AGENT_PAY_ACCEPT:
		return "AGENT_PAY_ACCEPT"
	case AGENT_PAY_DENY:
		return "AGENT_PAY_DENY"
	default:
		return "Unknow"
	}
}

var AgentPayStatusToID = map[string]AgentPayStatus{
	"AGENT_PAY_ASK":    AGENT_PAY_ASK,
	"AGENT_PAY_ACCEPT": AGENT_PAY_ACCEPT,
	"AGENT_PAY_DENY":   AGENT_PAY_DENY,
}

func (s AgentPayStatus) MarshalJSON() ([]byte, error) {
	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(s.String())
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

// UnmarshalJSON unmashals a quoted json string to the enum value
func (s *AgentPayStatus) UnmarshalJSON(b []byte) error {
	var j string
	err := json.Unmarshal(b, &j)
	if err != nil {
		return err
	}
	// Note that if the string cannot be found then it will be set to the zero value, 'Created' in this case.
	*s = AgentPayStatusToID[j]
	return nil
}
