package order

import (
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/http"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/orderfacade/model"
)

type Client struct {
	*bce.Client
}

func NewClient(conf *OrderFacadeClientConfiguration) (*Client, error) {
	if err := conf.Valid(); err != nil {
		return nil, fmt.Errorf("OrderFacadeClientConfiguration invalid, error: %v", err)
	}
	if conf.SignOption == nil {
		conf.SignOption = &auth.SignOptions{
			HeadersToSign: auth.DEFAULT_HEADERS_TO_SIGN,
			ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS,
			IsBce:         true,
		}
	}
	if conf.Retry == nil {
		conf.Retry = bce.DEFAULT_RETRY_POLICY
	}
	bceConf := bce.BceClientConfiguration{
		Endpoint:    conf.Endpoint,
		Credentials: &conf.Credentials,
		SignOption:  conf.SignOption,
		Retry:       conf.Retry,
		Timeout:     conf.Timeout,
		Logger:      conf.Logger,
	}
	bceClient := bce.NewClient(&bceConf)
	client := Client{
		Client: bceClient,
	}
	return &client, nil
}

func (c *Client) CreateOrder(orderCreateRequest *model.CreateOrderRequest) (orderUuidResult *model.OrderUuidResult, err error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri(CreateOrderApi)
	bceRequest.SetMethod(http.POST)
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	dataByte, err := json.Marshal(orderCreateRequest)
	if err != nil {
		return nil, fmt.Errorf("unmarshal data[%v] err: %v", orderCreateRequest, err)
	}
	body, err := bce.NewBodyFromBytes(dataByte)
	if err != nil {
		return nil, fmt.Errorf("NewBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	orderUuidResult = &model.OrderUuidResult{}
	if err := resp.ParseJsonBody(orderUuidResult); err != nil {
		return nil, err
	}
	return orderUuidResult, nil
}
