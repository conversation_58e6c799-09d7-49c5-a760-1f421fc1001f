package order

import (
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/util"
)

const (
	CreateOrderApi = "/v1/order"
)

type OrderFacadeClientConfiguration struct {
	Endpoint    string
	Retry       bce.RetryPolicy
	SignOption  *auth.SignOptions
	Credentials auth.BceCredentials
	Timeout     int
	Logger      util.Logger
}

func (c *OrderFacadeClientConfiguration) Valid() error {
	if len(c.Endpoint) == 0 {
		return fmt.Errorf("missing endpoint")
	}
	if len(c.Credentials.AccessKeyId) == 0 ||
		len(c.Credentials.SecretAccessKey) == 0 ||
		len(c.Credentials.SessionToken) == 0 {
		return fmt.Errorf("invalid credentials")
	}
	return nil
}
