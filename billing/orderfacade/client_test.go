package order

import (
	"fmt"
	"testing"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/iam"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/orderfacade/model"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/sts"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/testings"
)

func makeClient(t *testing.T, accountId string) *Client {
	iamConf := iam.IamClientConfiguration{
		// Endpoint: "http://iam.bj.internal-qasandbox.baidu-int.com",
		// UserName: "console_chpc",
		// Password: "B1vbpPvAxkpEW2ehYIvjfZnJ5WFgU6H2",
		Endpoint: "http://iam.bj.bce-internal.baidu.com:80",
		UserName: "console_chpc",
		Password: "1aP5ZRTrFjV1wQkOsz9eZf9u1sLsEujV",

		Domain: "Default",
	}
	iamCli := iam.NewClient(&iamConf)
	accessKey, err := iamCli.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}
	stsConf := sts.StsClientConfiguration{
		// Endpoint: "http://sts.bj.internal-qasandbox.baidu-int.com:8586",
		Endpoint: "http://sts.bj.iam.sdns.baidu.com:8586",

		Credentials: auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
	}
	stsCli, err := sts.NewClient(&stsConf)
	if err != nil {
		t.Fatal(err)
	}
	cred, err := stsCli.AssumeRole(accountId, "BceServiceRole_console_chpc")
	if err != nil {
		t.Fatal(err)
	}
	conf := OrderFacadeClientConfiguration{
		// Endpoint: "http://facade.internal-qasandbox.baidu-int.com:8993",
		Endpoint: "http://orderfacade.bce-console.sdns.baidu.com",

		Credentials: auth.BceCredentials{
			AccessKeyId:     cred.AccessKeyId,
			SecretAccessKey: cred.AccessKeySecret,
			SessionToken:    cred.SessionToken,
		},
		Retry: bce.NewNoRetryPolicy(),
	}
	cli, err := NewClient(&conf)
	if err != nil {
		t.Fatal(err)
	}

	return cli
}

func TestCreateOrder(t *testing.T) {
	accountId := "1545b9ab1a0f46b2bf5ebf32461987fe"

	cli := makeClient(t, accountId)
	charge := 100
	jobChargeAmount := float64(charge) / 0.*********

	item := model.CreateOrderItem{ // 预付费
		ServiceType:    "CHPC",
		ProductType:    "prepay",
		SubProductType: "project",
		Key:            "helixfold3-service",
		Count:          1,
		Flavor: []model.Flavor{
			{
				Name:  "subServiceType",
				Value: "HelixFold3",
				Scale: 1,
			},
			{
				Name:  "version",
				Value: fmt.Sprintf("%f", jobChargeAmount),
				Scale: 1,
			},
		},
		Time:     1,
		TimeUnit: "DAY",
		Extra:    "paddlehelix_infer_12345",
	}
	// item := model.CreateOrderItem{ // 后付费
	// 	ServiceType: "XUPERASSET",
	// 	ProductType: "postpay",
	// 	Key:         uuid.NewString(),
	// 	Count:       1,
	// 	Flavor:      []model.Flavor{},
	// }
	// item := model.CreateOrderItem{ // 量包
	// 	ServiceType:    "XUPERASSET",
	// 	ProductType:    "prepay",
	// 	SubProductType: "package",
	// 	Key:            uuid.NewString(),
	// 	Count:          1,
	// 	Time:           1,
	// 	TimeUnit:       "YEAR",
	// 	// ResourceActiveTime: time.Now().AddDaste(0, 1, 0).Format(time.RFC3339),
	// 	Flavor: []model.Flavor{
	// 		{
	// 			Name:  "subServiceType",
	// 			Value: "BDY-DJFWF-LB",
	// 			Scale: 1,
	// 		},
	// 		{
	// 			Name:  "Specification_0",
	// 			Value: "1WCB",
	// 			Scale: 1,
	// 		},
	// 		{
	// 			Name:  "deductPolicy", // 缺少这项量包抵扣会失败
	// 			Value: "BDY-DJFWF-LB",
	// 			Scale: 1,
	// 		},
	// 	},
	// }
	req := model.CreateOrderRequest{
		Region:      "global",
		OrderType:   model.OrederTypeNew,
		Items:       []model.CreateOrderItem{item},
		IsDirectPay: true,
	}

	fmt.Println(testings.ToJSON(req))

	res, err := cli.CreateOrder(&req)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("orderId:", res.OrderId)
}

func TestCreateOrderPGC(t *testing.T) {
	accountId := "d028fb5cc79142cdb3a580e068d3a06f"

	cli := makeClient(t, accountId)

	item := model.CreateOrderItem{
		ServiceType:    "XUPERASSET",
		ProductType:    "prepay",
		SubProductType: "", // pgc 没有 SubProductType
		Key:            uuid.NewString(),
		Count:          1,
		Time:           1,
		TimeUnit:       "YEAR",
		Flavor: []model.Flavor{
			{
				Name:  "subServiceType",
				Value: "SZZPDPFW",
				Scale: 1,
			},
			{
				Name:  "Version_0",
				Value: "JCB",
				Scale: 1,
			},
		},
	}
	req := model.CreateOrderRequest{
		Region:    "global",
		OrderType: model.OrederTypeNew,
		Items:     []model.CreateOrderItem{item},
	}

	fmt.Println(testings.ToJSON(req))

	res, err := cli.CreateOrder(&req)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("orderId:", res.OrderId)
}
