package model

type OrderType string

const (
	NEW = iota
	RENEW
	RESIZE
	DILATATION
	SHRINKAGE
	SHIFT_CHARGE
	TRANSFER_IN
	TO_PREPAY
	TO_POSTPAY

	OrederTypeNew         OrderType = "NEW"
	OrederTypeRenew       OrderType = "RENEW"
	OrederTypeResize      OrderType = "RESIZE"
	OrederTypeDilatation  OrderType = "DILATATION"
	OrederTypeShrinkage   OrderType = "SHRINKAGE"
	OrederTypeShiftCharge OrderType = "SHIFT_CHARGE"
	OrederTypeTransferIn  OrderType = "TRANSFER_IN"
	OrederTypeToPrepay    OrderType = "TO_PREPAY"
	OrederTypeToPostpay   OrderType = "TO_POSTPAY"
)

var OrderTypeID = map[OrderType]int{
	OrederTypeNew:         NEW,
	OrederTypeRenew:       RENEW,
	OrederTypeResize:      RESIZE,
	OrederTypeDilatation:  DILATATION,
	OrederTypeShrinkage:   SHRI<PERSON><PERSON>GE,
	OrederTypeShiftCharge: SHIFT_CHARGE,
	OrederTypeTransferIn:  TRANSFER_IN,
	OrederTypeToPrepay:    TO_PREPAY,
	OrederTypeToPostpay:   TO_POSTPAY,
}

type CreateOrderRequest struct {
	OrderType             OrderType               `json:"orderType"`
	Region                string                  `json:"region"`
	PaymentMethod         []PaymentModel          `json:"paymentMethod,omitempty"`
	Items                 []CreateOrderItem       `json:"items,omitempty"`
	IsDirectPay           bool                    `json:"isDirectPay"`
	UnionExpireOrderFlag  int                     `json:"unionExpireOrderFlag"`
	SubOrderType          string                  `json:"subOrderType"`
	DirectPayCouponConfig []DirectPayCouponConfig `json:"directPayCouponConfig"`
	Source                string                  `json:"source"`
	Key                   string                  `json:"key"`
	Total                 int                     `json:"total"`
	TicketId              string                  `json:"ticketId"`
	ClientToken           string                  `json:"clientToken"`
}

type DirectPayCouponConfig struct {
	Type       string `json:"type"`
	ScopeValue string `json:"scopeValue"`
}

type CreateOrderItem struct {
	ServiceType         string          `json:"serviceType"`             // 服务类型(必填)
	ProductType         string          `json:"productType"`             // 产品类型，prepay，postpay（必填）
	SubProductType      string          `json:"subProductType"`          // 子产品类型，project，package等
	SpecificType        int             `json:"specificType"`            // 特殊订单标识
	ChargePlanStartTime string          `json:"chargePlanStartTime"`     // 用于设置charge plan开始时间，用于指定计收时间
	ResourceActiveTime  string          `json:"resourceActiveTime"`      // 用于设置资源激活时间。在生成资源的时候，资源信息里的 startTime 会使用这个值，量包抵扣的时候，会根据这个值决定啥时候的用量可以抵扣
	PaymentMethod       []PaymentModel  `json:"paymentMethod,omitempty"` // 订单item的支付方式，用来设置代金券。 {"type":"coupon","values":["14556"]}
	PricingDetail       []PricingDetail `json:"pricingDetail,omitempty"` // （仅在item_campaign类活动订单生效）订单item的优惠方式，用来设置billing优惠策略
	PurchaseOrder       int             `json:"purchaseOrder"`           // 用户的购买顺序，如果没有传这个值，表示按照订单默认扣费顺序
	CustomPrice         float64         `json:"customPrice,omitempty"`   // 自定义价格

	// new
	Key      string   `json:"key,omitempty"`      // 用于区分订单项的标识。同一订单的多个订单项必须使用不同的key值。(必填)
	Extra    string   `json:"extra,omitempty"`    // 杂项，供业务方使用
	Count    int      `json:"count,omitempty"`    // 购买该配置资源实例个数(必填，最小值为1)
	Flavor   []Flavor `json:"flavor,omitempty"`   // 产品的配置, 将影响订单的价格
	Time     int64    `json:"time,omitempty"`     // 表示购买的资源时长。subProductType为project时time必须为1。(预付费订单必填)
	TimeUnit string   `json:"timeUnit,omitempty"` // 预付费订单购买时长的单位, 默认为MONTH. 可选值(MILLISECOND/SECOND/MINUTE/HOUR/DAY/WEEK/MONTH/YEAR)

	// renew
	// ResourceUuid string `json:"resourceUuid,omitempty"` // 资源uuid (billing的资源uuid，不是服务方的资源长id)(必填)
	// Time     int64    `json:"time,omitempty"`	// 续费时长(必填)
	// TimeUnit string   `json:"timeUnit,omitempty"`

	// resize
	// ResourceUuid string `json:"resourceUuid,omitempty"`
	// Extra    string   `json:"extra,omitempty"`
	// ResizeType int `json:"resizeType,omitempty"` // 预付费（0升配、1降配、2退款），后付费（null变配
	// Flavor   []Flavor `json:"flavor,omitempty"`

	// shiftcharge
	// ResourceUuid string `json:"resourceUuid,omitempty"`
	// Extra    string   `json:"extra,omitempty"`
	// Flavor   []Flavor `json:"flavor,omitempty"`

	// topostpay
	// ResourceUuid string `json:"resourceUuid,omitempty"`
	// Extra    string   `json:"extra,omitempty"`
	// Flavor   []Flavor `json:"flavor,omitempty"`

	// toprepare
	// ResourceUuid string `json:"resourceUuid,omitempty"`
	// Time     int64    `json:"time,omitempty"`
	// TimeUnit string   `json:"timeUnit,omitempty"`
	// Extra    string   `json:"extra,omitempty"`
	// Flavor   []Flavor `json:"flavor,omitempty"`
}

type PaymentModel struct {
	Type   string   `json:"type"`
	Values []string `json:"values"`
}

type PricingDetail struct {
	Type   string   `json:"type"`
	Values []string `json:"values"` // 这里为空时，要创建一个空数组，不能是 nil
}

type Flavor struct {
	Name  string  `json:"name"`
	Value string  `json:"value"`
	Scale float64 `json:"scale"`
}

type OrderUuidResult struct {
	OrderId string `json:"orderId,omitempty"`
}
