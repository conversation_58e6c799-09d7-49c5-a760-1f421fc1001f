package packagesvc

import (
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/http"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/packagesvc/model"
)

type Client struct {
	*bce.Client
}

func NewClient(conf *PackageSvcClientConfiguration) (*Client, error) {
	if err := conf.Valid(); err != nil {
		return nil, fmt.Errorf("PackageSvcClientConfiguration invalid, error: %v", err)
	}
	if conf.SignOption == nil {
		conf.SignOption = &auth.SignOptions{
			HeadersToSign: auth.DEFAULT_HEADERS_TO_SIGN,
			ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS,
			IsBce:         true,
		}
	}
	if conf.Retry == nil {
		conf.Retry = bce.DEFAULT_RETRY_POLICY
	}
	bceConf := bce.BceClientConfiguration{
		Endpoint:    conf.Endpoint,
		Credentials: &conf.Credentials,
		SignOption:  conf.SignOption,
		Retry:       conf.Retry,
		Timeout:     conf.Timeout,
		Logger:      conf.Logger,
	}
	bceClient := bce.NewClient(&bceConf)
	client := Client{
		Client: bceClient,
	}
	return &client, nil
}

// CreatePackages 创建量包订单
//  仅适用于次数量包，其他规格量包需要针对性调整
//  需要确保各订单项的flavor中存在“count”项
func (c *Client) CreatePackages(request *model.CreatePackageRequest) ([]model.PackageInfo, error) {
	// 检查 order 的 flavor，必须有 count 项
	if err := checkFlavor4PackageCount(request); err != nil {
		return nil, err
	}

	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri(PackageCreateApi)
	bceRequest.SetMethod(http.POST)
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	dataByte, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("unmarshal data[%v] err: %v", request, err)
	}
	body, err := bce.NewBodyFromBytes(dataByte)
	if err != nil {
		return nil, fmt.Errorf("NewBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()

	packageInfos := []model.PackageInfo{}
	if err := resp.ParseJsonBody(&packageInfos); err != nil {
		return nil, err
	}
	return packageInfos, nil
}

func (c *Client) GetPackagesByAccount(request *model.AcctPackagesOptions) (*model.AcctPackageResponse, error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri("/package/getByServiceTypeV3")
	bceRequest.SetMethod(http.POST)
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	dataByte, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("unmarshal data[%v] err: %v", request, err)
	}
	body, err := bce.NewBodyFromBytes(dataByte)
	if err != nil {
		return nil, fmt.Errorf("NewBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()

	packageInfos := &model.AcctPackageResponse{}
	if err := resp.ParseJsonBody(packageInfos); err != nil {
		return nil, err
	}
	return packageInfos, nil
}

func (c *Client) GetPackagesByOrder(request *model.OrderPackagesOptions) ([]model.AcctPackageInfo, error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri("/package/queryPackageName")
	bceRequest.SetMethod(http.POST)
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	dataByte, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("unmarshal data[%v] err: %v", request, err)
	}
	body, err := bce.NewBodyFromBytes(dataByte)
	if err != nil {
		return nil, fmt.Errorf("NewBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()

	packageInfos := []model.AcctPackageInfo{}
	if err := resp.ParseJsonBody(&packageInfos); err != nil {
		return nil, err
	}
	return packageInfos, nil
}
