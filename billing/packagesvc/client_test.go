package packagesvc

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/iam"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/packagesvc/model"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/testings"
)

func makeClient(t *testing.T) *Client {
	iamConf := iam.IamClientConfiguration{
		Endpoint: "http://iam.bj.internal-qasandbox.baidu-int.com",
		UserName: "xuperasset2",
		Password: "SRPA4SEbWuGO5usSCklz9voaIq1krcyI",
		Domain:   "Default",
	}
	iamCli := iam.NewClient(&iamConf)
	accessKey, err := iamCli.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}
	conf := PackageSvcClientConfiguration{
		Endpoint: "http://bjyz-y22-sandbox001.bjyz.baidu.com:8666",
		Credentials: auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
		Logger: testings.NewLogger(),
	}
	cli, err := NewClient(&conf)
	if err != nil {
		t.Fatal(err)
	}

	return cli
}

func TestGetAcctPackages(t *testing.T) {
	cli := makeClient(t)

	opt := model.AcctPackagesOptions{
		AccountId:   "d142ac44f4684dc7949cd680355d831e",
		ServiceType: "XUPERASSET",
		Regin:       "global",
		PageNo:      1,
		PageSize:    100,
	}

	fmt.Println(testings.ToJSON(&opt))

	pkgs, err := cli.GetPackagesByAccount(&opt)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println(testings.ToJSON(pkgs))
}

func TestGetOrderPackages(t *testing.T) {
	cli := makeClient(t)

	opt := model.OrderPackagesOptions{
		AccountId: "d142ac44f4684dc7949cd680355d831e",
		OrderIds:  []string{"a85ac9eba4b34f10b132ad40f878eece"},
	}

	pkgs, err := cli.GetPackagesByOrder(&opt)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println(testings.ToJSON(pkgs))
}
