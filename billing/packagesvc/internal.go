package packagesvc

import (
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/packagesvc/model"
)

const (
	FlavorCount = "count"
)

func checkFlavor4PackageCount(req *model.CreatePackageRequest) error {
	for _, item := range req.Items {
		if countValue := flavorExists(item.Flavor, FlavorCount); len(countValue) == 0 {
			return fmt.Errorf("missing [count] flavor for package in order items")
		}
	}

	return nil
}

// flavorExists 判断是否存在待查询的 flavor，存在则返回值，不存在返回空
func flavorExists(flavors []model.Flavor, name string) string {
	for _, flavor := range flavors {
		if flavor.Name == name {
			return flavor.Value
		}
	}

	return ""
}
