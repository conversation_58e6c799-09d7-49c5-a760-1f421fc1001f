package model

import (
	"encoding/json"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/order/model"
)

const (
	PKG_STATUS_RUNNING   = "RUNNING"
	PKG_STATUS_CREATED   = "CREATED"
	PKG_STATUS_EXPIRED   = "EXPIRED"
	PKG_STATUS_INITIAL   = "INITIAL"
	PKG_STATUS_USED_UP   = "USED_UP"
	PKG_STATUS_DESTROYED = "DESTROYED"
)

// CreatePackageRequest 量包的创建参数是 “订单”
type CreatePackageRequest = model.Order

type OrderItem = model.OrderItem

type Flavor = model.Flavor

type PackageInfo struct {
	Id           int     `json:"id"`
	AccountId    string  `json:"accountId"`
	Capacity     float32 `json:"capacity"`
	UsedCapacity float32 `json:"usedCapacity"`
	ServiceType  string  `json:"serviceType"`
	OrderId      string  `json:"orderId"`
	OrderItemKey string  `json:"orderItemKey"`
	PackageName  string  `json:"packageName"`
	ActiveTime   string  `json:"activeTime"`
	ExpireTime   string  `json:"expireTime"`
	CreateTime   string  `json:"createTime"`
	Status       string  `json:"status"`
	PackageType  string  `json:"packageType"`
	Region       string  `json:"region"`
}

type AcctPackagesOptions struct {
	AccountId   string `json:"accountId"`   // 账户ID，必填
	ServiceType string `json:"serviceType"` // 产品名称，必填
	Regin       string `json:"regin"`       // 区域，必填
	PageNo      int    `json:"pageNo"`      // 页号，必填
	PageSize    int    `json:"pageSize"`    // 每页大小，必填

	Status           string `json:"status,omitempty"`           // RUNNING|CREATED|EXPIRED|INITIAL|USED_UP|DESTROYED
	DeductInstanceId string `json:"deductInstanceId,omitempty"` // 量包绑定的实例ID
	PackageType      string `json:"packageType,omitempty"`      // 类型
	OrderBy          string `json:"orderBy,omitempty"`          // create_time|active_time|expire_time|capacity|status
	Order            string `json:"order,omitempty"`            // asc|desc
}

type OrderPackagesOptions struct {
	AccountId string   `json:"accountId"` // 账户ID，必填
	OrderIds  []string `json:"orderIds"`  // 订单ID，必填
}

type AcctPackageInfo struct {
	PackageInfo
	PackageClassifyType int             `json:"packageClassifyType"`
	DeductInstanceId    string          `json:"deductInstanceId"`
	DeductPolicy        string          `json:"deductPolicy"`
	PackageShortId      string          `json:"packageShortId"`
	PackageProperty     string          `json:"packageProperty"`
	PackageSegVOList    json.RawMessage `json:"packageSegVOList"` // 暂时不知道有没有用
}

type AcctPackageResponse struct {
	TotalCount int               `json:"totalCount"`
	PageNo     int               `json:"pageNo"`
	PageSize   int               `json:"pageSize"`
	Result     []AcctPackageInfo `json:"result"`
	OrderBy    string            `json:"orderBy"`
	Order      string            `json:"order"`
}
