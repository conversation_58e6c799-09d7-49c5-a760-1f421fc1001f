package realname

import (
	"errors"
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/http"
)

type Client struct {
	*bce.Client
}

func NewClient(conf *RealNameClientConfiguration) (*Client, error) {
	if err := conf.Valid(); err != nil {
		return nil, fmt.Errorf("RealNameClientConfiguration invalid, error: %v", err)
	}
	if conf.SignOption == nil {
		conf.SignOption = &auth.SignOptions{
			HeadersToSign: auth.DEFAULT_HEADERS_TO_SIGN,
			ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS,
			IsBce:         true,
		}
	}
	if conf.Retry == nil {
		conf.Retry = bce.DEFAULT_RETRY_POLICY
	}
	bceConf := bce.BceClientConfiguration{
		Endpoint:    conf.Endpoint,
		Credentials: &conf.Credentials,
		SignOption:  conf.SignOption,
		Retry:       conf.Retry,
		Timeout:     conf.Timeout,
		Logger:      conf.Logger,
	}
	bceClient := bce.NewClient(&bceConf)
	client := Client{
		Client: bceClient,
	}
	return &client, nil
}

func (rn *Client) QualifyRealName(accountId string) (*RealNameInfo, error) {
	if len(accountId) == 0 {
		return nil, errors.New("param error")
	}
	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri(QualifyRealNameApi)
	bceRequest.SetMethod(http.GET)
	bceRequest.SetParam("accountId", accountId)
	resp := &bce.BceResponse{}
	if err := rn.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()

	realNameInfo := new(RealNameInfo)
	if err := resp.ParseJsonBody(realNameInfo); err != nil {
		return nil, err
	}
	return realNameInfo, nil
}

func (rn *Client) QueryCompanyFile(accountId string) (*CompanyFile, error) {
	if len(accountId) == 0 {
		return nil, errors.New("param error")
	}
	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri(QueryCompanyFileApi)
	bceRequest.SetMethod(http.GET)
	bceRequest.SetParam("accountId", accountId)
	resp := &bce.BceResponse{}
	if err := rn.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()

	companyLicense := new(CompanyFile)
	if err := resp.ParseJsonBody(companyLicense); err != nil {
		return nil, err
	}
	return companyLicense, nil
}
