package realname

import (
	"encoding/json"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/iam"
)

func TestClient(t *testing.T) {
	iamConf := iam.IamClientConfiguration{
		Endpoint: "http://iam.bj.internal-qasandbox.baidu-int.com",
		UserName: "xuperasset2",
		Password: "SRPA4SEbWuGO5usSCklz9voaIq1krcyI",
		Domain:   "Default",
	}
	iamCli := iam.NewClient(&iamConf)
	accessKey, err := iamCli.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}
	conf := RealNameClientConfiguration{
		Endpoint: "http://gzns-store-sandbox089.gzns.baidu.com:8291",
		Credentials: auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
	}
	cli, err := NewClient(&conf)
	if err != nil {
		t.Fatal(err)
	}
	info, err := cli.QualifyRealName("c35440f91d6a48568ea01e21e9e336e5")
	if err != nil {
		t.Fatal(err)
	}
	str, _ := json.MarshalIndent(&info, "", "\t")
	fmt.Println(string(str))
}
