package realname

import (
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/util"
)

const (
	QualifyRealNameApi  = "/qualify/v2/qualification/realname"
	QueryCompanyFileApi = "/qualify/v1/qualification/realname/company/file"
)

type QualifyType string

const (
	PersonalQualification   QualifyType = "PERSONAL"
	EnterpriseQualification QualifyType = "ENTERPRISE"
	WhiteQualification      QualifyType = "WHITE"
)

type QualifyStatus string

const (
	QualificationReturn QualifyStatus = "RETURN"
	QualificationPass   QualifyStatus = "PASS"
	QualificationAudit  QualifyStatus = "AUDIT"
	QualificationNone   QualifyStatus = "NONE"
)

type RealNameInfo struct {
	QualifyType              QualifyType
	Name                     string
	QualifyStatus            QualifyStatus
	EnterprisePrimePassFlag  bool
	EnterpriseSeniorPassFlag bool
}

type RealNameClientConfiguration struct {
	Endpoint    string
	Retry       bce.RetryPolicy
	SignOption  *auth.SignOptions
	Credentials auth.BceCredentials
	Timeout     int
	Logger      util.Logger
}

func (c *RealNameClientConfiguration) Valid() error {
	if len(c.Endpoint) == 0 {
		return fmt.Errorf("missing endpoint")
	}
	if len(c.Credentials.AccessKeyId) == 0 || len(c.Credentials.SecretAccessKey) == 0 {
		return fmt.Errorf("missing credentials")
	}
	return nil
}

type CompanyFile struct {
	AccountId    string `json:"accountId"`
	File         string `json:"file"`
	Suffix       string `json:"suffix"`
	MaterialType string `json:"materialType"`
	Type         string `json:"type"`
	Name         string `json:"name"`
	Number       string `json:"number"`
}
