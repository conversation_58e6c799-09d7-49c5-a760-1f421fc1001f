package resource

import (
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/http"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/resource/model"
)

type Client struct {
	*bce.Client
}

func NewClient(conf *ClientConfiguration) (*Client, error) {
	if err := conf.Valid(); err != nil {
		return nil, fmt.Errorf("OrderFacadeClientConfiguration invalid, error: %v", err)
	}
	if conf.SignOption == nil {
		conf.SignOption = &auth.SignOptions{
			HeadersToSign: auth.DEFAULT_HEADERS_TO_SIGN,
			ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS,
			IsBce:         true,
		}
	}
	if conf.Retry == nil {
		conf.Retry = bce.DEFAULT_RETRY_POLICY
	}
	bceConf := bce.BceClientConfiguration{
		Endpoint:    conf.Endpoint,
		Credentials: &conf.Credentials,
		SignOption:  conf.SignOption,
		Retry:       conf.Retry,
		Timeout:     conf.Timeout,
		Logger:      conf.Logger,
	}
	bceClient := bce.NewClient(&bceConf)
	client := Client{
		Client: bceClient,
	}
	return &client, nil
}

func (c *Client) QueryResourceByName(request *model.QueryRscByNameReq) (*model.Resource, error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri(ApiChargeRscDetail)
	bceRequest.SetMethod(http.POST)
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	dataByte, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("marshal data[%v] err: %v", request, err)
	}
	body, err := bce.NewBodyFromBytes(dataByte)
	if err != nil {
		return nil, fmt.Errorf("NewBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	rscResp := &model.Resource{}
	if err := resp.ParseJsonBody(rscResp); err != nil {
		return nil, err
	}
	return rscResp, nil
}

func (c *Client) QueryResourceByStatus(request *model.QueryRscByStatusReq) (*model.QueryRscByStatusResp, error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri(ApiSearchRscDetail)
	bceRequest.SetMethod(http.POST)
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	dataByte, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("marshal data[%v] err: %v", request, err)
	}
	body, err := bce.NewBodyFromBytes(dataByte)
	if err != nil {
		return nil, fmt.Errorf("NewBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	rscResp := &model.QueryRscByStatusResp{}
	if err := resp.ParseJsonBody(rscResp); err != nil {
		return nil, err
	}
	return rscResp, nil
}

// 注意，此接口需使用订单服务的endpoint
func (c *Client) QueryResourceByUuid(uuid string) (*model.OrderResource, error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri(ApiQueryRscByUuid + "/" + uuid)
	bceRequest.SetMethod(http.GET)
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	rscResp := &model.OrderResource{}
	if err := resp.ParseJsonBody(rscResp); err != nil {
		return nil, err
	}
	return rscResp, nil
}

// QueryResourceWithExt 注意，此接口需使用订单服务的endpoint
func (c *Client) QueryResourceWithExt(uuid string) (*model.OrderResource, error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri(ApiQueryRscByUuid + "/" + uuid)
	bceRequest.SetParam("extend", "financeRefundFinish")
	bceRequest.SetMethod(http.GET)
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	resp := &bce.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	rscResp := &model.OrderResource{}
	if err := resp.ParseJsonBody(rscResp); err != nil {
		return nil, err
	}
	return rscResp, nil
}
