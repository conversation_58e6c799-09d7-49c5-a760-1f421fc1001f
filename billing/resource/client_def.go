package resource

import (
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/util"
)

const (
	ApiChargeRscDetail = "/v1/chargeResource/detail"
	ApiSearchRscDetail = "/v1/query/resource/detail"

	ApiQueryRscByUuid = "/resources" // 注意，此接口需使用订单服务的endpoint
)

type ClientConfiguration struct {
	Endpoint    string
	Retry       bce.RetryPolicy
	SignOption  *auth.SignOptions
	Credentials auth.BceCredentials
	Timeout     int
	Logger      util.Logger
}

func (c *ClientConfiguration) Valid() error {
	if len(c.Endpoint) == 0 {
		return fmt.Errorf("missing endpoint")
	}
	if len(c.Credentials.AccessKeyId) == 0 ||
		len(c.Credentials.SecretAccessKey) == 0 {
		return fmt.Errorf("invalid credentials")
	}
	return nil
}
