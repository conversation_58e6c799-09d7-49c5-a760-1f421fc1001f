package model

import (
	"bytes"
	"encoding/json"
	"strings"
)

type ResourceStatus string
type ProductType string

const (
	Init      ResourceStatus = "INIT"
	Running   ResourceStatus = "RUNNING"
	Stopped   ResourceStatus = "STOPPED"
	Destroyed ResourceStatus = "DESTROYED"
	Clear     ResourceStatus = "CLEAR"
	Hold      ResourceStatus = "HOLD"
)

const (
	PrePay  ProductType = "PREPAY"
	PostPay ProductType = "POSTPAY"
)

func (s *ResourceStatus) MarshalJSON() ([]byte, error) {
	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(string(*s))
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

// UnmarshalJSON unmarshal a quoted json string to the enum value
func (s *ResourceStatus) UnmarshalJSON(b []byte) error {
	var j string
	err := json.Unmarshal(b, &j)
	if err != nil {
		return err
	}
	*s = ResourceStatus(strings.ToUpper(j))
	return nil
}

func (s *ProductType) MarshalJSON() ([]byte, error) {
	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(string(*s))
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

// UnmarshalJSON unmarshal a quoted json string to the enum value
func (s *ProductType) UnmarshalJSON(b []byte) error {
	var j string
	err := json.Unmarshal(b, &j)
	if err != nil {
		return err
	}
	*s = ProductType(strings.ToUpper(j))
	return nil
}

type QueryRscByNameReq struct {
	ServiceType string `json:"serviceType"`
	Region      string `json:"region"`
	Name        string `json:"name"`
	AccountId   string `json:"accountId"`
}

type QueryRscByStatusReq struct {
	ServiceType              string           `json:"serviceType,omitempty"`
	Region                   string           `json:"region,omitempty"`
	ProductType              string           `json:"productType,omitempty"`
	AccountId                string           `json:"accountId,omitempty"`
	NameOrShortIds           []string         `json:"nameOrShortIds,omitempty"`
	Status                   []ResourceStatus `json:"status,omitempty"`
	ExpireTimeBeforeOrEquals string           `json:"expireTimeBeforeOrEquals,omitempty"`
	ExpireTimeAfterOrEquals  string           `json:"expireTimeAfterOrEquals,omitempty"`
	PageNo                   int              `json:"pageNo,omitempty"`
	PageSize                 int              `json:"pageSize,omitempty"`
}

type QueryRscByStatusResp struct {
	TotalCount int        `json:"totalCount"`
	PageNo     int        `json:"pageNo"`
	PageSize   int        `json:"pageSize"`
	Result     []Resource `json:"result"`
}

type Resource struct {
	AccountId      string         `json:"accountId"`
	ServiceType    string         `json:"serviceType"`
	Region         string         `json:"region"`
	Name           string         `json:"name"`
	ShortId        string         `json:"shortId"`
	Status         ResourceStatus `json:"status"`
	ProductType    ProductType    `json:"productType"`
	SubProductType string         `json:"subProductType"`
	Uuid           string         `json:"uuid"`
	CreateTime     string         `json:"createTime"`
	StartTime      string         `json:"startTime"`
	StopTime       string         `json:"stopTime"`
	ExpireTime     string         `json:"expireTime"`
	DestroyTime    string         `json:"destroyTime"`
	ReleaseTime    string         `json:"releaseTime"`
	Flavor         string         `json:"flavor"`
	Extra          string         `json:"extra"`
}

type Flavor struct {
	Name  string `json:"name"`
	Value string `json:"value"`
	Scale int    `json:"scale"`
}

type OrderResource struct {
	UUID                string   `json:"uuid"`
	AccountID           string   `json:"accountId"`
	ChargeUserID        int      `json:"chargeUserId"`
	ChargeUserType      string   `json:"chargeUserType"`
	ServiceType         string   `json:"serviceType"`
	ProductType         string   `json:"productType"`
	Region              string   `json:"region"`
	Name                string   `json:"name"`
	Flavor              []Flavor `json:"flavor"`
	Status              string   `json:"status"`
	TaskStatus          string   `json:"taskStatus"`
	Extra               string   `json:"extra"`
	CreateTime          string   `json:"createTime"`
	UpdateTime          string   `json:"updateTime"`
	ExpireTime          string   `json:"expireTime"`
	StopTime            string   `json:"stopTime"`
	DestroyTime         string   `json:"destroyTime"`
	FinanceRefundFinish bool     `json:"financeRefundFinish"`
}
