package splitaccount

import (
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/http"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/splitaccount/model"
)

type Client struct {
	*bce.Client
}

func NewClient(conf *ClientConfiguration) (*Client, error) {
	if err := conf.Valid(); err != nil {
		return nil, fmt.Errorf("ClientConfiguration invalid, error: %v", err)
	}
	if conf.SignOption == nil {
		conf.SignOption = &auth.SignOptions{
			HeadersToSign: auth.DEFAULT_HEADERS_TO_SIGN,
			ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS,
			IsBce:         true,
		}
	}
	if conf.Retry == nil {
		conf.Retry = bce.DEFAULT_RETRY_POLICY
	}
	bceConf := bce.BceClientConfiguration{
		Endpoint:    conf.Endpoint,
		Credentials: &conf.Credentials,
		SignOption:  conf.SignOption,
		Retry:       conf.Retry,
		Timeout:     conf.Timeout,
		Logger:      conf.Logger,
	}
	bceClient := bce.NewClient(&bceConf)
	client := Client{
		Client: bceClient,
	}

	return &client, nil
}

func (t *Client) QueryCommonInfo() (*model.AccountCommonInfo, error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetMethod(http.GET)
	bceRequest.SetUri(QueryCmnInfoApi)
	bceRequest.BuildHttpRequest()
	bceRequest.SetHeader(http.HOST, bceRequest.Host())
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	resp := &bce.BceResponse{}
	if err := t.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	cmnInfoResponse := new(model.AccountCommonInfo)
	if err := resp.ParseJsonBody(cmnInfoResponse); err != nil {
		return nil, err
	}
	return cmnInfoResponse, nil
}

func (t *Client) QueryBranchBankInfo(param *model.BranchQueryParam) (*model.BranchResp, error) {
	if param.Province == "" || param.City == "" || param.BankName == "" || param.BranchName == "" {
		return nil, errors.New("param error")
	}
	bceRequest := &bce.BceRequest{}
	bceRequest.SetMethod(http.POST)
	bceRequest.SetUri(QueryBranchBankInfoApi)
	bceRequest.BuildHttpRequest()
	bceRequest.SetHeader(http.HOST, bceRequest.Host())
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	bodyJson, err := json.Marshal(&param)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal BranchQueryParam, err: %v", err)
	}
	b, err := bce.NewBodyFromBytes(bodyJson)
	if err != nil {
		return nil, err
	}
	bceRequest.SetBody(b)
	resp := &bce.BceResponse{}
	if err := t.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	bankResponse := new(model.BranchResp)
	if err := resp.ParseJsonBody(bankResponse); err != nil {
		return nil, err
	}
	return bankResponse, nil
}

// CreateSplitAccount 创建分账对象
func (t *Client) CreateSplitAccount(param *model.CreateSplitAccountParam) (*model.UpdateSplitAccountResp, error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetMethod(http.POST)
	bceRequest.SetUri(CreateSplitAccountApi)
	bceRequest.BuildHttpRequest()
	bceRequest.SetHeader(http.HOST, bceRequest.Host())
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	bodyJson, err := json.Marshal(&param)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal CreateSplitAccountParam, err: %v", err)
	}
	b, err := bce.NewBodyFromBytes(bodyJson)
	if err != nil {
		return nil, err
	}
	bceRequest.SetBody(b)
	resp := &bce.BceResponse{}
	if err := t.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	accountResponse := new(model.UpdateSplitAccountResp)
	if err := resp.ParseJsonBody(accountResponse); err != nil {
		return nil, err
	}
	return accountResponse, nil
}

func (t *Client) QueryAccountStatus(param *model.QueryAccountStatusParam) (*model.QueryAccountStatusResp, error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetMethod(http.POST)
	bceRequest.SetUri(QueryAccountStatusApi)
	bceRequest.BuildHttpRequest()
	bceRequest.SetHeader(http.HOST, bceRequest.Host())
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	bodyJson, err := json.Marshal(&param)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal QueryAccountStatusParam, err: %v", err)
	}
	b, err := bce.NewBodyFromBytes(bodyJson)
	if err != nil {
		return nil, err
	}
	bceRequest.SetBody(b)
	resp := &bce.BceResponse{}
	if err := t.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	statusResponse := new(model.QueryAccountStatusResp)
	if err := resp.ParseJsonBody(statusResponse); err != nil {
		return nil, err
	}
	return statusResponse, nil
}

func (t *Client) UpdateSplitRatio(param *model.UpdateSplitRatioParam) (*model.UpdateSplitAccountResp, error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetMethod(http.POST)
	bceRequest.SetUri(UpdateSplitRatio)
	bceRequest.BuildHttpRequest()
	bceRequest.SetHeader(http.HOST, bceRequest.Host())
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	bodyJson, err := json.Marshal(&param)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal UpdateSplitRatioParam, err: %v", err)
	}
	b, err := bce.NewBodyFromBytes(bodyJson)
	if err != nil {
		return nil, err
	}
	bceRequest.SetBody(b)
	resp := &bce.BceResponse{}
	if err := t.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	accountResponse := new(model.UpdateSplitAccountResp)
	if err := resp.ParseJsonBody(accountResponse); err != nil {
		return nil, err
	}
	return accountResponse, nil
}

func (t *Client) UpdateSplitAccount(param *model.UpdateSplitAccountParam) (*model.UpdateSplitAccountResp, error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetMethod(http.POST)
	bceRequest.SetUri(UpdateSplitAccountApi)
	bceRequest.BuildHttpRequest()
	bceRequest.SetHeader(http.HOST, bceRequest.Host())
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	bodyJson, err := json.Marshal(&param)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal UpdateSplitAccountParam, err: %v", err)
	}
	b, err := bce.NewBodyFromBytes(bodyJson)
	if err != nil {
		return nil, err
	}
	bceRequest.SetBody(b)
	resp := &bce.BceResponse{}
	if err := t.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	accountResponse := new(model.UpdateSplitAccountResp)
	if err := resp.ParseJsonBody(accountResponse); err != nil {
		return nil, err
	}
	return accountResponse, nil
}

func (t *Client) QuerySeparateCashInOutRecord(param *model.QuerySeparateCashInOutRecordParam) (
	*model.QuerySeparateCashInOutRecordResp, error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetMethod(http.POST)
	bceRequest.SetUri(QuerySeparateCashInOutRecordApi)
	bceRequest.BuildHttpRequest()
	bceRequest.SetHeader(http.HOST, bceRequest.Host())
	bceRequest.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	bodyJson, err := json.Marshal(&param)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal QuerySeparateCashInOutRecordParam, err: %v", err)
	}
	b, err := bce.NewBodyFromBytes(bodyJson)
	if err != nil {
		return nil, err
	}
	bceRequest.SetBody(b)

	resp := &bce.BceResponse{}
	if err := t.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	recodeResponse := new(model.QuerySeparateCashInOutRecordResp)
	if err := resp.ParseJsonBody(recodeResponse); err != nil {
		return nil, err
	}
	return recodeResponse, nil
}
