package splitaccount

import (
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/util"
)

const (
	QueryCmnInfoApi        = "/v3/finance/separateAccount/info/bank"
	QueryBranchBankInfoApi = "/v3/finance/separateAccount/info/branch"
	CreateSplitAccountApi  = "/v3/finance/separateAccount/create"
	QueryAccountStatusApi  = "/v3/finance/separateAccount/query"
	UpdateSplitRatio       = "/v3/finance/separateAccount/update/ratio"
	UpdateSplitAccountApi  = "/v3/finance/separateAccount/update/info"

	QuerySeparateCashInOutRecordApi = "/v3/finance/record/separate/cashInOutRecord"
)

type ClientConfiguration struct {
	Endpoint    string
	Retry       bce.RetryPolicy
	SignOption  *auth.SignOptions
	Credentials auth.BceCredentials
	Timeout     int
	Logger      util.Logger
}

func (c *ClientConfiguration) Valid() error {
	if len(c.Endpoint) == 0 {
		return fmt.Errorf("missing endpoint")
	}
	if len(c.Credentials.AccessKeyId) == 0 || len(c.Credentials.SecretAccessKey) == 0 {
		return fmt.Errorf("missing credentials")
	}
	return nil
}
