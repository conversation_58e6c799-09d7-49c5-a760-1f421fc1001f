package splitaccount

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/iam"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/splitaccount/model"
)

func TestClient_QueryCommonInfo(t *testing.T) {
	iamConf := iam.IamClientConfiguration{
		Endpoint: "http://iam.bj.internal-qasandbox.baidu-int.com",
		UserName: "xuperasset2",
		Password: "SRPA4SEbWuGO5usSCklz9voaIq1krcyI",
		Domain:   "Default",
	}
	iamCli := iam.NewClient(&iamConf)
	accessKey, err := iamCli.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}
	conf := ClientConfiguration{
		Endpoint: "http://finance.qasandbox.bce.baidu-int.com",
		Credentials: auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
	}
	cli, err := NewClient(&conf)
	if err != nil {
		t.Fatal(err)
	}
	resp, err := cli.QueryCommonInfo()
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(resp)
}

func TestClient_CreateSplitAccount(t *testing.T) {
	iamConf := iam.IamClientConfiguration{
		Endpoint: "http://iam.bj.internal-qasandbox.baidu-int.com",
		UserName: "xuperasset2",
		Password: "SRPA4SEbWuGO5usSCklz9voaIq1krcyI",
		Domain:   "Default",
	}
	iamCli := iam.NewClient(&iamConf)
	accessKey, err := iamCli.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}
	conf := ClientConfiguration{
		Endpoint: "http://finance.qasandbox.bce.baidu-int.com",
		Credentials: auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
	}
	cli, err := NewClient(&conf)
	if err != nil {
		t.Fatal(err)
	}
	param := &model.CreateSplitAccountParam{}
	info, err := cli.CreateSplitAccount(param)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(info)
}

func TestClient_QuerySeparateCashInOutRecord(t *testing.T) {
	iamConf := iam.IamClientConfiguration{
		Endpoint: "http://iam.bj.internal-qasandbox.baidu-int.com",
		UserName: "xuperasset2",
		Password: "SRPA4SEbWuGO5usSCklz9voaIq1krcyI",
		Domain:   "Default",
	}
	iamCli := iam.NewClient(&iamConf)
	accessKey, err := iamCli.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}
	conf := ClientConfiguration{
		Endpoint: "http://finance.qasandbox.bce.baidu-int.com",
		Credentials: auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
	}
	cli, err := NewClient(&conf)
	if err != nil {
		t.Fatal(err)
	}
	param := model.QuerySeparateCashInOutRecordParam{
		PageNo:            2,
		PageSize:          10,
		OrderBy:           model.OrderByTransactTime,
		Order:             model.OrderAsc,
		StartTransactTime: "2023-06-01T00:00:00Z",
		EndTransactTime:   "2023-07-06T00:00:00Z",
		ServiceType:       "XUPERASSET",
	}
	resp, err := cli.QuerySeparateCashInOutRecord(&param)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(resp)
}
