package model

type AccountCommonInfo struct {
	Cities   []*CityInfo     `json:"cities"`
	Banks    []string        `json:"banks"`
	Industry []*IndustryInfo `json:"industry"`
}

type CityInfo struct {
	Province string   `json:"province"`
	Cities   []string `json:"cities"`
}

type IndustryInfo struct {
	IndustryId       int64           `json:"industryId"`
	IndustryName     string          `json:"industryName"`
	NeedPermit       int             `json:"needPermit"`
	IndustryLevel    int             `json:"industryLevel"`
	ParentIndustryId int64           `json:"parentIndustryId"`
	PermitDesc       string          `json:"permitDesc"`
	SubIndustry      []*IndustryInfo `json:"subIndustry"`
}

type BranchQueryParam struct {
	Province   string `json:"province"`
	City       string `json:"city"`
	BankName   string `json:"bankName"`
	BranchName string `json:"branchName"`
}

type BranchResp struct {
	BranchBanks []string `json:"branchBanks"`
}

type CreateSplitAccountParam struct {
	CmsContractId           string `json:"cmsContractId"`
	SkuId                   string `json:"skuId"`
	SeparateRatio           int    `json:"separateRatio"`
	CommissionSeparateRatio int    `json:"commissionSeparateRatio"`
	SupplierType            int    `json:"supplierType"`
	CompanyName             string `json:"companyName"`
	BusinessScope           string `json:"businessScope"`
	BusinessProvince        string `json:"businessProvince"`
	BusinessCity            string `json:"businessCity"`
	BusinessDetailAddress   string `json:"businessDetailAddress"`
	BusinessLicense         string `json:"businessLicense"`
	TaxRegistrationNo       string `json:"taxRegistrationNo"`
	OrganizationCode        string `json:"organizationCode"`
	TaxRegistrationUrl      string `json:"taxRegistrationUrl"`
	OrganizationUrl         string `json:"organizationUrl"`
	LicenseUrl              string `json:"licenseUrl"`
	LicenseStartTime        string `json:"licenseStartTime"`
	LicenseEndTime          string `json:"licenseEndTime"`
	TaxEndTime              string `json:"taxEndTime"`
	OrganizationEndTime     string `json:"organizationEndTime"`
	SplitName               string `json:"splitName"`
	BankAccount             string `json:"bankAccount"`
	BankCard                string `json:"bankCard"`
	BankProvince            string `json:"bankProvince"`
	BankCity                string `json:"bankCity"`
	BankName                string `json:"bankName"`
	BankBranchName          string `json:"bankBranchName"`
	PhoneNumber             string `json:"phoneNumber"`
	CapitalSettlementType   string `json:"capitalSettlementType"`
	PaymentDays             int    `json:"paymentDays"`
	CommissionRate          int    `json:"commissionRate"`
	DayMaxFrozenAmount      int64  `json:"dayMaxFrozenAmount"`
	PoolCashPledge          int64  `json:"poolCashPledge"`
	StlAcctType             int    `json:"stlAcctType"`
	OccupationType          string `json:"occupationType"`
	IndustryId              string `json:"industryId"`
	ManagePermitUrl         string `json:"managePermitUrl"`
	AuthCapital             int64  `json:"authCapital"`
	CapitalCcy              string `json:"capitalCcy"`
	LegalPerson             string `json:"legalPerson"`
	LegalId                 string `json:"legalId"`
	LegalPersonType         int    `json:"legalPersonType"` // byte是啥类型
	LegalCardStartTime      string `json:"legalCardStartTime"`
	LegalCardEndTime        string `json:"legalCardEndTime"`
	IdCardFrontUrl          string `json:"idCardFrontUrl"`
	IdCardBackUrl           string `json:"idCardBackUrl"`
	ManagerSame             int    `json:"managerSame"`
	Manager                 string `json:"manager"`
	ManagerCardType         int    `json:"managerCardType"` //byte 类型
	ContactCard             string `json:"contactCard"`
	ContactCardStartTime    string `json:"contactCardStartTime"`
	ContactCardEndTime      string `json:"contactCardEndTime"`
	ContactCardFrontUrl     string `json:"contactCardFrontUrl"`
	ContactCardBackUrl      string `json:"contactCardBackUrl"`
	BenefitSame             int    `json:"benefitSame"`
	Benefit                 string `json:"benefit"`
	BenefitCardType         int    `json:"benefitCardType"` //byte 类型
	BenefitCard             string `json:"benefitCard"`
	BenefitCardStartTime    string `json:"benefitCardStartTime"`
	BenefitCardEndTime      string `json:"benefitCardEndTime"`
	BenefitCardFrontUrl     string `json:"benefitCardFrontUrl"`
	BenefitCardBackUrl      string `json:"benefitCardBackUrl"`
}

type UpdateSplitAccountResp struct {
	Success           int    `json:"success"`
	SeparateAccountId int64  `json:"separateAccountId"`
	FailReason        string `json:"failReason"`
}

type QueryAccountStatusParam struct {
	SeparateAccountId int64 `json:"separateAccountId"`
}

type QueryAccountStatusResp struct {
	SeparateAccountId int64  `json:"separateAccountId"`
	AuditStatus       string `json:"auditStatus"`
	FailReason        string `json:"failReason"`
}

type UpdateSplitRatioParam struct {
	SeparateAccountId       int64 `json:"separateAccountId"`
	SeparateRatio           int   `json:"separateRatio"`
	CommissionSeparateRatio int   `json:"commissionSeparateRatio"`
}

type UpdateSplitAccountParam struct {
	SeparateAccountId int64 `json:"separateAccountId"`
	CreateSplitAccountParam
}
