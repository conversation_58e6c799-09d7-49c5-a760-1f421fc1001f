package model

// 企业类型
const (
	SupplierTypeCompany = 1 // 企业
	SupplierTypePerson  = 2 // 个体工商户
)

const (
	TimeFmt = "2006-01-02" // 固定时间格式
)

var (
	ValidImgType = []string{"jpg", "jpeg", "bmp", "png"}
)

const (
	CapitalSettlementTypeAuto     = 1 // T+n自动付款
	CapitalSettlementTypeConstant = 2 // 每月某日付款
	capitalSettlementTypeManual   = 4 // 自提付款
)

// 提现类型
const (
	CommissionRateDefault     = 6     // 佣金比例，默认0.6%
	DayMaxFrozenAmountDefault = 10000 // 每天最大退款限额，默认10000
	PoolCashPledgeDefault     = 0     // 提现后的保留金额（元）,默认0
)

// 结算对象类型
const (
	StlAcctTypeCompany   = 1 // 企业对公户
	StlAcctTypeJuridical = 2 // 法人对私户
	StlAcctTypeContact   = 3 // 经营联系人对私户

	StlAcctTypeDefault = StlAcctTypeCompany // 默认1
)

// 职业类型
const (
	OccupationTypeGuojia    = "0" // 国家机关、党群组织、企业、事业单位负责人
	OccupationTypeZhuanye   = "1" // 专业技术人员
	OccupationTypeBanshi    = "3" // 办事人员和有关人员
	OccupationTypeShangye   = "4" // 商业、服务业人员
	OccupationTypeShengchan = "5" // 农、林、牧、渔、水利业生产人员
	OccupationTypeCaozuo    = "6" // 生产、运输设备操作人员及有关人员
	OccupationTypeJunren    = "X" // 军人
)

// 币种
const (
	CapitalCcyCNY     = "CNY"         // 人民币
	CapitalCcyHKD     = "HKD"         // 港元
	CapitalCcyTWD     = "TWD"         // 新台币
	CapitalCcyEUR     = "EUR"         // 欧元
	CapitalCcyUSD     = "USD"         // 美元
	CapitalCcyGBP     = "GBP"         // 英镑
	CapitalCcyJPY     = "JPY"         // 日元
	CapitalCcyDefault = CapitalCcyCNY // 默认CNY
)

// 法人证件类型
const (
	legalPersonTypeShenfenzheng   = 1  // 身份证
	legalPersonTypeHukouben       = 2  // 居民户口簿”
	legalPersonTypeHuzhao         = 3  // 外国护照”
	legalPersonTypeJunguan        = 5  // 军官证”
	legalPersonTypeShiBING        = 6  // 士兵证”
	legalPersonTypeWujingjunguan  = 7  // 武警军官证”
	legalPersonTypeXianggang      = 8  // 港澳居民来往内地通行证（香港）”
	legalPersonTypeTaiwantongxing = 9  // 台湾居民来往大陆通行证”
	legalPersonTypeLinshi         = 10 // 临时居民身份证”
	legalPersonTypeWaiguo         = 11 // 外国人居留证”
	legalPersonTypeZhongguohuzhao = 12 // 中国护照”
	legalPersonTypeWujingshibing  = 13 // 武警士兵证”
	legalPersonTypeAomen          = 14 // 港澳居民来往内地通行证（澳门）”
	legalPersonTypeBianmin        = 15 // 边民出入境通行证”
	legalPersonTypeTaiwanlvxing   = 16 // 台湾居民旅行证”
	legalPersonTypeQita           = 99 // 其他个人证件”
)

// 审核状态
const (
	AuditStatusAuditing   = "AUDITING"    // 审核中
	AuditStatusCreated    = "CREATED"     // 创建成功
	AuditStatusAuditFail  = "AUDIT_FAIL"  // 创建审核失败（重新创建即可）
	AuditStatusUpdated    = "UPDATED"     // 更新成功
	AuditStatusUpdateFail = "UPDATE_FAIL" // 更新失败（需要修改信息之后重新更新）
)
