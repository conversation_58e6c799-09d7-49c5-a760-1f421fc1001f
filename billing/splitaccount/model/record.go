package model

const (
	OrderByTransactTime = "transact_time"

	OrderDesc = "desc"
	OrderAsc  = "asc"

	OpTypeAll    = -2
	OpTypePay    = 0
	OpTypeRefund = 1

	TimeFormat = "2006-01-02T15:04:05Z"
)

type QuerySeparateCashInOutRecordParam struct {
	PageNo            int    `json:"pageNo"`                      // 页码，最小1
	PageSize          int    `json:"pageSize"`                    // 每页条数，1-1000
	OrderBy           string `json:"orderBy"`                     // 排序字段，固定根据交易时间排序 传固定值transact_time
	Order             string `json:"order"`                       // 倒序排序desc 正序排序asc orderBy与order都传才能生效
	StartTransactTime string `json:"startTransactTime"`           // 交易时间 开始 2023-04-01T00:00:00Z
	EndTransactTime   string `json:"endTransactTime"`             // 交易时间 结束 startTransactTime与endTransactTime都传才能生效
	OpType            int    `json:"opType,omitempty"`            // 枚举，不传默认为-2 -2 查询全部记录 0 查询加款记录 1 查询退款记录
	ServiceType       string `json:"serviceType,omitempty"`       // 产品类型
	UserId            string `json:"userId,omitempty"`            // 需要查询的用户id
	ExternalAccountId string `json:"externalAccountId,omitempty"` // 服务商id 目前没用
	ContractNumber    string `json:"contractNumber,omitempty"`    // 合同号
}

func (req *QuerySeparateCashInOutRecordParam) Valid() bool {
	return req.PageNo > 0 && req.PageSize > 0 && req.PageSize <= 1000 &&
		len(req.OrderBy) > 0 && len(req.Order) > 0 &&
		len(req.StartTransactTime) > 0 && len(req.EndTransactTime) > 0
}

type QuerySeparateCashInOutRecordResult struct {
	SerialNumber                    string  `json:"serialNumber"`                    // 流水号
	TransactTime                    string  `json:"transactTime"`                    // 交易时间
	ChargePlanTime                  string  `json:"chargePlanTime"`                  // 计收时间
	OpType                          string  `json:"opType"`                          // 加退款类型
	UserID                          string  `json:"userId"`                          // 用户id
	ExternalAccountId               string  `json:"externalAccountId"`               // 服务商id
	ExternalAccountName             string  `json:"externalAccountName"`             // 服务商客户名称
	ContractNumber                  string  `json:"contractNumber"`                  // 分账对象对应的CMS合同号
	ServiceType                     string  `json:"serviceType"`                     // 产品类型
	SKUId                           string  `json:"skuId"`                           // 商品库id
	InternalSeparateRatio           float64 `json:"internalSeparateRatio"`           // 百度收入分成比例，百分比
	InternalCommissionSeparateRatio float64 `json:"internalCommissionSeparateRatio"` // 百度通道费分成比例，百分比
	CommissionRate                  float64 `json:"commissionRate"`                  // 合同约定通道费比例，百分比
	Amount                          float64 `json:"amount"`                          // 交易金额
	CommissionAmount                float64 `json:"commissionAmount"`                // 通道费总金额
	InternalCommissionAmount        float64 `json:"internalCommissionAmount"`        // 百度承担通道费金额
	ExternalCommissionAmount        float64 `json:"externalCommissionAmount"`        // IP方承担通道费金额
	InternalSeparateAmount          float64 `json:"internalSeparateAmount"`          // 扣除通道费后百度分成金额
	ExternalSeparateAmount          float64 `json:"externalSeparateAmount"`          // 扣除通道费后IP方分成金额
	InternalIncomeAmount            float64 `json:"internalIncomeAmount"`            // 百度总收入
	OrderID                         string  `json:"orderId"`                         // 订单号
	SeparateAccountID               int     `json:"separateAccountId"`               // 分账对象id
}

type QuerySeparateCashInOutRecordResp struct {
	TotalCount int                                  `json:"totalCount"`
	PageNo     int                                  `json:"pageNo"`
	PageSize   int                                  `json:"pageSize"`
	Result     []QuerySeparateCashInOutRecordResult `json:"result"`
}
