package sts

import (
	"encoding/json"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/iam"
)

// sts服务 根据accountId获取stsCredentials
func TestStsWithAccountId(t *testing.T) {
	iamConf := iam.IamClientConfiguration{
		Endpoint: "http://iam.bj.internal-qasandbox.baidu-int.com",
		UserName: "xuperasset2",
		Password: "SRPA4SEbWuGO5usSCklz9voaIq1krcyI",
		Domain:   "Default",
	}
	iamCli := iam.NewClient(&iamConf)
	accessKey, err := iamCli.GetAccessKey()
	if err != nil {
		t.Fatal(err)
	}
	conf := StsClientConfiguration{
		Endpoint: "http://sts.bj.internal-qasandbox.baidu-int.com:8586",
		Credentials: auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
	}
	cli, err := NewClient(&conf)
	if err != nil {
		t.Fatal(err)
	}
	info, err := cli.AssumeRole("d018e3cf5ad04985b6f57ff9ccee4362", "BceServiceRole_xuperasset2")
	// info, err := cli.AssumeRole("b7b3c58ca7d342f08c2317a5296cd35d", "BceServiceRole_xuperasset2")
	if err != nil {
		t.Fatal(err)
	}
	str, _ := json.MarshalIndent(&info, "", "\t")
	fmt.Println(string(str))
}
