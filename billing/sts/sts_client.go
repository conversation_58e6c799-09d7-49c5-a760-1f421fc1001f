package sts

import (
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/bce"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/http"
)

type Client struct {
	*bce.Client
}

func NewClient(conf *StsClientConfiguration) (*Client, error) {
	if err := conf.Valid(); err != nil {
		return nil, fmt.Errorf("StsClientConfiguration invalid, error: %v", err)
	}
	if conf.SignOption == nil {
		conf.SignOption = &auth.SignOptions{
			HeadersToSign: auth.DEFAULT_HEADERS_TO_SIGN,
			ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS,
			IsBce:         true,
		}
	}
	if conf.Retry == nil {
		conf.Retry = bce.DEFAULT_RETRY_POLICY
	}
	bceConf := bce.BceClientConfiguration{
		Endpoint:    conf.Endpoint,
		Credentials: &conf.Credentials,
		SignOption:  conf.SignOption,
		Retry:       conf.Retry,
		Timeout:     conf.Timeout,
		Logger:      conf.Logger,
	}
	bceClient := bce.NewClient(&bceConf)
	client := Client{
		Client: bceClient,
	}
	return &client, nil
}

// sts 角色扮演接口 accountId参数可以账户ID或者子用户ID
func (s *Client) AssumeRole(accountId string, roleName string) (credentials *Credentials, err error) {
	bceRequest := &bce.BceRequest{}
	bceRequest.SetUri(AssumeRoleApi)
	bceRequest.SetMethod(http.POST)
	bceRequest.SetParam("assumeRole", "")
	bceRequest.SetParam("accountId", accountId)
	bceRequest.SetParam("roleName", roleName)

	resp := &bce.BceResponse{}
	if err := s.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()

	credentials = &Credentials{}
	if err := resp.ParseJsonBody(credentials); err != nil {
		return nil, err
	}
	return credentials, nil
}
