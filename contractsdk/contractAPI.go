package contractsdk

type BlockChain interface {
	// 暂不支持
	DeployContract(node, contractName string, args map[string][]byte) (string, error)
	QueryContract(node, contractName, methodName string, args map[string][]byte) (string, error)
	PostTx(tx, options interface{}) error
	PreExecWithGenerateTx(node, contractName, methodName string, args map[string][]byte) (string, string, interface{}, error)
	QueryTx(node, txid string) (interface{}, error)
	QueryBlock(node, blockid string) (interface{}, error)
	QueryStatus(node string) (interface{}, error)
}
