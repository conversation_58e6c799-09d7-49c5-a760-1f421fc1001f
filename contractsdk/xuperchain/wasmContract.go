package xuperchain

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"google.golang.org/grpc"
	"math/big"
	"time"

	"github.com/xuperchain/xuper-sdk-go/v2/account"
	sdk "github.com/xuperchain/xuper-sdk-go/v2/common"
	"github.com/xuperchain/xuper-sdk-go/v2/crypto"

	"github.com/xuperchain/xuperchain/service/common"
	"github.com/xuperchain/xuperchain/service/pb"

	"github.com/xuperchain/xupercore/kernel/contract"
	"github.com/xuperchain/xupercore/lib/crypto/hash"
	"github.com/xuperchain/xupercore/lib/utils"
)

type xChain struct {
	admin           *account.Account
	contractAccount string
	bcName          string
}

type xOptions struct {
	Node string `json:"node"`
	TxId string `json:"tx_id"`
}

func NewXchain(adminSeed, bcName, contractAccount string) *xChain {
	acc, err := account.RetrieveAccount(adminSeed, 1)
	if err != nil {
		return nil
	}

	return &xChain{
		admin:           acc,
		contractAccount: contractAccount,
		bcName:          bcName,
	}
}

func (xc *xChain) isInit() bool {
	return xc.bcName != "" && xc.contractAccount != "" && xc.admin != nil
}

func (xc *xChain) DeployContract(node, contractName string, args map[string][]byte) (string, error) {
	// TODO: not implement
	return "", nil
}

func (xc *xChain) QueryContract(node, contractName, methodName string, args map[string][]byte) (string, error) {
	if node == "" || contractName == "" || methodName == "" || len(args) == 0 {
		return "", fmt.Errorf("param invalid")
	}

	cli, conn, err := conn(node)
	if err != nil {
		return "", err
	}
	defer conn.Close()

	if !xc.isInit() {
		return "", fmt.Errorf("xChain is not init")
	}

	// 1. make InvokeRequest
	invokeRequest := &pb.InvokeRequest{
		ModuleName:   "wasm",
		ContractName: contractName,
		MethodName:   methodName,
		Args:         args,
	}

	// 2. PreExec tx
	preExecResp, _, _, err := preExecWithSelectUTXOContract(invokeRequest, xc.admin, xc.contractAccount, xc.bcName, cli)
	if err != nil {
		return "", fmt.Errorf("failed to preExec tx: %s", err)
	}

	resps := preExecResp.Response.Responses
	if len(resps) < 1 {
		return "", fmt.Errorf("no resp")
	}

	result := string(resps[len(resps)-1].Body)

	return result, nil
}

func (xc *xChain) PostTx(tx, options interface{}) error {
	if tx == nil || options == nil {
		return fmt.Errorf("param invalid")
	}

	var xOption xOptions
	err := json.Unmarshal(options.([]byte), &xOption)
	if err != nil {
		return err
	}

	if !xc.isInit() {
		return fmt.Errorf("xChain is not init")
	}

	txIdByte, err := hex.DecodeString(xOption.TxId)
	if err != nil {
		return err
	}

	protoMsg := &pb.TxStatus{
		Bcname: xc.bcName,
		Tx:     tx.(*pb.Transaction),
		Txid:   txIdByte,
	}

	// 4. post tx
	cli, conn, err := conn(xOption.Node)
	if err != nil {
		return err
	}
	defer conn.Close()

	resp, err := cli.PostTx(context.TODO(), protoMsg)
	if err != nil {
		return fmt.Errorf("failed to post tx: %s", err)
	}

	if resp.Header.Error != pb.XChainErrorEnum_SUCCESS {
		return fmt.Errorf("post tx did not success: %s", resp.Header.Error.String())
	}

	return nil
}

func (xc *xChain) PreExecWithGenerateTx(node, contractName, methodName string, args map[string][]byte) (string, string, interface{}, error) {
	if node == "" || contractName == "" || methodName == "" || len(args) == 0 {
		return "", "", nil, fmt.Errorf("param invalid")
	}

	if !xc.isInit() {
		return "", "", nil, fmt.Errorf("xChain is not init")
	}

	// 1. make InvokeRequest
	invokeRequest := &pb.InvokeRequest{
		ModuleName:   "wasm",
		ContractName: contractName,
		MethodName:   methodName,
		Args:         args,
	}

	cli, conn, err := conn(node)
	if err != nil {
		return "", "", nil, err
	}

	defer conn.Close()

	// 2. PreExec tx
	preExecResp, req, address, err := preExecWithSelectUTXOContract(invokeRequest, xc.admin, xc.contractAccount, xc.bcName, cli)
	if err != nil {
		return "", "", nil, fmt.Errorf("failed to preExec tx: %v", err)
	}

	content := hash.DoubleSha256([]byte(xc.bcName + address + fmt.Sprintf("%d", preExecResp.GetResponse().GetGasUsed()) + "true"))
	cryptoClient := crypto.GetCryptoClient()

	privateKey, _ := cryptoClient.GetEcdsaPrivateKeyFromJsonStr(xc.admin.PrivateKey)
	sign, _ := cryptoClient.SignECDSA(privateKey, content)
	signInfo := &pb.SignatureInfo{
		PublicKey: xc.admin.PublicKey,
		Sign:      sign,
	}
	in := &pb.PreExecWithSelectUTXORequest{
		Header:      header(),
		Bcname:      xc.bcName,
		Address:     address,
		TotalAmount: 0,
		SignInfo:    signInfo,
		NeedLock:    true,
		Request:     req,
	}

	out, err := cli.PreExecWithSelectUTXO(context.TODO(), in)
	if err != nil {
		return "", "", nil, fmt.Errorf("failed to preExecWithSelectUTXO. err:%v", err)
	}

	// 3. generate & sign tx
	rawTx := generateTx(out, xc.admin, xc.contractAccount)
	tx := signTx(rawTx, xc.admin, xc.contractAccount)

	txId := tx.Txid

	return node, hex.EncodeToString(txId), tx, nil
}

func (xc *xChain) QueryTx(node, txid string) (interface{}, error) {
	if node == "" || txid == "" {
		return nil, fmt.Errorf("param invalid")
	}

	cli, conn, err := conn(node)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	if !xc.isInit() {
		return nil, fmt.Errorf("xChain is not init")
	}

	rawTxid, err := hex.DecodeString(txid)
	if err != nil {
		return nil, err
	}
	txStatus := &pb.TxStatus{
		Bcname: xc.bcName,
		Txid:   rawTxid,
	}
	res, err := cli.QueryTx(context.TODO(), txStatus)
	if err != nil {
		return nil, err
	}
	if res.Header.Error != pb.XChainErrorEnum_SUCCESS {
		return nil, errors.New(res.Header.Error.String())
	}
	if res.Tx == nil {
		return nil, sdk.ErrTxNotFound
	}

	return res, nil
}

func (xc *xChain) QueryBlock(node, blockid string) (interface{}, error) {
	if node == "" || blockid == "" {
		return nil, fmt.Errorf("param invalid")
	}

	cli, conn, err := conn(node)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	if !xc.isInit() {
		return nil, fmt.Errorf("xChain is not init")
	}

	rawBlockid, err := hex.DecodeString(blockid)
	if err != nil {
		return nil, err
	}
	blockIDPB := &pb.BlockID{
		Header:      header(),
		Bcname:      xc.bcName,
		Blockid:     rawBlockid,
		NeedContent: true,
	}
	res, err := cli.GetBlock(context.TODO(), blockIDPB)
	if err != nil {
		return nil, err
	}
	if res.Header.Error != pb.XChainErrorEnum_SUCCESS {
		return nil, errors.New(res.Header.Error.String())
	}
	if res.Block == nil {
		return nil, errors.New("block not found")
	}
	return res, nil
}

func (xc *xChain) QueryStatus(node string) (interface{}, error) {
	if node == "" {
		return nil, fmt.Errorf("param invalid")
	}

	cli, conn, err := conn(node)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	if !xc.isInit() {
		return nil, fmt.Errorf("xChain is not init")
	}

	req := &pb.CommonIn{
		Header:     header(),
		ViewOption: pb.ViewOption_NONE,
	}
	res, err := cli.GetSystemStatus(context.TODO(), req)
	if err != nil {
		return nil, err
	}
	if res.Header.Error != pb.XChainErrorEnum_SUCCESS {
		return nil, errors.New(res.Header.Error.String())
	}
	status := res.SystemsStatus.BcsStatus
	if len(status) < 1 {
		return "", fmt.Errorf("no status")
	}

	return status[len(status)-1], nil
}

func header() *pb.Header {
	out := &pb.Header{
		Logid: utils.GenLogId(),
	}

	return out
}

func conn(host string) (pb.XchainClient, *grpc.ClientConn, error) {
	opts := make([]grpc.DialOption, 0)
	opts = append(opts, grpc.WithInsecure())
	opts = append(opts, grpc.WithMaxMsgSize(64<<20-1))
	c, err := grpc.Dial(host, opts...)
	if err != nil {
		return nil, c, err
	}

	return pb.NewXchainClient(c), c, nil
}

func preExecWithSelectUTXOContract(request *pb.InvokeRequest, ak *account.Account, from, bcName string, xcli pb.XchainClient) (*pb.InvokeRPCResponse, *pb.InvokeRPCRequest, string, error) {
	var initiator string
	authRequire := make([]string, 0, 1)
	if from != "" {
		authRequire = append(authRequire, from+"/"+ak.Address)
		initiator = from
	} else {
		authRequire = append(authRequire, ak.Address)
		initiator = ak.Address
	}

	req := &pb.InvokeRPCRequest{
		Header:      header(),
		Bcname:      bcName,
		Requests:    []*pb.InvokeRequest{request},
		Initiator:   initiator,
		AuthRequire: authRequire,
	}

	address := ak.Address
	if request.ModuleName == "xkernel" && request.MethodName == "Deploy" {
		address = from
	}

	preExecResp, err := xcli.PreExec(context.TODO(), req)
	if err != nil {
		return nil, nil, "", err
	}
	for _, res := range preExecResp.Response.Responses {
		if res.Status >= contract.StatusErrorThreshold {
			return nil, nil, "", fmt.Errorf("contract error status:%d message:%s", res.Status, res.Message)
		}
	}

	return preExecResp, req, address, nil
}

func generateTx(response *pb.PreExecWithSelectUTXOResponse, ak *account.Account, from string) *pb.Transaction {
	var initiator string
	if from != "" {
		initiator = from
	} else {
		initiator = ak.Address
	}
	tx := &pb.Transaction{
		Version:   1,
		Coinbase:  false,
		Desc:      []byte(""),
		Nonce:     utils.GenNonce(),
		Timestamp: time.Now().UnixNano(),
		Initiator: initiator,

		TxInputs:  response.GetResponse().UtxoInputs,
		TxOutputs: response.GetResponse().UtxoOutputs,

		ContractRequests: response.GetResponse().GetRequests(),
		TxInputsExt:      response.GetResponse().GetInputs(),
		TxOutputsExt:     response.GetResponse().GetOutputs(),
	}

	total := big.NewInt(0)
	for i := range tx.TxOutputs {
		amount := big.NewInt(0).SetBytes(tx.TxOutputs[i].GetAmount())
		total.Add(amount, total)
	}

	if response.GetResponse().GetGasUsed() > 0 {
		amount := big.NewInt(response.GetResponse().GetGasUsed())
		total.Add(amount, total)
		txFee := &pb.TxOutput{
			ToAddr: []byte("$"),
			Amount: amount.Bytes(),
		}
		tx.TxOutputs = append(tx.TxOutputs, txFee)
	}
	if total.Sign() > 0 {
		for _, utxo := range response.GetUtxoOutput().UtxoList {
			txInput := &pb.TxInput{
				RefTxid:   utxo.RefTxid,
				RefOffset: utxo.RefOffset,
				FromAddr:  utxo.ToAddr,
				Amount:    utxo.Amount,
			}
			tx.TxInputs = append(tx.TxInputs, txInput)
		}
		utxoTotal, _ := big.NewInt(0).SetString(response.GetUtxoOutput().TotalSelected, 10)
		if utxoTotal.Cmp(total) > 0 {
			delta := utxoTotal.Sub(utxoTotal, total)
			txCharge := &pb.TxOutput{
				ToAddr: []byte(ak.Address),
				Amount: delta.Bytes(),
			}
			tx.TxOutputs = append(tx.TxOutputs, txCharge)
		}
	}

	return tx
}

func signTx(tx *pb.Transaction, ak *account.Account, from string) *pb.Transaction {
	if from != "" {
		tx.AuthRequire = append(tx.AuthRequire, from+"/"+ak.Address)
	} else {
		tx.AuthRequire = append(tx.AuthRequire, ak.Address)
	}

	cryptoClient := crypto.GetCryptoClient()
	signTx, _ := common.ComputeTxSign(cryptoClient, tx, []byte(ak.PrivateKey))
	signInfo := &pb.SignatureInfo{
		PublicKey: ak.PublicKey,
		Sign:      signTx,
	}
	tx.InitiatorSigns = append(tx.InitiatorSigns, signInfo)
	tx.AuthRequireSigns = append(tx.AuthRequireSigns, signInfo)
	tx.Txid, _ = common.MakeTxId(tx)

	return tx
}
