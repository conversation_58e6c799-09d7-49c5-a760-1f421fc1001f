package xuperchain

import (
	"testing"
)

func TestWasmInvokeContract(t *testing.T) {
	var testInvokeWasmContracts = []struct {
		contractName    string
		contractAccount string
		node            string
		bcName          string
		adminSeed       string
	}{
		{
			adminSeed:       "络 超 抵 急 社 季 村 迟 贴 阅 暖 门",
			bcName:          "xuper",
			node:            "127.0.0.1:37101",
			contractName:    "counter",
			contractAccount: "******************@xuper",
		},
	}

	methodArgs := map[string]map[string][]byte{
		"increase": {
			"key": []byte("test"),
		},
	}
	for _, arg := range testInvokeWasmContracts {
		for method, args := range methodArgs {
			xchain := InitXchain(arg.adminSeed, arg.node, arg.bcName, arg.contractAccount)
			txId, err := xchain.InvokeContract(arg.contractName, method, args)
			t.Logf("InvokeContract txid: %v, err: %v", txId, err)
		}
	}
}

func TestQueryTx(t *testing.T) {
	txIds := []string{
		"d8259af219c8fb462eccb24a983c9b31e3918e55f22fbe6cd38512d2c86e8425",
		"d4ed477fc3a25e6de6e8ec277ee1acb6389517e46303e7280324aa39495944f2",
		"213a06fdb7d6326bdaa8705a20ac1c41915c23dbdc949e520c46e5ca718626e0",
		"8fd730c28c4eb79200518b434bbb2749c457c4c8cb7195918c529fa72d0232ba",
		"d874d3a8a9525714ccc25f3f138f09cab52edd72c3848d659292f8dcda1e8324",
		"03c0e035690e0ea60a975ba632e16bc0c982c164c8dfd6720d5bace2536c8f53",
	}

	xchain := InitXchain("弟 背 老 杀 风 湿 封 壳 瑞 隶 隶 门", "127.0.0.1:37101", "xuper", "******************@xuper")

	for _, txid := range txIds {
		status, err := xchain.QueryTx(txid)
		t.Logf("query tx status: %s, err: %v\n", status, err)
	}
}
