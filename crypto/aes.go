package crypto

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"fmt"
)

func PKCS7Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

func PKCS7UnPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

// 加密
func AesEncrypt(origData, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockSize := block.BlockSize()

	// 填充补齐
	origData = PKCS7Padding(origData, blockSize)

	// 加密
	blockMode := cipher.NewCBCEncrypter(block, key[:blockSize])
	crypted := make([]byte, len(origData))
	blockMode.CryptBlocks(crypted, origData)

	return crypted, nil
}

// 解密
func AesDecrypt(crypted, key []byte) ([]byte, error) {
	// 获取block size
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockSize := block.BlockSize()
	if len(crypted)%blockSize != 0 {
		return nil, fmt.Errorf("crypted not full blocks")
	}

	// 解密
	blockMode := cipher.NewCBCDecrypter(block, key[:blockSize])
	origData := make([]byte, len(crypted))
	blockMode.CryptBlocks(origData, crypted)

	// 校验解密后数据合法性，防止panic
	unpadding := int(origData[len(origData)-1])
	if len(origData) < blockSize || len(origData)%blockSize != 0 || unpadding > blockSize {
		return nil, fmt.Errorf("origin data error.block_size:%d length:%d unpadding:%d",
			blockSize, len(origData), unpadding)
	}

	// 去掉填充字符
	origData = PKCS7UnPadding(origData)
	return origData, nil
}
