package crypto

import (
	"encoding/base64"
)

func Base64Encode(content []byte) string {
	return base64.StdEncoding.EncodeToString(content)
}

func Base64Decode(content string) ([]byte, error) {
	return base64.StdEncoding.DecodeString(content)
}

func Base64UrlEncode(content []byte) string {
	return base64.RawURLEncoding.EncodeToString(content)
}

func Base64UrlDecode(content string) ([]byte, error) {
	return base64.RawURLEncoding.DecodeString(content)
}
