package crypto

import (
	"crypto/ecdsa"
	"github.com/xuperchain/crypto/core/account"
	"github.com/xuperchain/crypto/core/ecies"
	"github.com/xuperchain/crypto/core/sign"
)

// 使用ECC私钥来签名
func SignECDSA(k *ecdsa.PrivateKey, msg []byte) ([]byte, error) {
	signature, err := sign.SignECDSA(k, msg)
	return signature, err
}

// 使用ECC公钥来验证签名，验证统一签名的新签名函数
func VerifyECDSA(k *ecdsa.PublicKey, signature, msg []byte) (bool, error) {
	result, err := sign.VerifyECDSA(k, signature, msg)
	return result, err
}

// 使用椭圆曲线非对称加密
func EncryptByEcdsaKey(publicKey *ecdsa.PublicKey, msg []byte) (cypherText []byte, err error) {
	cypherText, err = ecies.Encrypt(publicKey, msg)
	return cypherText, err
}

// 使用椭圆曲线非对称解密
func DecryptByEcdsaKey(privateKey *ecdsa.PrivateKey, cypherText []byte) (msg []byte, err error) {
	msg, err = ecies.Decrypt(privateKey, cypherText)
	return msg, err
}

// 从json格式私钥内容字符串产生ECC私钥
func GetEcdsaPrivateKeyFromJsonStr(keyStr string) (*ecdsa.PrivateKey, error) {
	jsonBytes := []byte(keyStr)
	return account.GetEcdsaPrivateKeyFromJson(jsonBytes)
}

// 从json格式公钥内容字符串产生ECC公钥
func GetEcdsaPublicKeyFromJsonStr(keyStr string) (*ecdsa.PublicKey, error) {
	jsonBytes := []byte(keyStr)
	return account.GetEcdsaPublicKeyFromJson(jsonBytes)
}

// 使用单个公钥来生成钱包地址
func GetAddressFromPublicKey(key *ecdsa.PublicKey) (string, error) {
	address, err := account.GetAddressFromPublicKey(key)
	return address, err
}

// 验证钱包地址和公钥是否匹配
func VerifyAddressUsingPublicKeys(address string, pub *ecdsa.PublicKey) (bool, uint8) {
	return account.VerifyAddressUsingPublicKey(address, pub)
}

// 验证钱包地址合法的格式
func CheckAddressFormat(address string) bool {
	isValid, _ := account.CheckAddressFormat(address)
	return isValid
}
