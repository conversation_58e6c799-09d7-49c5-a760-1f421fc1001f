package vilg

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"sync"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type ViLGClient struct {
	ClientId     string
	ClientSecret string

	token       string
	tokenExpire time.Time
	tokenLock   sync.Mutex
}

func NewViLGClient(ak, sk string) *ViLGClient {
	return &ViLGClient{
		ClientId:     ak,
		ClientSecret: sk,
	}
}

func (t *ViLGClient) accessToken() (string, error) {
	t.tokenLock.Lock()
	defer t.tokenLock.Unlock()

	// 已存在且有效，直接返回
	if len(t.token) > 0 && !time.Now().After(t.tokenExpire) {
		return t.token, nil
	}

	// 否则重新生成
	param := map[string]string{
		"grant_type":    "client_credentials",
		"client_id":     t.ClientId,
		"client_secret": t.ClientSecret,
	}
	resp, err := t.doPost(apiAccessToken, param, "")
	if err != nil {
		return "", err
	}
	if resp.Code != ErrCodeOk {
		return "", fmt.Errorf("ernie returned error, code: %d, msg: %s", resp.Code, resp.Msg)
	}

	var token string
	if err := json.Unmarshal(resp.Data, &token); err != nil {
		return "", fmt.Errorf("failed to parse token from resp.Data, data: [%s], err: %v", resp.Data, err)
	}

	t.token = token
	t.tokenExpire = time.Now().Add(tokenExpire)

	return token, nil
}

func (t *ViLGClient) Text2Img(p *Text2ImgParam) (*Text2ImgResp, *BaseResp, error) {
	if err := p.Valid(); err != nil {
		return nil, nil, fmt.Errorf("invalid param: %v", err)
	}

	token, err := t.accessToken()
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get ernie accessToken: %v", err)
	}
	query := map[string]string{
		"access_token": token,
	}

	form := url.Values{}
	form.Set("text", p.Text)
	form.Set("style", supportedStyle[p.Style])
	form.Set("resolution", supportedResolution[p.Resolution])
	form.Set("num", strconv.FormatInt(p.Num, 10))
	form.Set("watermark", "0")

	rawResp, err := t.doPost(apiText2Img, query, form.Encode())
	if err != nil {
		return nil, nil, err
	}
	if rawResp.Code != ErrCodeOk {
		return nil, rawResp, fmt.Errorf("ernie returned error, code: %d, msg: %s", rawResp.Code, rawResp.Msg)
	}

	var result Text2ImgResp
	if err := json.Unmarshal(rawResp.Data, &result); err != nil {
		return nil, rawResp, fmt.Errorf("failed to parse token from resp.Data, data: [%s], err: %v", rawResp.Data, err)
	}

	return &result, rawResp, nil
}

func (t *ViLGClient) GetImg(taskId int64) (*GetImgResp, *BaseResp, error) {
	if taskId <= 0 {
		return nil, nil, fmt.Errorf("invalid taskId: %d", taskId)
	}

	token, err := t.accessToken()
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get ernie accessToken: %v", err)
	}
	query := map[string]string{
		"access_token": token,
	}

	form := url.Values{}
	form.Set("taskId", strconv.FormatInt(taskId, 10))

	rawResp, err := t.doPost(apiGetImg, query, form.Encode())
	if err != nil {
		return nil, nil, err
	}
	if rawResp.Code != ErrCodeOk {
		return nil, rawResp, fmt.Errorf("ernie returned error, code: %d, msg: %s", rawResp.Code, rawResp.Msg)
	}

	var result GetImgResp
	if err := json.Unmarshal(rawResp.Data, &result); err != nil {
		return nil, rawResp, fmt.Errorf("failed to parse token from resp.Data, data: [%s], err: %v", rawResp.Data, err)
	}

	return &result, rawResp, nil
}

// doPost post
func (t *ViLGClient) doPost(api string, param map[string]string, data string) (*BaseResp, error) {
	var result BaseResp

	reqUrl := fmt.Sprintf("https://%s%s", ernieHost, api)
	url, err := url.Parse(reqUrl)
	if err != nil {
		return nil, fmt.Errorf("invalid url: %s", reqUrl)
	}
	query := url.Query()
	for k, v := range param {
		query.Set(k, v)
	}
	url.RawQuery = query.Encode()

	header := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
	}
	resp, err := httpclient.Post(url.String(), header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to request ernie url:%s err:%v", reqUrl, err)
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("ernie return http code %d", resp.StatusCode)
	}
	if err := json.Unmarshal(resp.Body, &result); err != nil {
		return nil, fmt.Errorf("failed to parse ernie result: [%s], err:%v", resp.Body, err)
	}

	return &result, nil
}
