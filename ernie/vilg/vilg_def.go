package vilg

import (
	"encoding/json"
	"fmt"
	"time"
	"unicode/utf8"
)

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000

	tokenExpire = time.Hour * 20

	maxTextLength = 100

	ernieHost = "wenxin.baidu.com"
)

const (
	apiAccessToken = "/moduleApi/portal/api/oauth/token"
	apiText2Img    = "/moduleApi/portal/api/rest/1.0/ernievilg/v1/txt2img"
	apiGetImg      = "/moduleApi/portal/api/rest/1.0/ernievilg/v1/getImg"
)

const (
	StatusProcessing = 0
	StatusFinished   = 1
)

var supportedStyle = map[int64]string{
	1:  "古风",
	2:  "二次元",
	3:  "写实风格",
	4:  "浮世绘",
	5:  "low poly",
	6:  "未来主义",
	7:  "像素风格",
	8:  "概念艺术",
	9:  "赛博朋克",
	10: "洛丽塔风格",
	11: "巴洛克风格",
	12: "超现实主义",
	13: "水彩画",
	14: "蒸汽波艺术",
	15: "油画",
	16: "卡通画",
}

var supportedResolution = map[int64]string{
	1: "1024*1024",
	2: "1024*1536",
	3: "1536*1024",
}

const (
	ErrCodeOk           = 0
	ErrCodeQPSExceed    = 18   // QPS 访问超限
	ErrCodeInvalidParam = 4001 // 请求参数格式错误
	ErrCodeInvalidInput = 4002 // 请求参数格式错误，请检查必传参数是否齐全，参数类型等
	ErrCodeInvalidStype = 4003 // 请求参数中，图片风格不在可选范围内
	ErrCodeInternal     = 4004 // API服务内部错误，可能引起原因有请求超时、模型推理错误等
	ErrCodeAuditFailed  = 1    // 请求内容触发黄反算子和敏感词过滤，提示文本：暂不支持创作该内容，请修改后再试
)

type BaseResp struct {
	Code int64           `json:"code"`
	Msg  string          `json:"msg"`
	Data json.RawMessage `json:"data"`
}

type Text2ImgParam struct {
	Text       string
	Style      int64
	Resolution int64
	Num        int64
}

func (p *Text2ImgParam) Valid() error {
	if len(p.Text) == 0 {
		return fmt.Errorf("empty input text")
	}
	words := utf8.RuneCountInString(p.Text)
	if words == 0 || words > maxTextLength {
		return fmt.Errorf("invalid text length [%d], expected to range from 1 to %d", words, maxTextLength)
	}

	if _, exist := supportedStyle[p.Style]; !exist {
		return fmt.Errorf("unsupported style: %d", p.Style)
	}

	if _, exist := supportedResolution[p.Resolution]; !exist {
		return fmt.Errorf("unsupported resolution: %d", p.Resolution)
	}

	if p.Num <= 0 || p.Num > 6 {
		return fmt.Errorf("unsupported num [%d], expected to range from 1 to 6", p.Num)
	}

	return nil
}

type Text2ImgResp struct {
	RequestId string `json:"requestId"`
	TaskId    int64  `json:"taskId"`
}

type GetImgResp struct {
	Img        string `json:"img"`
	Waiting    string `json:"waiting"`
	CreateTime string `json:"createTime"`
	RequestId  string `json:"requestId"`
	Style      string `json:"style"`
	Text       string `json:"text"`
	Resolution string `json:"resolution"`
	TaskId     int64  `json:"taskId"`
	Status     int64  `json:"status"`
	ImgUrls    []struct {
		Image string      `json:"image"`
		Score interface{} `json:"score"`
	} `json:"imgUrls"`
}
