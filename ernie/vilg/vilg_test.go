package vilg

import (
	"encoding/json"
	"fmt"
	"testing"
)

const (
	testAK = "RRKKmeO0iwxWQAz4aHGa5lwdPEIeQXMG"
	testSK = ""
)

func getClient() *ViLGClient {
	return NewViLGClient(testAK, testSK)
}

func TestGetToken(t *testing.T) {
	cli := getClient()

	token, err := cli.accessToken()
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println("token:", token)
}

func TestText2Img(t *testing.T) {
	cli := getClient()

	param := Text2ImgParam{
		Text:       "一栋别墅，简洁大方，有花园，有栅栏，花园里有一片草地，两个孩子正在玩耍",
		Style:      15,
		Resolution: 3,
		Num:        1,
	}
	result, raw, err := cli.Text2Img(&param)
	if err != nil {
		if raw != nil {
			fmt.Printf("Code: %d, Msg: %s\n", raw.Code, raw.Msg)
		}
		t.Fatal(err)
	}

	fmt.Println("taskId:", result.TaskId)
}

func TestGetImg(t *testing.T) {
	cli := getClient()

	var taskId int64 = 14506396

	result, raw, err := cli.GetImg(taskId)
	if err != nil {
		if raw != nil {
			fmt.Printf("Code: %d, Msg: %s\n", raw.Code, raw.Msg)
		}
		t.Fatal(err)
	}

	if result.Status == StatusProcessing {
		fmt.Println("still need to wait:", result.Waiting)
	} else {
		fmt.Println("url:", result.Img)
		bs, _ := json.MarshalIndent(result, "", "  ")
		fmt.Println(string(bs))
	}
}

func TestText2ImgParam(t *testing.T) {
	cases := []struct {
		p  Text2ImgParam
		ok bool
	}{
		{Text2ImgParam{"一幅画", 1, 1, 1}, true},
		{Text2ImgParam{"", 1, 1, 1}, false},
		{Text2ImgParam{"一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十", 1, 1, 1}, true},
		{Text2ImgParam{"一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十零", 1, 1, 1}, false},
		{Text2ImgParam{"一幅画", 16, 1, 1}, true},
		{Text2ImgParam{"一幅画", 0, 1, 1}, false},
		{Text2ImgParam{"一幅画", -1, 1, 1}, false},
		{Text2ImgParam{"一幅画", 17, 1, 1}, false},
		{Text2ImgParam{"一幅画", 1, 3, 1}, true},
		{Text2ImgParam{"一幅画", 1, 0, 1}, false},
		{Text2ImgParam{"一幅画", 1, -1, 1}, false},
		{Text2ImgParam{"一幅画", 1, 4, 1}, false},
		{Text2ImgParam{"一幅画", 1, 1, 6}, true},
		{Text2ImgParam{"一幅画", 1, 1, 0}, false},
		{Text2ImgParam{"一幅画", 1, 1, -1}, false},
		{Text2ImgParam{"一幅画", 1, 1, 7}, false},
	}

	for i, c := range cases {
		err := c.p.Valid()
		if (err != nil && c.ok) ||
			(err == nil && !c.ok) {
			t.Fatal(fmt.Sprintf("invalid case: %d", i))
		}
	}
}
