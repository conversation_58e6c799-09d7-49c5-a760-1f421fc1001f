package vilgv2

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type ViLGClient struct {
	ClientId     string
	ClientSecret string

	token       string
	tokenExpire time.Time
	tokenLock   sync.Mutex
}

func NewViLGClient(ak, sk string) *ViLGClient {
	return &ViLGClient{
		ClientId:     ak,
		ClientSecret: sk,
	}
}

func (t *ViLGClient) accessToken() (string, error) {
	t.tokenLock.Lock()
	defer t.tokenLock.Unlock()

	// 已存在且有效，直接返回
	if len(t.token) > 0 && !time.Now().After(t.tokenExpire) {
		return t.token, nil
	}

	// 否则重新生成
	param := map[string]string{
		"grant_type":    "client_credentials",
		"client_id":     t.ClientId,
		"client_secret": t.ClientSecret,
	}
	reqRes, err := t.doPost(apiAccessToken, param, "")
	if err != nil {
		return "", err
	}
	var result GetTokenResp
	if err = json.Unmarshal([]byte(reqRes.Body), &result); err != nil {
		return "", fmt.Errorf("json unmarshal get token response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	if len(result.Error) != 0 {
		return "", fmt.Errorf("get token error, error: %s, error_description: %s",
			result.Error, result.ErrorDescription)
	}

	t.token = result.AccessToken
	t.tokenExpire = time.Now().Add(tokenExpire)

	return t.token, nil
}

func (t *ViLGClient) Txt2Img(p *Txt2ImgParam) (*Txt2ImgResp, error) {
	if err := p.Valid(); err != nil {
		return nil, fmt.Errorf("invalid param: %v", err)
	}

	token, err := t.accessToken()
	if err != nil {
		return nil, fmt.Errorf("failed to get ernie accessToken: %v", err)
	}
	query := map[string]string{
		"access_token": token,
	}

	size := strings.Split(supportedResolution[p.Resolution], "*")
	if len(size) != 2 {
		return nil, fmt.Errorf("supportedResolution error, size: %v", size)
	}
	width, err := strconv.Atoi(size[0])
	if err != nil {
		return nil, fmt.Errorf("atoi err: %v", err)
	}
	height, err := strconv.Atoi(size[1])
	if err != nil {
		return nil, fmt.Errorf("atoi err: %v", err)
	}

	reqBody := Txt2ImgReq{
		Prompt:       p.Prompt,
		Version:      "v2",
		Width:        width,
		Height:       height,
		ImageNum:     p.ImageNum,
		Image:        p.Image,
		Url:          p.Url,
		PdfFile:      p.PdfFile,
		PdfFileNum:   p.PdfFileNum,
		ChangeDegree: p.ChangeDegree,
	}
	body, _ := json.Marshal(reqBody)

	reqRes, err := t.doPost(apiTxt2Img, query, string(body))
	if err != nil {
		return nil, err
	}
	var result Txt2ImgResp
	if err = json.Unmarshal([]byte(reqRes.Body), &result); err != nil {
		return nil, fmt.Errorf("failed to parse Txt2ImgResp from reqRes.Body, body: [%s], err: %v",
			reqRes.Body, err)
	}
	if result.ErrorCode != 0 {
		return &result, fmt.Errorf("ernie returned error, ErrorCode: %d, ErrorMsg: %s",
			result.ErrorCode, result.ErrorMsg)
	}

	return &result, nil
}

func (t *ViLGClient) GetImg(taskId int64) (*GetImgResp, error) {
	if taskId <= 0 {
		return nil, fmt.Errorf("invalid taskId: %d", taskId)
	}

	token, err := t.accessToken()
	if err != nil {
		return nil, fmt.Errorf("failed to get ernie accessToken: %v", err)
	}
	query := map[string]string{
		"access_token": token,
	}

	reqBody := GetImgParam{
		TaskId: strconv.Itoa(int(taskId)),
	}
	body, _ := json.Marshal(reqBody)

	reqRes, err := t.doPost(apiGetImg, query, string(body))
	if err != nil {
		return nil, err
	}
	var result GetImgResp
	if err = json.Unmarshal([]byte(reqRes.Body), &result); err != nil {
		return nil, fmt.Errorf("failed to parse GetImgResp from reqRes.Body, body: [%s], err: %v",
			reqRes.Body, err)
	}
	if result.ErrorCode != 0 {
		return &result, fmt.Errorf("ernie returned error, ErrorCode: %d, ErrorMsg: %s",
			result.ErrorCode, result.ErrorMsg)
	}

	return &result, nil
}

// doPost post
func (t *ViLGClient) doPost(api string, param map[string]string, data string) (*RequestRes, error) {
	reqUrl := fmt.Sprintf("https://%s%s", ernieHost, api)
	url, err := url.Parse(reqUrl)
	if err != nil {
		return nil, fmt.Errorf("invalid url: %s", reqUrl)
	}
	query := url.Query()
	for k, v := range param {
		query.Set(k, v)
	}
	url.RawQuery = query.Encode()

	header := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
	}
	resp, err := httpclient.Post(url.String(), header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to request ernie url:%s err:%v", reqUrl, err)
	}
	result := &RequestRes{
		ReqUrl: url.String(),
	}
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode != http.StatusOK {
		return result, fmt.Errorf("ernie return http code %d.url:%s", resp.StatusCode, reqUrl)
	}

	return result, nil
}
