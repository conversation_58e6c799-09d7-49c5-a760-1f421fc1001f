package vilgv2

import (
	"encoding/json"
	"fmt"
	"testing"
)

const (
	testAK = "aGhYFbiApoF9DTxGNMRkuUoM"
	testSK = ""
)

func getClient() *ViLGClient {
	return NewViLGClient(testAK, testSK)
}

func TestGetToken(t *testing.T) {
	cli := getClient()

	token, err := cli.accessToken()
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println("token:", token)
}

func TestTxt2Img(t *testing.T) {
	cli := getClient()

	param := Txt2ImgParam{
		Prompt:     "一栋别墅，简洁大方，有花园，有栅栏，花园里有一片草地，两个孩子正在玩耍，像素风格",
		ImageNum:   1,
		Resolution: 4,
	}
	result, err := cli.Txt2Img(&param)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println("taskId: ", result.Data.TaskId)
}

func TestGetImg(t *testing.T) {
	cli := getClient()

	var taskId int64 = 1672873253805987847

	result, err := cli.GetImg(taskId)
	if err != nil {
		t.Fatal(err)
	}

	if result.Data.TaskProgress == StatusProcessing {
		fmt.Println("still need to wait:")
	} else {
		fmt.Println("url:", result.Data.SubTaskResultList)
		bs, _ := json.MarshalIndent(result, "", "  ")
		fmt.Println(string(bs))
	}
}
