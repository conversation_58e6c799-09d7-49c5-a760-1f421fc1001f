package vilgv2

import (
	"fmt"
	"net/http"
	"time"
	"unicode/utf8"
)

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000

	tokenExpire = time.Hour * 24 * 25 // token有效期30天

	maxTextLength = 200

	ernieHost = "aip.baidubce.com"
)

const (
	apiAccessToken = "/oauth/2.0/token"
	apiTxt2Img     = "/rpc/2.0/ernievilg/v1/txt2imgv2"
	apiGetImg      = "/rpc/2.0/ernievilg/v1/getImgv2"
)

const (
	StatusProcessing = 0
	StatusFinished   = 1
)

const (
	TaskErrCodeInvalidParam           = 282004 // 请求中包含非法参数或字数超限，请检查后重新尝试
	TaskErrCodeMissParam              = 282003 // 缺少必要参数
	TaskErrCodeFlowControl            = 17     // 日配额流量超限，错误信息为中文的“用量超限”指单个用户使用 AI 作画的用量超限
	TaskErrCodeQpsLimit               = 18     // QPS 超限额
	TaskErrCodeRecognize              = 216630 // 服务器内部错误，请再次请求，如果持续出现此类错误，请通过工单联系技术支持
	SubTaskErrCodeInvalidParam        = 501    // 文本黄反拦截
	SubTaskErrCodeGenImgFail          = 201    // 模型生图失败
	TaskErrCodeInvalidParamForm       = 216100 // 参数不满足格式要求
	TaskErrCodeReqLimit               = 4      // 错误信息为中文的“请求超限”指所有用户提交的 AI 作画总数超限制
	TaskErrCodeSingleQpsLimit         = 13     // 错误信息为中文的“QPS 超限”指单个用户使用提交请求接口的 QPS 超限
	TaskErrCodeSingleConcurrencyLimit = 15     // 错误信息为中文的“并发超限”指单个用户使用 AI 作画的并发超限
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type GetTokenResp struct {
	Error            string `json:"error"`
	ErrorDescription string `json:"error_description"`
	AccessToken      string `json:"access_token"`
	ExpiresIn        int    `json:"expires_in"`
}

type Txt2ImgParam struct {
	Prompt       string `json:"prompt"`
	Resolution   int    `json:"resolution"`
	ImageNum     int    `json:"image_num"`
	Image        string `json:"image"`
	Url          string `json:"url"`
	PdfFile      string `json:"pdf_file"`
	PdfFileNum   string `json:"pdf_file_num"`
	ChangeDegree int    `json:"change_degree"`
}

type Txt2ImgReq struct {
	Prompt       string `json:"prompt"`
	Version      string `json:"version"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	Style        string `json:"style"`
	ImageNum     int    `json:"image_num"`
	Image        string `json:"image,omitempty"`
	Url          string `json:"url,omitempty"`
	PdfFile      string `json:"pdf_file,omitempty"`
	PdfFileNum   string `json:"pdf_file_num,omitempty"`
	ChangeDegree int    `json:"change_degree,omitempty"`
}

var supportedResolution = map[int]string{
	1: "512*512",
	2: "640*360",
	3: "360*640",
	4: "1024*1024",
	5: "1280*720",
	6: "720*1280",
	7: "2048*2048",
	8: "2560*1440",
	9: "1440*2560",
}

func (p *Txt2ImgParam) Valid() error {
	if len(p.Prompt) == 0 {
		return fmt.Errorf("empty input prompt")
	}
	words := utf8.RuneCountInString(p.Prompt)
	if words == 0 || words > maxTextLength {
		return fmt.Errorf("invalid prompt length [%d], expected to range from 1 to %d", words, maxTextLength)
	}
	if _, exist := supportedResolution[p.Resolution]; !exist {
		return fmt.Errorf("unsupported resolution: %d", p.Resolution)
	}
	if p.ImageNum <= 0 || p.ImageNum > 8 {
		return fmt.Errorf("unsupported num [%d], expected to range from 1 to 8", p.ImageNum)
	}
	if (len(p.Image) != 0 || len(p.Url) != 0 || len(p.PdfFile) != 0) && p.ChangeDegree == 0 {
		return fmt.Errorf("change degree is necessary when using image, url or pdf")
	}
	return nil
}

type Txt2ImgResp struct {
	LogId     uint64          `json:"log_id"`
	ErrorCode int64           `json:"error_code"`
	ErrorMsg  string          `json:"error_msg"`
	Data      Txt2ImgRespData `json:"data"`
}

type Txt2ImgRespData struct {
	TaskId        string `json:"task_id"`
	PrimaryTaskId uint64 `json:"primary_task_id"`
}

type GetImgParam struct {
	TaskId string `json:"task_id"`
}

type GetImgResp struct {
	LogId     uint64         `json:"log_id"`
	ErrorCode int64          `json:"error_code"`
	ErrorMsg  string         `json:"error_msg"`
	Data      GetImgRespData `json:"data"`
}

type GetImgRespData struct {
	TaskId            uint64          `json:"task_id"`
	TaskStatus        string          `json:"task_status"`
	TaskProgress      int             `json:"task_progress"`
	SubTaskResultList []SubTaskResult `json:"sub_task_result_list"`
}

type SubTaskResult struct {
	SubTaskStatus    string       `json:"sub_task_status"`
	SubTaskProgress  int          `json:"sub_task_progress"`
	SubTaskErrorCode int          `json:"sub_task_error_code"`
	FinalImageList   []FinalImage `json:"final_image_list"`
}

type FinalImage struct {
	ImgApproveConclusion string `json:"img_approve_conclusion"`
	ImgURL               string `json:"img_url"`
	Width                int    `json:"width"`
	Height               int    `json:"height"`
}
