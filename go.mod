module icode.baidu.com/baidu/blockchain/xasset-golib

go 1.14

require (
	github.com/baidubce/bce-sdk-go v0.9.80
	github.com/go-sql-driver/mysql v1.6.0
	github.com/golang/protobuf v1.5.2 // indirect
	github.com/google/uuid v1.3.0
	github.com/mediocregopher/radix.v2 v0.0.0-20181115013041-b67df6e626f9
	github.com/prometheus/client_golang v1.10.0 // indirect
	github.com/prometheus/common v0.21.0 // indirect
	github.com/satori/go.uuid v1.2.0
	github.com/spf13/afero v1.5.1
	github.com/stretchr/testify v1.7.0
	github.com/xuperchain/crypto v0.0.0-20211224062819-eca101aeda3f
	github.com/xuperchain/xuper-sdk-go/v2 v2.0.0-20210722084115-86d72d395950
	github.com/xuperchain/xuperchain v0.0.0-20210708031936-951e4ade7bdd
	github.com/xuperchain/xupercore v0.0.0-20210918081251-05788c449a44
	golang.org/x/text v0.3.6
	google.golang.org/grpc v1.39.0
	gopkg.in/cenkalti/backoff.v1 v1.1.0
	icode.baidu.com/baidu/go-lib/bns v0.0.0-20210127100500-f7f1cb287d8f
)
