/*
 * File         : httpclient.go
 * Date         : 2015-12-28 10:16:02
 * Last modified: 2016-04-19 17:52:12
 */
package httpclient

import (
	"crypto/tls"
	"errors"
	"io/ioutil"
	"net"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"
)

// 可选项
const (
	OptDisableFollowLocation         = "DisableFollowLocation"
	OptDisableCompression            = "DisableCompression"
	OptDisableAutoSetPostContentType = "DisableAutoSetPostContentType"
	OptTlsSipVerify                  = "TlsSkipVerify"
)

type HttpResponse struct {
	StatusCode int
	Header     http.Header
	Body       []byte
}

var DisableRedirectError = errors.New("Don't redirect!")

func noRedirect(req *http.Request, via []*http.Request) error {
	return DisableRedirectError
}

func SendRequest(method string, requrl string, header map[string]string, ConnectTimeoutMs time.Duration,
	ReadWriteTimeoutMs time.Duration, data string, opt map[string]string) (HttpResponse, error) {
	disableCompression := false
	checkRedirect := noRedirect
	if v, ok := opt[OptDisableFollowLocation]; !ok || v != "1" {
		checkRedirect = nil
	}
	if v, ok := opt[OptDisableCompression]; ok && v == "1" {
		disableCompression = true
	}

	transport := &http.Transport{
		Dial: func(netw, addr string) (net.Conn, error) {
			conn, err := net.DialTimeout(netw, addr, ConnectTimeoutMs*time.Millisecond)
			if err != nil {
				return nil, err
			}
			conn.SetDeadline(time.Now().Add(ReadWriteTimeoutMs * time.Millisecond))
			return conn, nil
		},
		//ResponseHeaderTimeout: time.Second * 2,
		MaxIdleConnsPerHost: -1,
		DisableCompression:  disableCompression,
		//DisableKeepAlives: true,
	}

	// tls is skip verify
	if v, ok := opt[OptTlsSipVerify]; ok && v == "1" {
		transport.TLSClientConfig = &tls.Config{InsecureSkipVerify: true}
	}

	client := &http.Client{
		Transport:     transport,
		CheckRedirect: checkRedirect,
	}
	var res HttpResponse
	var request *http.Request
	var err error
	if method == "POST" {
		request, err = http.NewRequest(method, requrl, strings.NewReader(data))
		autoSetPostContentType := true
		if v, ok := opt[OptDisableAutoSetPostContentType]; ok && v == "1" {
			autoSetPostContentType = false
		}
		if autoSetPostContentType && request != nil {
			request.Header.Set("Content-Type", "application/x-www-form-urlencoded;charset=utf-8")
		}
	} else if method == "PUT" {
		request, err = http.NewRequest(method, requrl, strings.NewReader(data))
	} else {
		request, err = http.NewRequest(method, requrl, nil)
	}

	if err != nil {
		return res, err
	}

	for k, v := range header {
		if strings.EqualFold(k, "host") {
			request.Host = v
		} else {
			request.Header.Set(k, v)
		}
	}

	response, err := client.Do(request)
	if response != nil {
		res.StatusCode = response.StatusCode
		res.Header = response.Header
	}
	if err != nil {
		if urlError, ok := err.(*url.Error); ok && urlError.Err == DisableRedirectError {
			return res, nil //Discard body and response.Body.Close() in go src when status is 302, ft
		}
		return res, err
	}

	defer response.Body.Close()
	res.Body, err = ioutil.ReadAll(response.Body)
	if err != nil {
		return res, err
	}
	return res, nil
}

func Post(url string, header map[string]string, ConnectTimeoutMs,
	ReadWriteTimeoutMs time.Duration, data string, opts ...map[string]string) (HttpResponse, error) {
	var opt map[string]string
	if len(opts) == 1 {
		opt = opts[0]
	}
	return SendRequest("POST", url, header, ConnectTimeoutMs, ReadWriteTimeoutMs, data, opt)
}

func Put(url string, header map[string]string, ConnectTimeoutMs,
	ReadWriteTimeoutMs time.Duration, data string, opts ...map[string]string) (HttpResponse, error) {
	var opt map[string]string
	if len(opts) == 1 {
		opt = opts[0]
	}
	return SendRequest("PUT", url, header, ConnectTimeoutMs, ReadWriteTimeoutMs, data, opt)
}

func Get(url string, header map[string]string, ConnectTimeoutMs,
	ReadWriteTimeoutMs time.Duration, opts ...map[string]string) (HttpResponse, error) {
	var opt map[string]string
	if len(opts) == 1 {
		opt = opts[0]
	}
	return SendRequest("GET", url, header, ConnectTimeoutMs, ReadWriteTimeoutMs, "", opt)
}

func Head(url string, header map[string]string, ConnectTimeoutMs,
	ReadWriteTimeoutMs time.Duration, opts ...map[string]string) (HttpResponse, error) {
	var opt map[string]string
	if len(opts) == 1 {
		opt = opts[0]
	}
	return SendRequest("HEAD", url, header, ConnectTimeoutMs, ReadWriteTimeoutMs, "", opt)
}

func Delete(url string, header map[string]string, ConnectTimeoutMs,
	ReadWriteTimeoutMs time.Duration, opts ...map[string]string) (HttpResponse, error) {
	var opt map[string]string
	if len(opts) == 1 {
		opt = opts[0]
	}
	return SendRequest("DELETE", url, header, ConnectTimeoutMs, ReadWriteTimeoutMs, "", opt)
}

func MultiGet(urls []string, header map[string]string, ConnectTimeoutMs time.Duration,
	ReadWriteTimeoutMs time.Duration, opts ...map[string]string) ([]HttpResponse, error) {
	var opt map[string]string
	if len(opts) == 1 {
		opt = opts[0]
	}

	url_len := len(urls)
	res := make([]HttpResponse, url_len)
	ch := make(chan error, url_len)
	var wg sync.WaitGroup
	wg.Add(url_len)
	for i, url := range urls {
		go func(i int, url string) {
			defer wg.Done()
			var err error
			res[i], err = SendRequest("GET", url, header, ConnectTimeoutMs, ReadWriteTimeoutMs, "", opt)
			if err != nil {
				ch <- err
				return
			}
			ch <- nil
		}(i, url)
	}
	wg.Wait()
	for _ = range res {
		if err := <-ch; err != nil {
			return nil, err
		}
	}
	return res, nil
}

func MultiPost(urls []string, header map[string]string, ConnectTimeoutMs time.Duration,
	ReadWriteTimeoutMs time.Duration, datas []string, opts ...map[string]string) ([]HttpResponse, error) {
	var opt map[string]string
	if len(opts) == 1 {
		opt = opts[0]
	}

	url_len := len(urls)
	res := make([]HttpResponse, url_len)
	ch := make(chan error, url_len)
	var wg sync.WaitGroup
	wg.Add(url_len)
	for i, url := range urls {
		go func(i int, url string) {
			defer wg.Done()
			var err error
			res[i], err = SendRequest("POST", url, header, ConnectTimeoutMs, ReadWriteTimeoutMs, datas[i], opt)
			if err != nil {
				ch <- err
				return
			}
			ch <- nil
		}(i, url)
	}
	wg.Wait()
	for _ = range res {
		if err := <-ch; err != nil {
			return nil, err
		}
	}
	return res, nil
}

/*for keeplive client*/

//demo
/*
	var connectTimeoutMs time.Duration = 100    //100ms
	var readwriteTimeoutMs time.Duration = 2000 //2000ms
	client := httpclient.NewHTTPRequest(connectTimeoutMs, readwriteTimeoutMs)
	client.SetMaxIdleConnsPerHost(5)
	//client.SetDisableCompression()
	//client.SetDisableRedirect()
	client.BuildClient()

	for i := 0; i < 10; i++ {
		response, err := client.SendRequest("GET", "http://************:8375", nil, "")
		fmt.Println(err)
		fmt.Println(response.StatusCode)
		fmt.Println(response.Header)
		fmt.Println(string(response.Body))
	}
*/

type HTTPRequest struct {
	connectTimeoutMs    time.Duration
	readWriteTimeoutMs  time.Duration
	maxIdleConnsPerHost int
	disableCompression  bool
	checkRedirect       func(req *http.Request, via []*http.Request) error
	client              *http.Client
}

func NewHTTPRequest(ConnectTimeoutMs, ReadWriteTimeoutMs time.Duration) *HTTPRequest {
	return &HTTPRequest{
		connectTimeoutMs:    ConnectTimeoutMs * time.Millisecond,
		readWriteTimeoutMs:  ReadWriteTimeoutMs * time.Millisecond,
		maxIdleConnsPerHost: 2,
		disableCompression:  false,
		checkRedirect:       nil,
	}
}

func (x *HTTPRequest) SetMaxIdleConnsPerHost(poolNum int) {
	x.maxIdleConnsPerHost = poolNum
}

func (x *HTTPRequest) SetDisableCompression() {
	x.disableCompression = true
}

func (x *HTTPRequest) SetDisableRedirect() {
	x.checkRedirect = noRedirect
}
func (x *HTTPRequest) BuildClient() {
	x.client = &http.Client{
		Transport: &Transport{
			ConnectTimeout:      x.connectTimeoutMs,
			RequestTimeout:      x.readWriteTimeoutMs,
			MaxIdleConnsPerHost: x.maxIdleConnsPerHost,
			DisableCompression:  x.disableCompression,
		},
		CheckRedirect: x.checkRedirect,
	}
}

func (x *HTTPRequest) SendRequest(method string, requrl string, header map[string]string,
	data string) (HttpResponse, error) {
	var res HttpResponse
	var request *http.Request
	var err error
	if method == "POST" {
		request, err = http.NewRequest(method, requrl, strings.NewReader(data))
		request.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	} else if method == "PUT" {
		request, err = http.NewRequest(method, requrl, strings.NewReader(data))
	} else {
		request, err = http.NewRequest(method, requrl, nil)
	}

	if err != nil {
		return res, err
	}

	for k, v := range header {
		if strings.EqualFold(k, "host") {
			request.Host = v
		} else {
			request.Header.Set(k, v)
		}
	}
	response, err := x.client.Do(request)
	if response != nil {
		res.StatusCode = response.StatusCode
		res.Header = response.Header
	}
	if err != nil {
		if urlError, ok := err.(*url.Error); ok && urlError.Err == DisableRedirectError {
			return res, nil //Discard body and response.Body.Close() in go src when status is 302, ft
		}
		return res, err
	}

	defer response.Body.Close()
	res.Body, err = ioutil.ReadAll(response.Body)
	if err != nil {
		return res, err
	}
	return res, nil
}
