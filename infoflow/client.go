package infoflow

import (
	"encoding/json"
	"fmt"
	"net/url"
	"strings"

	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type Client struct {
	*InfoFlowClientConfig
}

func NewClient(cfg *InfoFlowClientConfig) *Client {
	return &Client{
		InfoFlowClientConfig: cfg,
	}
}

func (c *Client) GetAccessToken() (string, error) {
	reqUrl, err := url.Parse(fmt.Sprintf("%s%s", c.TokenEndpoint, TokenApi))
	if err != nil {
		return "", fmt.Errorf("parse url fail, err: %v", err)
	}
	v := url.Values{}
	v.Set("corpid", c.CorpId)
	v.Set("corpsecret", c.CorpSecret)
	reqUrl.RawQuery = v.Encode()
	respBody, err := c.doGet(reqUrl.String())
	if err != nil {
		return "", fmt.<PERSON>rrorf("request get access token fail. err: %v", err)
	}
	resp := new(GetAccessTokenResponse)
	if err := json.Unmarshal(respBody, resp); err != nil {
		return "", fmt.Errorf("unmarshal resp body fail, err: %v", err)
	}
	if resp.ErrCode != 0 {
		return "", fmt.Errorf("resp code %v, errMsg %s", resp.ErrCode, resp.ErrMsg)
	}
	return resp.AccessToken, nil
}

func (c *Client) SendTextMsg(p *SendTextMsgParam) error {
	if !p.Valid() {
		return fmt.Errorf("param error")
	}
	reqUrl, err := url.Parse(fmt.Sprintf("%s%s", c.SendEndpoint, SendApi))
	if err != nil {
		return fmt.Errorf("parse url fail, err: %v", err)
	}
	token := p.AccessToken
	if len(token) == 0 {
		token, err = c.GetAccessToken()
		if err != nil {
			return fmt.Errorf("get token fail, err: %v", err)
		}
	}
	v := url.Values{}
	v.Set("access_token", token)
	reqUrl.RawQuery = v.Encode()
	reqBody := SendTextMsgRequestBody{
		MsgType: MsgTypeText,
		AgentId: p.AgentId,
		Text:    Text{Content: p.Content},
	}
	if len(p.ToUser) != 0 {
		reqBody.ToUser = strings.Join(p.ToUser, "|")
	}
	if len(p.ToParty) != 0 {
		reqBody.ToParty = strings.Join(p.ToParty, "|")
	}
	if len(p.ToTag) != 0 {
		reqBody.ToUser = strings.Join(p.ToTag, "|")
	}
	body, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("marshal req body fail, err: %v", err)
	}
	respBody, err := c.doPost(reqUrl.String(), string(body))
	if err != nil {
		return fmt.Errorf("request send text msg fail. err: %v", err)
	}
	resp := new(SendMsgResponse)
	if err := json.Unmarshal(respBody, resp); err != nil {
		return fmt.Errorf("unmarshal resp body fail, err: %v", err)
	}
	if resp.ErrCode != 0 {
		return fmt.Errorf("resp code %v, errMsg %s", resp.ErrCode, resp.ErrMsg)
	}
	return nil
}

func (c *Client) IsFollow(p *IsFollowParam) ([]UserFollow, error) {
	if !p.Valid() {
		return nil, fmt.Errorf("param error")
	}
	reqUrl, err := url.Parse(fmt.Sprintf("%s%s", c.TokenEndpoint, IsFollowApi))
	if err != nil {
		return nil, fmt.Errorf("parse url fail, err: %v", err)
	}
	token := p.AccessToken
	if len(token) == 0 {
		token, err = c.GetAccessToken()
		if err != nil {
			return nil, fmt.Errorf("get token fail, err: %v", err)
		}
	}
	v := url.Values{}
	v.Set("access_token", token)
	reqUrl.RawQuery = v.Encode()
	reqBody := IsFollowRequestBody{
		AgentId: p.AgentId,
		UserIds: p.UserIds,
	}
	body, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("marshal req body fail, err: %v", err)
	}
	respBody, err := c.doPost(reqUrl.String(), string(body))
	if err != nil {
		return nil, fmt.Errorf("request is follow fail. err: %v", err)
	}
	resp := new(IsFollowResponse)
	if err := json.Unmarshal(respBody, resp); err != nil {
		return nil, fmt.Errorf("unmarshal resp body fail, err: %v", err)
	}
	if resp.ErrCode != 0 {
		return nil, fmt.Errorf("resp code %v, errMsg %s", resp.ErrCode, resp.ErrMsg)
	}
	return resp.UserList, nil
}

func (c *Client) doPost(url, data string) ([]byte, error) {
	header := make(map[string]string)
	header["Content-Type"] = "application/json;charset=utf-8"
	resp, err := httpclient.Post(url, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		return nil, fmt.Errorf("post request fail. url:%s, err: %v", url, err)
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("post request http code not 200. url: %s, http_code: %d", url, resp.StatusCode)
	}

	return resp.Body, nil
}

func (c *Client) doGet(url string) ([]byte, error) {
	opts := map[string]string{
		httpclient.OptTlsSipVerify: "1", // https请求，跳过ssl检查
	}
	resp, err := httpclient.Get(url, nil, ReqConnTimeoutMs, ReqRWTimeoutMs, opts)
	if err != nil {
		return nil, fmt.Errorf("get request fail. url:%s, err: %v", url, err)
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("get request http code not 200. url: %s, http_code: %d", url, resp.StatusCode)
	}

	return resp.Body, nil
}
