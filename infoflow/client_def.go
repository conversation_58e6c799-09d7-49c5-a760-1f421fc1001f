package infoflow

type InfoFlowClientConfig struct {
	SendEndpoint  string
	TokenEndpoint string
	CorpId        string
	CorpSecret    string
	Timeout       int
}

const (
	SendApi          = "/api/message/send"
	TokenApi         = "/api/gettoken"
	IsFollowApi      = "/api/agent/isFollow"
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000

	MsgTypeText = "text"
)

type GetAccessTokenResponse struct {
	ErrCode     int    `json:"errcode"`
	ErrMsg      string `json:"errmsg"`
	AccessToken string `json:"access_token"`
}

type Text struct {
	Content string `json:"content"`
}

type SendTextMsgRequestBody struct {
	ToUser  string `json:"touser"`
	ToParty string `json:"toparty"`
	ToTag   string `json:"totag"`
	MsgType string `json:"msgtype"`
	AgentId int    `json:"agentid"`
	Text    Text   `json:"text"`
}

type SendTextMsgParam struct {
	AccessToken string
	ToUser      []string
	ToParty     []string
	ToTag       []string
	AgentId     int
	Content     string
}

func (p *SendTextMsgParam) Valid() bool {
	if len(p.Content) == 0 || p.AgentId < 0 {
		return false
	}
	return len(p.ToUser) != 0 || len(p.ToParty) != 0 || len(p.ToTag) != 0
}

type SendMsgResponse struct {
	ErrCode      int      `json:"errcode"`
	ErrMsg       string   `json:"errmsg"`
	InvalidUser  []string `json:"invaliduser"`
	InvalidParty []string `json:"invalidparty"`
	InvalidTag   []string `json:"invalidtag"`
}

type IsFollowParam struct {
	AccessToken string
	AgentId     int
	UserIds     []string
}

func (p *IsFollowParam) Valid() bool {
	return p.AgentId >= 0 && len(p.UserIds) > 0
}

type IsFollowRequestBody struct {
	AgentId int      `json:"agentid"`
	UserIds []string `json:"userids"`
}

type UserFollow struct {
	Userid     string `json:"userid"`
	FollowTime string `json:"followTime"`
	IsFollow   bool   `json:"isFollow"`
}

type IsFollowResponse struct {
	ErrCode  int          `json:"errcode"`
	ErrMsg   string       `json:"errmsg"`
	UserList []UserFollow `json:"userlist"`
}
