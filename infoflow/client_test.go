package infoflow

import (
	"fmt"
	"testing"
)

func getClient() *Client {
	cfg := &InfoFlowClientConfig{
		TokenEndpoint: "https://qyin.im.baidu.com",
		SendEndpoint:  "http://apiin.im.baidu.com",
		CorpId:        "hi53Ldm7uvm7zopMha",
		CorpSecret:    "",
	}
	return NewClient(cfg)
}

func TestSendTextMsg(t *testing.T) {
	client := getClient()
	if err := client.SendTextMsg(&SendTextMsgParam{
		ToUser:  []string{""},
		AgentId: 1488,
		Content: "测试",
	}); err != nil {
		t.Error(err)
	}
}

func TestIsFollow(t *testing.T) {
	client := getClient()
	var users []string
	userList, err := client.IsFollow(&IsFollowParam{
		AgentId: 1488,
		UserIds: users,
	})
	if err != nil {
		t.Error(err)
	}

	fmt.Println(userList)
}
