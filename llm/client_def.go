package llm

import (
	"fmt"
	"net/http"
	"time"
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

// ------------------ 文心 --------------------
const (
	WenXin      = 0
	WenXinTurbo = 1

	wenxinApiAccessToken     = "/oauth/2.0/token"
	wenxinApiErnieChat       = "/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions"
	wenxinApiErnieTurbotChat = "/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant"
	wenxinApiEmbedding       = "/rpc/2.0/ai_custom/v1/wenxinworkshop/embeddings/embedding-v1"

	WenXinRoleUser      = "user"      // 用户（提问方）
	WenXinRoleAssistent = "assistant" // 助手（回答方）

	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000

	ChatReqTimeoutMs = 60000 // 聊天接口超时间设置较长

	tokenExpire = time.Hour * 24

	MaxErnieTokenLimit      = 2000
	MaxErnieTurboTokenLimit = 11200

	bceHost = "aip.baidubce.com"
)

type LLMConfig struct {
	ID       int
	Api      string
	MaxToken int
}

var LLMChatApiMap = map[int]LLMConfig{
	WenXin: {
		WenXin,
		wenxinApiErnieChat,
		MaxErnieTokenLimit,
	},
	WenXinTurbo: {
		WenXinTurbo,
		wenxinApiErnieTurbotChat,
		MaxErnieTurboTokenLimit,
	},
}

type AccessToken struct {
	AccessToken string `json:"access_token"`
	ExpireIn    int64  `json:"expires_in"`
}

type ChatParam struct {
	User        string               `json:"user"`        // 非必填，表示最终用户的唯一标识符，可以监视和检测滥用行为，防止接口恶意调用。
	Stream      bool                 `json:"stream"`      // 是否以流式接口的形式返回数据，默认false
	Messages    []*WenXinChatMessage `json:"messages"`    // 聊天上下文信息
	Temperature float64              `json:"temperature"` // 聊天温度，默认为0.95
	TopP        float64              `json:"top_p"`
}

type WenXinChatMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

func (p *ChatParam) Valid() error {
	// 对话列表必须是单数
	if len(p.Messages)%2 != 1 {
		return fmt.Errorf("length of message should be odd")
	}

	roleSeq := []string{}
	for i, m := range p.Messages {
		if m.Role != WenXinRoleUser && m.Role != WenXinRoleAssistent {
			return fmt.Errorf("invalid message role: %v", m.Role)
		}

		// 对话列表的角色必须是 user、assistant、user... 这样的序列
		roleSeq = append(roleSeq, m.Role)
		if (i%2 == 0 && m.Role == WenXinRoleAssistent) || (i%2 == 1 && m.Role == WenXinRoleUser) {
			return fmt.Errorf("invalid role sequence: %+v", roleSeq)
		}
	}

	return nil
}

type ChatResp struct {
	Id               string `json:"id"`                 // 本轮对话的id
	Object           string `json:"object"`             // 回包类型 chat.completion: 多轮对话返回
	Created          int64  `json:"created"`            // 时间戳
	SentenceId       int64  `json:"sentence_id"`        // 表示当前子句的序号。只有在流式接口模式下会返回该字段
	IsEnd            bool   `json:"is_end"`             // 表示当前子句是否是最后一句。只有在流式接口模式下会返回该字段
	Result           string `json:"result"`             // 对话返回结果
	NeedClearHistory bool   `json:"need_clear_history"` // 表示用户输入是否存在安全，是否关闭当前会话，清理历史回话信息
	BanRound         int64  `json:"ban_round"`          // 当need_clear_history为true时，此字段会告知第几轮对话有敏感信息，如果是当前问题，ban_round =-1
	Usage            struct {
		PromptTokens     int64 `json:"prompt_tokens"`     // 问题tokens数
		CompletionTokens int64 `json:"completion_tokens"` // 回答tokens数
		TotalTokens      int64 `json:"total_tokens"`      // tokens总数
	} `json:"usage"` // token统计信息，token数 = 汉字数+单词数*1.3 （仅为估算逻辑）
	ErrorCode int    `json:"error_code"`
	ErrorMsg  string `json:"error_msg"`
}

type WenXinEmbeddingParam struct {
	Input []string `json:"input"`
}

func (p *WenXinEmbeddingParam) Valid() error {
	if p.Input == nil || len(p.Input) == 0 {
		return fmt.Errorf("input param is empty")
	}
	return nil
}

type WenXinEmbeddingResp struct {
	Id      string `json:"id"`
	Object  string `json:"object"`
	Created int    `json:"created"`
	Data    []struct {
		Object    string  `json:"object"`
		Embedding []int64 `json:"embedding"`
		Index     int     `json:"index"`
	} `json:"data"`
	Usage struct {
		PromptTokens int `json:"prompt_tokens"`
		TotalTokens  int `json:"total_tokens"`
	} `json:"usage"`
	ErrCode int    `json:"error_code"`
	ErrMsg  string `json:"error_msg"`
}
