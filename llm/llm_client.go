package llm

import (
	"encoding/json"
	"fmt"
	"net/url"
	"sync"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type BotClient struct {
	ClientId     string
	ClientSecret string

	token       string
	tokenExpire time.Time
	tokenLock   sync.Mutex
}

func NewWenXinBotClient(ak, sk string) *BotClient {
	return &BotClient{
		ClientId:     ak,
		ClientSecret: sk,
	}
}

func (t *BotClient) accessToken() (string, error) {
	t.tokenLock.Lock()
	defer t.tokenLock.Unlock()

	// 已存在且有效，直接返回
	if len(t.token) > 0 && !time.Now().After(t.tokenExpire) {
		return t.token, nil
	}

	// 否则重新生成
	param := map[string]string{
		"grant_type":    "client_credentials",
		"client_id":     t.ClientId,
		"client_secret": t.ClientSecret,
	}
	reqRes, err := t.doPost(wenxinApiAccessToken, param, "")
	if err != nil {
		return "", err
	}

	var result AccessToken
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return "", fmt.Errorf("json unmarshal did response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}

	t.token = result.AccessToken
	t.tokenExpire = time.Now().Add(tokenExpire)

	return result.AccessToken, nil
}

func (t *BotClient) Chat(p *ChatParam, llmID int) (*ChatResp, error) {
	if err := p.Valid(); err != nil {
		return nil, fmt.Errorf("invalid param: %v", err)
	}

	if p.Stream {
		return nil, fmt.Errorf("stream is not support for this method")
	}

	var llmConfig LLMConfig
	var ok bool
	if llmConfig, ok = LLMChatApiMap[llmID]; !ok {
		return nil, fmt.Errorf("unknown llm config: %v", llmID)
	}

	// 最后一个message的content长度（即此轮对话的问题）不能超过MaxToken个字符
	lastQuestionLen := len(p.Messages[len(p.Messages)-1].Content)
	if lastQuestionLen > llmConfig.MaxToken {
		return nil, fmt.Errorf("last message exceed max length: %d", lastQuestionLen)
	}

	token, err := t.accessToken()
	if err != nil {
		return nil, fmt.Errorf("failed to get ernie bot accessToken: %v", err)
	}
	query := map[string]string{
		"access_token": token,
	}

	body, _ := json.Marshal(p)

	reqRes, err := t.doPost(llmConfig.Api, query, string(body), ChatReqTimeoutMs)
	if err != nil {
		return nil, err
	}

	var result ChatResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, fmt.Errorf("json unmarshal llm chat response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, nil
}

func (t *BotClient) StreamChat(p *ChatParam, llmID int) (chan *Event, error) {
	if err := p.Valid(); err != nil {
		return nil, fmt.Errorf("invalid param: %v", err)
	}

	if !p.Stream {
		return nil, fmt.Errorf("only stream is support for this method")
	}

	var llmConfig LLMConfig
	var ok bool
	if llmConfig, ok = LLMChatApiMap[llmID]; !ok {
		return nil, fmt.Errorf("unknown llm config: %v", llmID)
	}

	// 最后一个message的content长度（即此轮对话的问题）不能超过MaxToken个字符
	lastQuestionLen := len(p.Messages[len(p.Messages)-1].Content)
	if lastQuestionLen > llmConfig.MaxToken {
		return nil, fmt.Errorf("last message exceed max length: %d", lastQuestionLen)
	}

	token, err := t.accessToken()
	if err != nil {
		return nil, fmt.Errorf("failed to get ernie bot accessToken: %v", err)
	}
	query := map[string]string{
		"access_token": token,
	}

	reqUrl := fmt.Sprintf("https://%s%s", bceHost, llmConfig.Api)
	url, err := url.Parse(reqUrl)
	if err != nil {
		return nil, fmt.Errorf("invalid url: %s", reqUrl)
	}
	newQuery := url.Query()
	for k, v := range query {
		newQuery.Set(k, v)
	}
	url.RawQuery = newQuery.Encode()

	events := make(chan *Event)
	body, _ := json.Marshal(p)
	client := NewClient(url.String())
	err = client.SubscribeChan("messages", string(body), events)
	if err != nil {
		return nil, err
	}

	return events, nil
}

func (t *BotClient) WenXinEmbedding(p *WenXinEmbeddingParam) (*WenXinEmbeddingResp, error) {
	if err := p.Valid(); err != nil {
		return nil, fmt.Errorf("invalid param: %v", err)
	}

	token, err := t.accessToken()
	if err != nil {
		return nil, fmt.Errorf("failed to get ernie bot accessToken: %v", err)
	}
	query := map[string]string{
		"access_token": token,
	}

	body, _ := json.Marshal(p)
	reqRes, err := t.doPost(wenxinApiEmbedding, query, string(body), ChatReqTimeoutMs)
	if err != nil {
		return nil, err
	}

	var result WenXinEmbeddingResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, fmt.Errorf("json unmarshal did response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, nil
}

// doPost post
func (t *BotClient) doPost(api string, param map[string]string, data string, timeout ...time.Duration) (*RequestRes, error) {
	reqUrl := fmt.Sprintf("https://%s%s", bceHost, api)
	url, err := url.Parse(reqUrl)
	if err != nil {
		return nil, fmt.Errorf("invalid url: %s", reqUrl)
	}
	query := url.Query()
	for k, v := range param {
		query.Set(k, v)
	}
	url.RawQuery = query.Encode()

	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
	}
	var reqTimeout time.Duration = ReqRWTimeoutMs
	if len(timeout) > 0 {
		reqTimeout = timeout[0]
	}
	resp, err := httpclient.Post(url.String(), header, ReqConnTimeoutMs, reqTimeout, data, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to request ernie url:%s err:%v", reqUrl, err)
	}

	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}
