package msgcenter

import (
	"encoding/json"
	"errors"
	"fmt"
)

// 队列标识
type QueueInfo struct {
	Topic   string `json:"topic"`
	Channel string `json:"channel"`
}

// 消息内容
type BellMessage struct {
	Id        string    `json:"id"`
	Body      string    `json:"body"`
	Timestamp int64     `json:"timestamp"`
	Attempts  uint16    `json:"attempts"`
	QueueInfo QueueInfo `json:"queue_info"`
}

func NewBellMsgByJson(jsMsg string) (*BellMessage, error) {
	if jsMsg == "" {
		return nil, errors.New("json string is empty")
	}

	var obj BellMessage
	err := json.Unmarshal([]byte(jsMsg), &obj)
	if err != nil {
		return nil, err
	}

	if !obj.IsValid() {
		return nil, errors.New("msg is invalid")
	}

	return &obj, nil
}

func (t *BellMessage) String() string {
	return fmt.Sprintf("[id:%s] [body:%s] [timestamp:%d] [attempts:%d] [topic:%s] [channel:%s]",
		t.Id, t.Body, t.Timestamp, t.Attempts, t.QueueInfo.Topic, t.QueueInfo.Channel)
}

func (t *BellMessage) IsValid() bool {
	if t.Id == "" || t.Body == "" || t.Timestamp == 0 {
		return false
	}

	if t.QueueInfo.Topic == "" || t.QueueInfo.Channel == "" {
		return false
	}

	return true
}
