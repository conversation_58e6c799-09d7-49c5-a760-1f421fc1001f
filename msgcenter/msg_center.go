package msgcenter

import (
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/url"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

const (
	MsgCenterErrnoSucc     = 0
	MsgCenterErrnoParamErr = 1002
	MsgCenterErrnoNoAuth   = 1004
	MsgCenterErrnoNotFind  = 1006
	MsgCenterErrnoNoMsg    = 2000
)

const (
	ReqConnTimeoutMs = 600
	ReqRWTimeoutMs   = 1800
)

const (
	MsgCenterApiQuePut     = "/bell/api/v1/queue/put"
	MsgCenterApiQueGet     = "/bell/api/v1/queue/get"
	MsgCenterApiMsgFin     = "/bell/api/v1/msg/finish"
	MsgCenterApiMsgReque   = "/bell/api/v1/msg/requeue"
	MsgCenterApiMsgTouch   = "/bell/api/v1/msg/touch"
	MsgCenterApiAdminAlive = "/bell/api/v1/admin/checkalive"
)

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

type GetMsgResp struct {
	BaseResp
	Msg string `json:"msg"`
}

type MsgCenter struct {
	tpl  int
	sk   string
	addr *addr.Addr
}

func NewMsgCenter(tpl int, sk string, addr *addr.Addr) *MsgCenter {
	return &MsgCenter{tpl, sk, addr}
}

func (t *MsgCenter) PutMsg(topic, msg string, deferMs int) (*BaseResp, string, error) {
	if topic == "" || msg == "" {
		return nil, "", errors.New("param error")
	}
	if err := t.isInit(); err != nil {
		return nil, "", err
	}
	if deferMs < 1 {
		deferMs = 0
	}

	timestamp := time.Now().Unix()
	v := url.Values{}
	v.Set("tpl", fmt.Sprintf("%d", t.tpl))
	v.Set("timestamp", fmt.Sprintf("%d", timestamp))
	v.Set("sign", t.sign(timestamp))
	v.Set("topic", topic)
	v.Set("defer", fmt.Sprintf("%d", deferMs))
	v.Set("body", msg)
	body := v.Encode()

	resp, reqUrl, err := t.doRequest(MsgCenterApiQuePut, body)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("request msg center fail.err:%v", err)
	}

	var result BaseResp
	err = json.Unmarshal([]byte(resp), &result)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("json unmarshal msg center response fail.resp:%s err:%v", resp, err)
	}

	if result.Errno != MsgCenterErrnoSucc {
		return &result, reqUrl, fmt.Errorf("msg center proc fail.resp:%s", resp)
	}

	return &result, reqUrl, nil
}

func (t *MsgCenter) GetMsg(queue *QueueInfo) (*GetMsgResp, string, error) {
	if queue == nil {
		return nil, "", errors.New("param error")
	}
	if err := t.isInit(); err != nil {
		return nil, "", err
	}

	timestamp := time.Now().Unix()
	v := url.Values{}
	v.Set("tpl", fmt.Sprintf("%d", t.tpl))
	v.Set("timestamp", fmt.Sprintf("%d", timestamp))
	v.Set("sign", t.sign(timestamp))
	v.Set("topic", queue.Topic)
	v.Set("channel", queue.Channel)
	body := v.Encode()

	resp, reqUrl, err := t.doRequest(MsgCenterApiQueGet, body)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("request msg center fail.err:%v", err)
	}

	var result GetMsgResp
	err = json.Unmarshal([]byte(resp), &result)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("json unmarshal msg center response fail.resp:%s err:%v", resp, err)
	}

	if result.Errno != MsgCenterErrnoSucc {
		return &result, reqUrl, fmt.Errorf("msg center proc fail.resp:%s", resp)
	}

	return &result, reqUrl, nil
}

func (t *MsgCenter) FinMsg(msgId string, msgTime int64) (*BaseResp, string, error) {
	if msgId == "" || msgTime < 1 {
		return nil, "", errors.New("param error")
	}
	if err := t.isInit(); err != nil {
		return nil, "", err
	}

	timestamp := time.Now().Unix()
	v := url.Values{}
	v.Set("tpl", fmt.Sprintf("%d", t.tpl))
	v.Set("timestamp", fmt.Sprintf("%d", timestamp))
	v.Set("sign", t.sign(timestamp))
	v.Set("msg_id", msgId)
	v.Set("msg_time", fmt.Sprintf("%d", msgTime))
	body := v.Encode()

	resp, reqUrl, err := t.doRequest(MsgCenterApiMsgFin, body)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("request msg center fail.err:%v", err)
	}

	var result BaseResp
	err = json.Unmarshal([]byte(resp), &result)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("json unmarshal msg center response fail.resp:%s err:%v", resp, err)
	}

	if result.Errno != MsgCenterErrnoSucc {
		return &result, reqUrl, fmt.Errorf("msg center proc fail.resp:%s", resp)
	}

	return &result, reqUrl, nil
}

func (t *MsgCenter) ReQueMsg(msg string) (*BaseResp, string, error) {
	if msg == "" {
		return nil, "", errors.New("param error")
	}
	if err := t.isInit(); err != nil {
		return nil, "", err
	}

	timestamp := time.Now().Unix()
	v := url.Values{}
	v.Set("tpl", fmt.Sprintf("%d", t.tpl))
	v.Set("timestamp", fmt.Sprintf("%d", timestamp))
	v.Set("sign", t.sign(timestamp))
	v.Set("msg", msg)
	body := v.Encode()

	resp, reqUrl, err := t.doRequest(MsgCenterApiMsgReque, body)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("request msg center fail.err:%v", err)
	}

	var result BaseResp
	err = json.Unmarshal([]byte(resp), &result)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("json unmarshal msg center response fail.resp:%s err:%v", resp, err)
	}

	if result.Errno != MsgCenterErrnoSucc {
		return &result, reqUrl, fmt.Errorf("msg center proc fail.resp:%s", resp)
	}

	return &result, reqUrl, nil
}

func (t *MsgCenter) TouchMsg(msg string) (*BaseResp, string, error) {
	if msg == "" {
		return nil, "", errors.New("param error")
	}
	if err := t.isInit(); err != nil {
		return nil, "", err
	}

	timestamp := time.Now().Unix()
	v := url.Values{}
	v.Set("tpl", fmt.Sprintf("%d", t.tpl))
	v.Set("timestamp", fmt.Sprintf("%d", timestamp))
	v.Set("sign", t.sign(timestamp))
	v.Set("msg", msg)
	body := v.Encode()

	resp, reqUrl, err := t.doRequest(MsgCenterApiMsgTouch, body)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("request msg center fail.err:%v", err)
	}

	var result BaseResp
	err = json.Unmarshal([]byte(resp), &result)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("json unmarshal msg center response fail.resp:%s err:%v", resp, err)
	}

	if result.Errno != MsgCenterErrnoSucc {
		return &result, reqUrl, fmt.Errorf("msg center proc fail.resp:%s", resp)
	}

	return &result, reqUrl, nil
}

func (t *MsgCenter) Checkalive() (*BaseResp, string, error) {
	if err := t.isInit(); err != nil {
		return nil, "", err
	}

	timestamp := time.Now().Unix()
	v := url.Values{}
	v.Set("tpl", fmt.Sprintf("%d", t.tpl))
	v.Set("timestamp", fmt.Sprintf("%d", timestamp))
	v.Set("sign", t.sign(timestamp))
	body := v.Encode()

	resp, reqUrl, err := t.doRequest(MsgCenterApiAdminAlive, body)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("request msg center fail.err:%v", err)
	}

	var result BaseResp
	err = json.Unmarshal([]byte(resp), &result)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("json unmarshal msg center response fail.resp:%s err:%v", resp, err)
	}

	if result.Errno != MsgCenterErrnoSucc {
		return &result, reqUrl, fmt.Errorf("msg center proc fail.resp:%s", resp)
	}

	return &result, reqUrl, nil
}

// post. data is json string
func (t *MsgCenter) doRequest(api string, data string) (string, string, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return "", "", fmt.Errorf("get msg center addr fail.err:%v", err)
	}

	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return "", reqUrl, fmt.Errorf("request msg center fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)

	if resp.StatusCode != 200 {
		return "", reqUrl, fmt.Errorf("request msg center http code not 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}

	return string(resp.Body), reqUrl, nil
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *MsgCenter) isInit() error {
	if t.addr == nil || t.tpl < 1 || t.sk == "" {
		return errors.New("param error")
	}

	return nil
}

func (t *MsgCenter) sign(timestamp int64) string {
	h := md5.New()
	io.WriteString(h, fmt.Sprintf("%d+%d+%s", t.tpl, timestamp, t.sk))
	return fmt.Sprintf("%x", h.Sum(nil))
}
