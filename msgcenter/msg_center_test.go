package msgcenter

import (
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
)

var testHost []string = []string{"127.0.0.1:8610"}
var topic string = "bell_dev_test_queue"
var channel string = "bell_test_channel"
var tpl int = 1
var sk string = "9e01d8ef1d80e8c7f3582ec051fea9b1"

func TestPutMsg(t *testing.T) {
	a, _ := addr.NewAddrByHost(testHost)
	body := fmt.Sprintf("hello bell.timestamp:%d", time.Now().Unix())
	resp, u, err := NewMsgCenter(tpl, sk, a).PutMsg(topic, body, 0)
	if err != nil {
		fmt.Println(err, u)
		return
	}

	fmt.Println(resp, u)
}

func TestFinMsg(t *testing.T) {
	a, _ := addr.NewAddrByHost(testHost)
	resp, u, err := NewMsgCenter(tpl, sk, a).GetMsg(&QueueInfo{topic, channel})
	if err != nil {
		fmt.Println(err, u)
		return
	}

	fmt.Println(resp, u)

	var msg BellMessage
	err = json.Unmarshal([]byte(resp.Msg), &msg)
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(msg)

	respf, u, err := NewMsgCenter(tpl, sk, a).FinMsg(msg.Id, msg.Timestamp)
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(respf, u)
}

func TestReQueMsg(t *testing.T) {
	a, _ := addr.NewAddrByHost(testHost)
	resp, u, err := NewMsgCenter(tpl, sk, a).GetMsg(&QueueInfo{topic, channel})
	if err != nil {
		fmt.Println(err, u)
		return
	}

	fmt.Println(resp, u)

	var msg BellMessage
	err = json.Unmarshal([]byte(resp.Msg), &msg)
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(msg)

	respf, u, err := NewMsgCenter(tpl, sk, a).ReQueMsg(resp.Msg)
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(respf, u)
}

func TestTouchMsg(t *testing.T) {
	a, _ := addr.NewAddrByHost(testHost)
	resp, u, err := NewMsgCenter(tpl, sk, a).GetMsg(&QueueInfo{topic, channel})
	if err != nil {
		fmt.Println(err, u)
		return
	}

	fmt.Println(resp, u)

	var msg BellMessage
	err = json.Unmarshal([]byte(resp.Msg), &msg)
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(msg)

	respf, u, err := NewMsgCenter(tpl, sk, a).TouchMsg(resp.Msg)
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(respf, u)
}
