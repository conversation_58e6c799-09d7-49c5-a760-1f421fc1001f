package mysql

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"

	_ "github.com/go-sql-driver/mysql"
)

const (
	DEFAULT_MAXOPENCONNS = 1
	DEFAULT_MAXIDLECONNS = 0
	// 单位s
	DEFAULT_CONNMAXLIFETIMES = 20
)

type Mysql struct {
	db         *sql.DB
	lock       *sync.RWMutex
	connOption *MysqlConnOption
	// user:key@tcp(%s)/dbname?charset=utf8&interpolateParams=true&timeout=20s
	dsnFmt string
	// 统一封装host获取方式
	addr *addr.Addr
	// 当前使用的host
	host *addr.HostInfo
	// 当前使用的dsn
	dsn string
}

type MysqlConnOption struct {
	// 设置最大允许打开的连接数
	MaxOpenConns int `json:"max_open_conns"`
	// 设置允许空闲保持的最大连接数，目前dba这边维护的mysql都是通过proxy代码的，都是短链接方式访问
	MaxIdleConns int `json:"max_idle_conns"`
	// 链接超时时间，单位s
	ConnMaxLifeTime time.Duration `json:"conn_max_lifetime"`
}

func NewMysql(connOption *MysqlConnOption, dsnFmt string, paddr *addr.Addr) (*Mysql, error) {
	err := checkNewMysqlParam(connOption, dsnFmt)
	if err != nil {
		return nil, err
	}
	if paddr == nil {
		return nil, errors.New("addr is nil")
	}

	obj := new(Mysql)
	obj.connOption = connOption
	obj.dsnFmt = dsnFmt
	obj.lock = new(sync.RWMutex)
	obj.addr = paddr

	obj.host, err = obj.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail by addr.GetAddr.err:%v", err)
	}

	dHost := fmt.Sprintf("%s:%s", obj.host.Host, obj.host.Port)
	obj.dsn = fmt.Sprintf(obj.dsnFmt, dHost)

	obj.db, err = newMysql(obj.dsn, obj.connOption)
	if err != nil {
		obj.addr.SetFail(obj.host)
		return nil, err
	}

	obj.addr.SetOk(obj.host)
	return obj, nil

}

func NewMysqlByHost(connOption *MysqlConnOption, dsnFmt string, hosts []string) (*Mysql, error) {
	err := checkNewMysqlParam(connOption, dsnFmt)
	if err != nil {
		return nil, err
	}
	// create addr
	a, err := addr.NewAddrByHost(hosts)
	if err != nil {
		return nil, fmt.Errorf("new addr by host fail.err:%v", err)
	}

	return NewMysql(connOption, dsnFmt, a)
}

func NewMysqlByBns(connOption *MysqlConnOption, dsnFmt string, bns string) (*Mysql, error) {
	err := checkNewMysqlParam(connOption, dsnFmt)
	if err != nil {
		return nil, err
	}
	// create addr
	a, err := addr.NewAddrByBns(bns)
	if err != nil {
		return nil, fmt.Errorf("new addr by bns fail.err:%v", err)
	}

	return NewMysql(connOption, dsnFmt, a)
}

// TODO:这里这种方式控制并发安全其实有问题，可以有并发拿到handle还没用，这个handle被其他并发关闭
// 测试验证了下这种情况下并不会core掉，只是会报错，可以靠RefreshConn重试解决
// 绝对安全就需要在执行sql时加读锁，执行完sql释放读锁，但是这样太繁琐，而且很容易死锁
// 暂时没有想到好办法，先靠RefreshConn重试解决吧，sql包本身是并发安全的，是由于这种使用方式引入的
// 不过：sql.Open函数后面给的建议是:The returned DB is safe for concurrent use by multiple goroutines
// and maintains its own pool of idle connections. Thus, the Open function should be called just once.
// It is rarely necessary to close a DB.
func (t *Mysql) GetDB() *sql.DB {
	t.lock.RLock()
	defer t.lock.RUnlock()

	return t.db
}

func (t *Mysql) GetStats() sql.DBStats {
	t.lock.RLock()
	defer t.lock.RUnlock()

	return t.db.Stats()
}

// 重试时需要刷新链接
func (t *Mysql) RefreshConn() error {
	t.lock.Lock()
	defer t.lock.Unlock()

	err := t.db.Ping()
	if err != nil {
		t.db.Close()

		host, err := t.addr.GetAddr()
		if err != nil {
			return fmt.Errorf("get addr fail by addr.GetAddr.err:%v", err)
		}
		dHost := fmt.Sprintf("%s:%s", host.Host, host.Port)
		dsn := fmt.Sprintf(t.dsnFmt, dHost)

		db, err := newMysql(dsn, t.connOption)
		if err != nil {
			t.addr.SetFail(host)
			return err
		}
		t.addr.SetOk(host)

		t.db = db
		t.dsn = dsn
		t.host = host
	}

	return nil
}

func (t *Mysql) Close() {
	t.lock.Lock()
	defer t.lock.Unlock()

	if t.db != nil {
		t.db.Close()
	}
}

func newMysql(dsn string, options *MysqlConnOption) (*sql.DB, error) {
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("mysql open fail.dsn:%s err:%v", dsn, err)
	}

	db.SetMaxOpenConns(options.MaxOpenConns)
	db.SetMaxIdleConns(options.MaxIdleConns)
	db.SetConnMaxLifetime(options.ConnMaxLifeTime)

	err = db.Ping()
	if err != nil {
		db.Close()
		return nil, fmt.Errorf("mysql ping fail.dsn:%s err:%v", dsn, err)
	}

	return db, nil
}

func checkNewMysqlParam(connOption *MysqlConnOption, dsnFmt string) error {
	if connOption == nil || dsnFmt == "" {
		return errors.New("param set error")
	}

	if connOption.MaxOpenConns < 0 {
		return errors.New("param set error")
	}

	if strings.Count(dsnFmt, "%s") != 1 {
		return errors.New("param set error")
	}

	return nil
}
