package mysql

import (
	"fmt"
	"testing"
	"time"
)

func TestNewMysqlByHost(t *testing.T) {
	option := &MysqlConnOption{3, 0, 10 * time.Second}
	dsnFmt := "xxx:xxx@tcp(%s)/netdisk_xxx?charset=utf8&interpolateParams=true&timeout=20s"
	hosts := make([]string, 1)
	hosts[0] = "10.xx.172.xx:xx00"
	db, err := NewMysqlByHost(option, dsnFmt, hosts)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer db.Close()

	//db.Close()

	err = db.RefreshConn()
	if err != nil {
		fmt.Println(err)
		return
	}

	var lastSMtime, lastFsid, ctime, mtime int64
	var status int
	sql := "select last_smtime,last_fsid,status,ctime,mtime from user_diff_info limit 1"
	err = db.GetDB().QueryRow(sql).Scan(&lastSMtime, &lastFsid, &status, &ctime, &mtime)
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(lastSMtime, lastFsid, ctime, mtime, status)
	if err != nil {
		fmt.Println(err)
		return
	}
}

func TestNewMysqlByBns(t *testing.T) {
	option := &MysqlConnOption{3, 0, 10 * time.Second}
	dsnFmt := "xx:xx@tcp(%s)/netdisk_xxx?charset=utf8&interpolateParams=true&timeout=20s"
	db, err := NewMysqlByBns(option, dsnFmt, "smartbns.xxxxx.xdb.all.serv")
	if err != nil {
		fmt.Println(err)
		return
	}
	defer db.Close()
}
