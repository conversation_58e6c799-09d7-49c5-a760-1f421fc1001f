package notice

import (
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type Client struct {
	addr string
}

func NewClient(addr string) *Client {
	return &Client{
		addr: addr,
	}
}

func (c *Client) Notice(req *NoticeRequest) error {
	if req == nil || len(req.NoticeType) == 0 || len(req.CurrentUser) == 0 {
		return errors.New("param error")
	}
	body, err := json.Marshal(req)
	if err != nil {
		return err
	}

	resp, err := c.doRequest(NoticeApi, string(body))
	if err != nil {
		return fmt.Errorf("request notice fail.err:%v", err)
	}
	var result NoticeResp
	err = json.Unmarshal([]byte(resp), &result)
	if err != nil {
		return fmt.Errorf("json unmarshal notice response fail.resp:%s err:%v", resp, err)
	}

	if result.Code != 0 {
		return fmt.Errorf("notice fail.resp:%s", resp)
	}
	return nil
}

// post. data is json string
func (c *Client) doRequest(api string, data string) (string, error) {
	reqUrl := fmt.Sprintf("http://%s/%s", c.addr, api)
	header := make(map[string]string)
	header["Content-Type"] = "application/json;charset=utf-8"
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		return "", fmt.Errorf("request notice fail. url:%s, err: %v", reqUrl, err)
	}

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("request notice http code not 200. url: %s, http_code: %d", reqUrl, resp.StatusCode)
	}

	return string(resp.Body), nil
}
