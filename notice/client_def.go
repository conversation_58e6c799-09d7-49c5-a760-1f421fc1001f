package notice

const (
	NoticeApi        = "/api/openapi/send"
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000
)

type NoticeMsg struct {
	Hi      string `json:"hi"`      // 发送Hi必填，hi通告内容
	Email   string `json:"email"`   // 发送邮件必填，邮件通告内容
	Subject string `json:"subject"` // 发送邮件必填，邮件通告主题
}

type NoticeRequest struct {
	NoticeType  string    `json:"noticeType"`  // 发送来源
	NoticeEmail string    `json:"noticeEmail"` // 通告邮箱，不发送邮件传空值或不传即可（多个用逗号隔开）
	NoticeHi    string    `json:"noticeHi"`    // 通告Hi群，不发送hi传空值或不传即可（HI群可以多个）
	NoticeMsg   NoticeMsg `json:"noticeMsg"`   // 发送内容
	CurrentUser string    `json:"currentUser"` // 操作人
}

type NoticeResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}
