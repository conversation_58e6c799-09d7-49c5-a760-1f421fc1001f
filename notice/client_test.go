package notice

import (
	"fmt"
	"testing"
)

var addr string = "notice.nsc.baidu.com"

func getNoticeClient() *Client {
	return NewClient(addr)
}

func TestNotice(t *testing.T) {
	cli := getNoticeClient()
	req := NoticeRequest{
		NoticeType:  "事件管理",
		NoticeEmail: "<EMAIL>",
		NoticeHi:    "",
		NoticeMsg: NoticeMsg{
			Email:   "test email",
			Subject: "test subject",
			Hi:      "测试Hi通告",
		},
		CurrentUser: "fengzhaoyang",
	}
	fmt.Println(req.NoticeMsg.Email)
	if err := cli.Notice(&req); err != nil {
		t.Error(err)
	}
}
