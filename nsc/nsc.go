package nsc

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"net/url"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

const (
	NscStatusSucc = 0
)

const (
	ReqConnTimeoutMs = 200
	ReqRWTimeoutMs   = 600
)

const (
	NscHost = "http://api.nsc.baidu.com"
	//NscHost = "http://bjyz-sys-nsc02.bjyz.baidu.com:8230"
)

const (
	NscApiIsIntranet = "/dunet/ip/isintranet"
)

type IsIntranetResp struct {
	Status  int            `json:"status"`
	Message map[string]int `json:"message"`
}

type NscClient struct {
	appName string
	token   string
}

func NewNscClient(AppName, Token string) *NscClient {
	return &NscClient{AppName, Token}
}

func (t *NscClient) IsIntranet(host string) (bool, error) {
	v := url.Values{}
	v.Set("ip", fmt.Sprintf("%s", host))
	req := fmt.Sprintf("%s%s?%s", NscHost, NscApiIsIntranet, v.Encode())
	resp, err := t.doRequest(req)
	if err != nil {
		return false, fmt.Errorf("request nsc fail.err:%v", err)
	}

	var result IsIntranetResp
	err = json.Unmarshal([]byte(resp), &result)
	if err != nil {
		return false, fmt.Errorf("json unmarshal nsc response fail.resp:%s err:%v", resp, err)
	}

	if result.Status != NscStatusSucc {
		return false, fmt.Errorf("nsc check fail.resp:%s", resp)
	}

	if v, ok := result.Message["isIntranet"]; ok && v == 1 {
		return true, nil
	}

	return false, nil
}

func (t *NscClient) doRequest(reqUrl string) (string, error) {
	header := make(map[string]string)
	header["Content-Type"] = "application/json"
	header["app-name"] = t.appName
	header["time-stamp"] = fmt.Sprintf("%d", time.Now().UnixNano()/1e6)
	header["Authorization"] = t.sign(header["time-stamp"])
	resp, err := httpclient.Get(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, nil)
	if err != nil {
		return "", fmt.Errorf("request nsc fail.url:%s err:%v", reqUrl, err)
	}

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("request nsc http code not 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}

	return string(resp.Body), nil
}

func (t *NscClient) sign(timestamp string) string {
	h := md5.New()
	io.WriteString(h, fmt.Sprintf("%s%s%s", t.appName, timestamp, t.token))
	return fmt.Sprintf("%x", h.Sum(nil))
}
