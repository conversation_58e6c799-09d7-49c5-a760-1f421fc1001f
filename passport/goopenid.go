package passport

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"hash/crc32"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

const (
	unionIdDecode = "/id?type=unionid&action=decode"
)

type GoOpenIdClient struct {
	goOpenIdCfg *GoOpenIdCfg
	addr        *addr.Addr
}

type GoOpenIdCfg struct {
	ApId   string
	OtpKey string
}

func NewGoOpendIdClient(cfg *GoOpenIdCfg, addr *addr.Addr) *GoOpenIdClient {
	return &GoOpenIdClient{
		goOpenIdCfg: cfg,
		addr:        addr,
	}
}

func (t *GoOpenIdClient) GetUid(unionId string) (int64, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return 0, fmt.Errorf("get addr fail.err:%v", err)
	}

	token, err := GetToken(t.goOpenIdCfg.OtpKey, 0)
	if err != nil {
		return 0, fmt.Errorf("get token fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s&unionid=%s&apid=%s&token=%s",
		hostInfo.Host, hostInfo.Port, unionIdDecode, unionId, t.goOpenIdCfg.ApId, token)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	resp, err := httpclient.Get(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, nil)
	if err != nil {
		return 0, fmt.Errorf("request product fail.url:%s err:%v", reqUrl, err)
	}
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return 0, fmt.Errorf("request product http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}

	res := struct {
		Data *struct {
			Uid    int64 `json:"userid"`
			DevUid int64 `json:"devuid"`
		} `json:"decoding"`
		RequestId string `json:"request_id"`
		Errno     int    `json:"code"`
		Errmsg    string `json:"msg"`
	}{}
	err = json.Unmarshal([]byte(result.Body), &res)
	if err != nil {
		return 0, fmt.Errorf("json unmarshal passport response fail.url:%s http_code:%d "+
			"resp:%s err:%v", result.ReqUrl, result.HttpCode, result.Body, err)
	}
	if res.Errno != 0 {
		return 0, fmt.Errorf("decode unionid to uid failed.resp:%s", resp.Body)

	}

	if res.Data == nil {
		return 0, fmt.Errorf("res data is empty.resp:%s", resp.Body)
	}

	return res.Data.Uid, nil
}

func GetToken(otpKey string, tokenTime int64) (string, error) {
	var tokenVersion uint16 = 1 //目前固定为1
	var keyVersion uint16 = 1   //目前固定为1
	var otp uint32
	if tokenTime == 0 {
		tokenTime = time.Now().Unix()
	}
	otp = Crc32(fmt.Sprintf("%d&%s", tokenTime, otpKey))
	rawToken := fmt.Sprintf("%d&%d&%d&%d", tokenVersion, keyVersion, tokenTime, otp)
	token := base64.RawURLEncoding.EncodeToString([]byte(rawToken))
	return token, nil
}
func Crc32(buf string) uint32 {
	return crc32.ChecksumIEEE([]byte(buf))
}
