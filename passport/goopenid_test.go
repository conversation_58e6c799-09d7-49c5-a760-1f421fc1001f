package passport

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
)

func TestUid(t *testing.T) {
	// 线上集群
	bns := "group.smartbns-from_product=passport-openid%group.flow-openid.passport.all"
	a, _ := addr.NewAddrByBns(bns)

	obj := NewGoOpendIdClient(a)

	res, err := obj.GetUid("xxx")
	if err != nil {
		t.<PERSON><PERSON><PERSON>("err:%v", err)
		return
	}

	fmt.Println("Res:", res)
}
