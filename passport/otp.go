package passport

import (
	"bytes"
	"encoding/binary"
	"encoding/hex"
	"fmt"
	"hash/crc32"
	"time"
)

const (
	// AuthTypeOtp otp鉴权类型
	AuthTypeOtp = "otp"
)

// OtpConfig Otp鉴权配置
type OtpConfig struct {
	KeyVersion   uint16 // keyVersion key版本号，申请数据权限后分配的key_version，一般为1
	TokenVersion uint16 // tokenVersion token版本号，申请数据权限后分配的，token_version，一般为1
	Key          string // otpKey 申请数据权限后分配的otp_key
	ApID         uint16 // apID 申请数据权限后分配的apid
}

// GenOtpToken 创建session和passport服务鉴权auth_token
// token授权方法wiki地址: http://wiki.baidu.com/pages/viewpage.action?pageId=*********
func GenOtpToken(conf *OtpConfig) string {
	timeStamp := time.Now().Unix()
	packer := newItemPacker()
	_ = packer.Version(conf.KeyVersion)
	_ = packer.Version(conf.TokenVersion)
	_ = packer.Time(uint32(timeStamp))
	otp := fmt.Sprintf("%s%d%d", conf.Key, timeStamp, conf.ApID)
	_ = packer.Time(crc32.ChecksumIEEE([]byte(otp)))
	return binToHex(packer.buf.Bytes())
}

func newItemPacker() *itemPacker {
	packer := new(itemPacker)
	packer.buf = bytes.NewBuffer([]byte{})
	return packer
}

func binToHex(src []byte) string {
	dst := make([]byte, hex.EncodedLen(len(src)))
	hex.Encode(dst, src)
	return string(dst)
}

type itemPacker struct {
	buf *bytes.Buffer
}

func (packer itemPacker) Version(ver uint16) error {
	return binary.Write(packer.buf, binary.LittleEndian, ver)
}

func (packer itemPacker) Time(ver uint32) error {
	return binary.Write(packer.buf, binary.LittleEndian, ver)
}

func (packer itemPacker) Bytes(data []byte) error {
	return binary.Write(packer.buf, binary.LittleEndian, data)
}
