package passport

// 接口文档参见 http://dev.passport.baidu.com/docs/agg/viewv2?path=tech.text&doc=server/passgate/man_http.text

import (
	"encoding/json"
	"fmt"
	"net/url"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type GateConfig struct {
	AppUser   string `json:"app_user"`
	AppPasswd string `json:"app_passwd"`
}

type PassGateClient struct {
	gateConf *GateConfig
	otpConf  *OtpConfig
	addr     *addr.Addr
}

type UserInfoAll struct {
	UserName    string `json:"username"`
	Sex         int    `json:"sex"`
	DisplayName string `json:"displayname"`
	SecureMobil string `json:"securemobil"`
	SecureEmail string `json:"secureemail"`
}

type GetUserInfoAllResp struct {
	ErrNo        int64                   `json:"err_no"`
	ErrMsg       string                  `json:"err_msg"`
	ResultParams map[string]*UserInfoAll `json:"result_params"`
}

func NewPassGateClient(cfg *GateConfig, otpCfg *OtpConfig, addr *addr.Addr) *PassGateClient {
	return &PassGateClient{cfg, otpCfg, addr}
}

// GetUserName 查询用户名称
func (t *PassGateClient) GetUserName(uid int64) (string, *RequestRes, error) {
	if !t.isInit() || uid <= 0 {
		return "", nil, fmt.Errorf("not init or param unset")
	}

	v := url.Values{}
	v.Set("service_name", "userinfo_all") // 固定服务名
	v.Set("method", "batget")             // 固定方法名
	v.Set("app_user", t.gateConf.AppUser)
	v.Set("app_passwd", t.gateConf.AppPasswd)
	v.Set("userid", fmt.Sprintf("[%d]", uid))
	v.Set("inter_encoding", "utf8") // 字符编码，固定值
	v.Set("auth_type", "otp")       // otp授权固定值
	v.Set("auth_token", GenOtpToken(t.otpConf))
	v.Set("req_fields", `["username"]`) // 只查询用户名称
	param := v.Encode()

	reqRes, err := t.doRequestRetry("/passgate", param)
	if err != nil {
		return "", nil, fmt.Errorf("request passgate for user name fail.err:%v", err)
	}
	if reqRes.HttpCode != 200 {
		return "", reqRes, fmt.Errorf("request passport http code not equal 200.code:%d url:%s",
			reqRes.HttpCode, reqRes.ReqUrl)
	}

	var resp GetUserInfoAllResp
	err = json.Unmarshal([]byte(reqRes.Body), &resp)

	if err != nil {
		return "", reqRes, fmt.Errorf("unmarshal passgate resp failed or status not ok.url:%s body:%s",
			reqRes.ReqUrl, reqRes.Body)
	}

	if resp.ErrNo != 0 {
		return "", reqRes, fmt.Errorf("passgate return errno %d .url:%s body:%s",
			resp.ErrNo, reqRes.ReqUrl, reqRes.Body)
	}

	uInfo, ok := resp.ResultParams[fmt.Sprintf("%d", uid)]
	if !ok {
		return "", reqRes, fmt.Errorf("passgate no user data found.url:%s body:%s",
			reqRes.ReqUrl, reqRes.Body)
	}

	return uInfo.UserName, reqRes, nil
}

// MultiGetUserName 批量查询用户名称
func (t *PassGateClient) MultiGetUserName(uidList []int64) (map[string]*UserInfoAll, *RequestRes, error) {
	if !t.isInit() || len(uidList) <= 0 {
		return nil, nil, fmt.Errorf("not init or param unset")
	}

	uidListStr, err := json.Marshal(uidList)
	if err != nil {
		return nil, nil, fmt.Errorf("marshal uid list failed, err: %v", err)
	}
	v := url.Values{}
	v.Set("service_name", "userinfo_all") // 固定服务名
	v.Set("method", "batget")             // 固定方法名
	v.Set("app_user", t.gateConf.AppUser)
	v.Set("app_passwd", t.gateConf.AppPasswd)
	v.Set("userid", string(uidListStr))
	v.Set("inter_encoding", "utf8") // 字符编码，固定值
	v.Set("auth_type", "otp")       // otp授权固定值
	v.Set("auth_token", GenOtpToken(t.otpConf))
	v.Set("req_fields", `["username"]`) // 只查询用户名称
	param := v.Encode()

	reqRes, err := t.doRequestRetry("/passgate", param)
	if err != nil {
		return nil, nil, fmt.Errorf("request passgate for user name fail.err:%v", err)
	}
	if reqRes.HttpCode != 200 {
		return nil, reqRes, fmt.Errorf("request passport http code not equal 200.code:%d url:%s",
			reqRes.HttpCode, reqRes.ReqUrl)
	}

	var resp GetUserInfoAllResp
	err = json.Unmarshal([]byte(reqRes.Body), &resp)

	if err != nil {
		return nil, reqRes, fmt.Errorf("unmarshal passgate resp failed or status not ok.url:%s body:%s",
			reqRes.ReqUrl, reqRes.Body)
	}

	if resp.ErrNo != 0 {
		return nil, reqRes, fmt.Errorf("passgate return errno %d .url:%s body:%s",
			resp.ErrNo, reqRes.ReqUrl, reqRes.Body)
	}

	return resp.ResultParams, reqRes, nil
}

func (t *PassGateClient) doRequestRetry(api, data string) (*RequestRes, error) {
	var resp *RequestRes
	var err error
	for i := 0; i < ReqMaxRetrys; i++ {
		resp, err = t.doRequest(api, data)
		if err == nil {
			break
		}
	}
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// get
func (t *PassGateClient) doRequest(api string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}

	reqUrl := fmt.Sprintf("http://%s:%s%s?%s", hostInfo.Host, hostInfo.Port, api, data)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}

	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	resp, err := httpclient.Get(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request passport fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)

	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	return result, nil
}

func (t *PassGateClient) isInit() bool {
	if t == nil || t.gateConf == nil || t.otpConf == nil || t.addr == nil {
		return false
	}

	return true
}
