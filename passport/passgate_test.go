package passport

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
)

func TestPassGateClient_GetUserName(t *testing.T) {
	bns := "group.smartbns-from_product=ess-passgate%group.flow-npg.passport.all:http"
	a, _ := addr.NewAddrByBns(bns)
	gateConf := &GateConfig{
		AppUser:   "xxx",
		AppPasswd: "xxx",
	}
	otpConf := &OtpConfig{
		KeyVersion:   1,
		TokenVersion: 1,
		Key:          "xxx",
		ApID:         111,
	}
	client := NewPassGateClient(gateConf, otpConf, a)
	name, res, err := client.GetUserName(*********)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(name, res)
}
