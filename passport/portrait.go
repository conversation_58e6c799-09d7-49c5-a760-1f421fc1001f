package passport

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"errors"
	"fmt"
	"hash/crc32"
	"io"
	"strconv"
	"strings"
)

const (
	// 小图尺寸头像
	SmallPortraitUrl = "https://himg.bdimg.com/sys/portrait/item/"
	// 高清尺寸头像
	BigPortraitUrl = "https://himg.bdimg.com/sys/portraith/item/"
)

type PortraitType int

const (
	// 小图尺寸头像
	PortraitTypeSmall = 0
	// 高清尺寸头像
	PortraitTypeBig = 1
)

type PortraitConf struct {
	Tpl        string
	KeyVersion string
	Key        string
	Iv         string
}

func GenPortrait(conf *PortraitConf, uid int64, ptype PortraitType) (string, error) {
	if conf == nil || uid < 1 {
		return "", errors.New("param error")
	}

	sign, err := GetSign(uid, conf.Tpl, conf.KeyVersion, conf.Key, conf.Iv)
	if err != nil {
		return "", err
	}

	url := SmallPortraitUrl
	if ptype == PortraitTypeBig {
		url = BigPortraitUrl
	}

	return url + sign, nil
}

func encryptData(data string, key string, iv string) (string, error) {
	keyBytes := []byte(key)
	ivBytes := []byte(iv)

	result, err := aesEncrypt([]byte(data), keyBytes, ivBytes)
	if err != nil {
		return "", err
	}

	data = base64.RawStdEncoding.EncodeToString(result)

	data = strings.Replace(data, "+", "-", -1)
	data = strings.Replace(data, "/", "_", -1)
	data = strings.Replace(data, "=", "", -1)

	return data, nil
}

func decryptData(data string, key string, iv string) (string, error) {
	keyBytes := []byte(key)
	ivBytes := []byte(iv)

	var result []byte
	var err error

	data = strings.Replace(data, "-", "+", -1)
	data = strings.Replace(data, "_", "/", -1)

	result, err = base64.RawStdEncoding.DecodeString(data)
	if err != nil {
		return "", err
	}

	origData, err := aesDecrypt(result, keyBytes, ivBytes)
	if err != nil {
		return "", err
	}

	return string(origData), nil

}

func aesEncrypt(data, key []byte, iv []byte) ([]byte, error) {
	if key == nil || len(key) != 32 {
		return nil, fmt.Errorf("key length error")
	}

	if iv == nil || len(iv) != 16 {
		return nil, fmt.Errorf("iv length error")
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockSize := block.BlockSize()
	data = pkcs5Padding(data, blockSize)
	blockMode := cipher.NewCBCEncrypter(block, iv[:blockSize])
	crypted := make([]byte, len(data))
	blockMode.CryptBlocks(crypted, data)
	return crypted, nil
}

func aesDecrypt(data, key []byte, iv []byte) ([]byte, error) {
	if key == nil || len(key) != 32 {
		return nil, fmt.Errorf("key length error")
	}
	if iv == nil || len(iv) != 16 {
		return nil, fmt.Errorf("iv length error")
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockSize := block.BlockSize()
	blockMode := cipher.NewCBCDecrypter(block, iv[:blockSize])
	origData := make([]byte, len(data))
	blockMode.CryptBlocks(origData, data)
	origData = pkcs5UnPadding(origData)
	return origData, nil
}

func pkcs5Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

func pkcs5UnPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

func GetSign(userId int64, tpl string, keyVersion string, key string, iv string) (string, error) {
	crc32String := key + iv + "PassIdCRC32"
	ieee := crc32.NewIEEE()
	_, err := io.WriteString(ieee, crc32String)
	if err != nil {
		return "", err
	}
	checkSum := ieee.Sum32()

	data := userId ^ int64(checkSum)

	dataString := fmt.Sprintf("%d", data)
	encryptedData, _ := encryptData(dataString, key, iv)

	crc32String2 := tpl + keyVersion + key + iv + encryptedData + "PassPortraitCheckSum"
	ieee.Reset()
	_, err2 := io.WriteString(ieee, crc32String2)
	if err2 != nil {
		return "", err2
	}

	sign := fmt.Sprintf("%s.%s.%x.%s", tpl, keyVersion, ieee.Sum32(), encryptedData)
	return sign, nil
}

func GetUserId(sign string, tpl string, keyVersion string, key string, iv string) (int64, error) {
	info := strings.Split(sign, ".")
	if len(info) != 4 {
		return 0, fmt.Errorf("sign format error")
	}

	checkSumString := info[2]
	data := info[3]

	clientCheckSum, err := strconv.ParseInt(checkSumString, 16, 64)
	if err != nil {
		return 0, fmt.Errorf("clientCheckSum %s", err.Error())
	}

	ieee := crc32.NewIEEE()
	_, err1 := io.WriteString(ieee, tpl+keyVersion+key+iv+data+"PassPortraitCheckSum")
	if err1 != nil {
		return 0, err1
	}

	serverCheckSum := int64(ieee.Sum32())
	ieee.Reset()

	if clientCheckSum != serverCheckSum {
		return 0, fmt.Errorf("checksum error")
	}

	data2, err := decryptData(data, key, iv)
	if err != nil {
		return 0, err
	}

	data3, err2 := strconv.ParseInt(data2, 10, 64)
	if err2 != nil {
		return 0, err2
	}

	_, err3 := io.WriteString(ieee, key+iv+"PassIdCRC32")
	if err3 != nil {
		return 0, err3
	}

	passId := int64(ieee.Sum32()) ^ data3
	return passId, nil
}
