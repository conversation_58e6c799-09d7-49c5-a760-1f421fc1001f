package passport

import (
	"fmt"
	"net/http"
	"net/url"
	"strconv"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

const (
	ReqConnTimeoutMs = 800
	ReqRWTimeoutMs   = 1000
	ReqMaxRetrys     = 3
)

const (
	SSN_GET_SESSION_DATA_BY_SID = 258
)

const (
	// UserTypeOld 老版注册用户
	UserTypeOld uint8 = 0
	// UserTypeReal 真实注册用户：绑定手机了
	UserTypeReal uint8 = 1
	// UserTypeIncomplete 半账号：老账号没有绑定手机
	UserTypeIncomplete uint8 = 2
	// UserTypeQuick 快推账号
	UserTypeQuick uint8 = 3
	// UserTypeTourist 游客账号：第三方oauth登录的
	UserTypeTourist uint8 = 4
	// UserLogInIsRealName 公有位GData[8]
	UserLogInIsRealName = 2
)

const (
	// 成功
	SSN_RSTATUS_OK = 0
	// 机器未授权
	SSN_RSTATUS_DENY        = 1
	SSN_RSTATUS_DENY_ATTACK = 2
	// 服务器暂时不能提供服务
	SSN_RSTATUS_SERVER_BUSY = 4
	// 提供的SessionID不正确
	SSN_RSTATUS_INVALID_SID = 8
	// 没有权限修改相应数据
	SSN_RSTATUS_NO_POWER = 16
	// 输入不满足要求
	SSN_RSTATUS_INVALID_PARAM = 32
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type SessionConfig struct {
	Tpl  string
	ApID int
}

type SessionReq struct {
	Bduss      string // 必选
	ClientIP   string // 必选
	Tourist    int    // 可选，默认0
	Incomplete int    // 可选，默认0
	NeedInfo   int    // 可选，默认0
}

type SessionResp struct {
	Status        int    // 命令处理状态，参考SessionRet*常量定义
	NeedSetCookie int    // 是否重置cookie，用于标识客户端是否需要重置cookie
	SessionID     string // bduss
	UserID        int64  // 帐号userID
	UserName      string // 帐号UserName
	DisplayName   string // 帐号DisplayName
	SecureEmail   string // 帐号secureemail绑定邮箱
	SecureMobile  string // 帐号securemobil绑定手机
	IsIncomplete  bool   // 解析是否是半账号
	IsRealName    bool   // 解析是否是实名认证
	IsTourist     bool   // 是否是游客账号
}

type PassSessionClient struct {
	sessionConf *SessionConfig
	optConf     *OtpConfig
	addr        *addr.Addr
}

func NewPassSessionCli(scfg *SessionConfig, optCfg *OtpConfig, addr *addr.Addr) *PassSessionClient {
	return &PassSessionClient{scfg, optCfg, addr}
}

func (t *PassSessionClient) Session(req *SessionReq) (*SessionResp, *RequestRes, error) {
	if !t.isInit() || req == nil || req.Bduss == "" || req.ClientIP == "" {
		return nil, nil, fmt.Errorf("not init or param unset")
	}

	v := url.Values{}
	v.Set("cm", fmt.Sprintf("%d", SSN_GET_SESSION_DATA_BY_SID))
	v.Set("cip", req.ClientIP)
	v.Set("apid", fmt.Sprintf("%d", t.sessionConf.ApID))
	v.Set("sid", req.Bduss)
	v.Set("tourist_user", fmt.Sprintf("%d", req.Tourist))
	v.Set("incomplete_user", fmt.Sprintf("%d", req.Incomplete))
	v.Set("need_info", fmt.Sprintf("%d", req.NeedInfo))
	v.Set("tpl", t.sessionConf.Tpl)
	v.Set("auth_token", GenOtpToken(t.optConf))
	param := v.Encode()

	reqRes, err := t.doRequestRetry("/ssn", param)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for publish asset fail.err:%v", err)
	}
	if reqRes.HttpCode != 200 {
		return nil, reqRes, fmt.Errorf("request passport http code not equal 200.code:%d url:%s",
			reqRes.HttpCode, reqRes.ReqUrl)
	}

	sessionRes, err := t.fmtSessionResp(reqRes.Body)
	if err != nil {
		return nil, reqRes, fmt.Errorf("format session resp failed or status not ok.url:%s body:%s",
			reqRes.ReqUrl, reqRes.Body)
	}

	return sessionRes, reqRes, nil
}

func (t *PassSessionClient) fmtSessionResp(body string) (*SessionResp, error) {
	v, err := url.ParseQuery(body)
	if err != nil {
		return nil, err
	}

	var status, rcookie int
	var sessionID, semail, smobil, gdata, uname, dname string
	var uid int64

	if status, err = strconv.Atoi(v.Get("status")); err != nil {
		return nil, fmt.Errorf("format session resp status failed.err:%v", err)
	}
	if status != SSN_RSTATUS_OK {
		return nil, fmt.Errorf("passport session resp status not ok.status:%d", status)
	}

	if rcookie, err = strconv.Atoi(v.Get("need_reset_cookie")); err != nil {
		return nil, fmt.Errorf("format session resp need_reset_cookie failed.err:%v", err)
	}
	if uid, err = strconv.ParseInt(v.Get("uid"), 10, 64); err != nil {
		return nil, fmt.Errorf("format session resp uid failed.err:%v", err)
	}
	sessionID = v.Get("session_id")
	semail = v.Get("secureemail")
	smobil = v.Get("securemobil")
	gdata = v.Get("global_data")
	if uname, err = gbkToUTF8(v.Get("username")); err != nil {
		return nil, fmt.Errorf("gbk username to utf8 failed.err:%v", err)
	}
	if dname, err = gbkToUTF8(v.Get("displayname")); err != nil {
		return nil, fmt.Errorf("gbk displayname to utf8 failed.err:%v", err)
	}

	resp := &SessionResp{
		Status:        status,
		NeedSetCookie: rcookie,
		SessionID:     sessionID,
		UserID:        uid,
		UserName:      uname,
		DisplayName:   dname,
		SecureEmail:   semail,
		SecureMobile:  smobil,
		IsIncomplete:  t.isIncompleteUser([]byte(gdata)),
		IsRealName:    t.isRealName([]byte(gdata)),
		IsTourist:     t.isTouristUser([]byte(gdata)),
	}

	return resp, nil
}

func (t *PassSessionClient) isIncompleteUser(gData []byte) bool {
	if len(gData) < 8 {
		return false
	}

	return gData[7] == UserTypeIncomplete
}

func (t *PassSessionClient) isTouristUser(gData []byte) bool {
	if len(gData) < 8 {
		return false
	}

	return gData[7] == UserTypeTourist
}

func (t *PassSessionClient) isRealName(gData []byte) bool {
	if len(gData) < 8 {
		return false
	}

	if (gData[8]&0xc0)>>6 == UserLogInIsRealName {
		return true
	}

	return false
}

func (t *PassSessionClient) doRequestRetry(api, data string) (*RequestRes, error) {
	var resp *RequestRes
	var err error
	for i := 0; i < ReqMaxRetrys; i++ {
		resp, err = t.doRequest(api, data)
		if err == nil {
			break
		}
	}
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// get
func (t *PassSessionClient) doRequest(api string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}

	reqUrl := fmt.Sprintf("http://%s:%s%s?%s", hostInfo.Host, hostInfo.Port, api, data)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}

	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	resp, err := httpclient.Get(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request passport fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)

	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	return result, nil
}

func (t *PassSessionClient) isInit() bool {
	if t == nil || t.sessionConf == nil || t.optConf == nil || t.addr == nil {
		return false
	}

	return true
}
