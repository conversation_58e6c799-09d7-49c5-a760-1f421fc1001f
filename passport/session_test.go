package passport

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
)

func TestSession(t *testing.T) {
	obj := NewPassSessionCli(testSessionConf(), testOptConf(), testAddr())

	req := &SessionReq{
		Bduss:    "xxx",
		ClientIP: "***********",
	}

	res, resp, err := obj.Session(req)
	if err != nil {
		t.<PERSON>rro<PERSON>("err:%v", err)
		return
	}

	fmt.Println("Session_Res:", res)
	fmt.Println("Resp:", resp)
}

func testOptConf() *OtpConfig {
	return &OtpConfig{
		KeyVersion:   1,
		TokenVersion: 1,
		Key:          "xxx",
		ApID:         123,
	}
}

func testSessionConf() *SessionConfig {
	return &SessionConfig{
		Tpl:  "xxx",
		ApID: 123,
	}
}

func testAddr() *addr.Addr {
	// 线上集群
	bns := "group.smartbns-from_product=ess-session%group.flow-ssngate.passport.all:http"
	a, _ := addr.NewAddrByBns(bns)
	return a
}
