package passport

import (
	"bytes"
	"io/ioutil"

	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

// gbkToUTF8 convert GBK to UTF-8
func gbkToUTF8(str string) (string, error) {
	s := []byte(str)
	I := bytes.NewReader(s)
	O := transform.NewReader(I, simplifiedchinese.GBK.NewDecoder())
	d, e := ioutil.ReadAll(O)
	if e != nil {
		return str, e
	}
	return string(d), nil
}
