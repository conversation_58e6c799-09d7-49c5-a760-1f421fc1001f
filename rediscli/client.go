package rediscli

import (
	"crypto/sha1"
	"encoding/hex"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"

	"github.com/mediocregopher/radix.v2/redis"
)

type RedisClient struct {
	rdsCli  *redis.Client
	timeout time.Duration
	// 保证刷新链接时线程安全
	lock *sync.RWMutex
	// 存储当前使用的host信息
	host *addr.HostInfo
	// 同时支持bns+host.
	addr *addr.Addr
}

func NewRedis(paddr *addr.Addr, timeout time.Duration) (*RedisClient, error) {
	if paddr == nil {
		return nil, errors.New("addr is empty")
	}

	h, err := paddr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail by addr.GetAddr.err:%v", err)
	}

	obj := new(RedisClient)
	obj.timeout = timeout
	obj.lock = new(sync.RWMutex)
	obj.addr = paddr
	obj.host = h

	rHost := fmt.Sprintf("%s:%s", obj.host.Host, obj.host.Port)
	r, err := redis.DialTimeout("tcp", rHost, obj.timeout)
	if err != nil {
		obj.addr.SetFail(obj.host)
		return nil, fmt.Errorf("redis.DialTimeout fail.host:%s err:%v", rHost, err)
	}
	obj.addr.SetOk(obj.host)
	obj.rdsCli = r

	return obj, nil
}

func NewRedisByHost(host string, timeout time.Duration) (*RedisClient, error) {
	a, err := addr.NewAddrByHost([]string{host})
	if err != nil {
		return nil, fmt.Errorf("new addr by host fail.addr:%s err:%v", host, err)
	}

	return NewRedis(a, timeout)
}

func NewRedisByBns(bnsName string, timeout time.Duration) (*RedisClient, error) {
	a, err := addr.NewAddrByBns(bnsName)
	if err != nil {
		return nil, fmt.Errorf("new addr by bns fail.bns:%s err:%v", bnsName, err)
	}

	return NewRedis(a, timeout)
}

func (r *RedisClient) Close() {
	r.lock.Lock()
	defer r.lock.Unlock()

	if r.rdsCli != nil {
		r.rdsCli.Close()
	}
}

func (r *RedisClient) RefreshConn() error {
	// 先创建新redis client，注意线程安全
	h, err := r.addr.GetAddr()
	if err != nil {
		return fmt.Errorf("get addr fail by addr.GetAddr.err:%v", err)
	}

	rHost := fmt.Sprintf("%s:%s", h.Host, h.Port)
	robj, err := redis.DialTimeout("tcp", rHost, r.timeout)
	if err != nil {
		r.addr.SetFail(h)
		return fmt.Errorf("redis.DialTimeout fail.host:%s err:%v", rHost, err)
	}
	r.addr.SetOk(h)

	// 加锁
	r.lock.Lock()
	defer r.lock.Unlock()

	if r.rdsCli != nil {
		r.rdsCli.Close()
	}
	r.rdsCli = robj
	r.host = h

	return nil
}

func (r *RedisClient) Sadd(key string, value ...interface{}) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("sadd", key, value)
	return res.Int()
}

func (r *RedisClient) Scard(key string) (int64, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("scard", key)
	return res.Int64()
}

func (r *RedisClient) Smembers(key string) ([]string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("smembers", key)
	return res.List()
}

func (r *RedisClient) Sscan(key string, cursor int, match string, count uint32) (nextCursor int, list []string, err error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("sscan", key, cursor, "match", match, "count", count)

	var respArr []*redis.Resp
	respArr, err = res.Array()
	if err != nil {
		return -1, nil, err
	}

	if len(respArr) < 2 {
		return -1, nil, fmt.Errorf("redis result data is error")
	}

	nextCursor, err = respArr[0].Int()
	if err != nil {
		return -1, nil, err
	}

	list, err = respArr[1].List()
	if err != nil {
		return -1, nil, err
	}

	return nextCursor, list, nil
}

func (r *RedisClient) SpopInt64(key string) (int64, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("spop", key)
	if res.IsType(redis.Nil) {
		return 0, nil
	}

	return res.Int64()
}

func (r *RedisClient) SpopStr(key string) (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("spop", key)
	if res.IsType(redis.Nil) {
		return "", nil
	}

	return res.Str()
}

func (r *RedisClient) Srandmember(key string, count int) ([]string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("srandmember", key, count)
	return res.List()
}

func (r *RedisClient) Srem(key string, value ...interface{}) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("srem", key, value)
	return res.Int()
}

func (r *RedisClient) Sismember(key, value string) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("sismember", key, value)
	return res.Int()
}

func (r *RedisClient) Hset(key string, field, value interface{}) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("hset", key, field, value)
	return res.Int()
}

func (r *RedisClient) HsetNx(key string, field, value interface{}) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("hsetnx", key, field, value)
	return res.Int()
}

func (r *RedisClient) HgetInt64(key string, field interface{}) (int64, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("hget", key, field)
	if res.IsType(redis.Nil) {
		return 0, nil
	}

	return res.Int64()
}

func (r *RedisClient) HgetStr(key string, field interface{}) (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("hget", key, field)
	if res.IsType(redis.Nil) {
		return "", nil
	}

	return res.Str()
}

func (r *RedisClient) Hincrby(key string, field interface{}, increment int) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("hincrby", key, field, increment)
	return res.Int()
}

func (r *RedisClient) Hgetall(key string) (map[string]string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("hgetall", key)
	return res.Map()
}

func (r *RedisClient) Hvals(key string) ([]string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("hvals", key)
	return res.List()
}

func (r *RedisClient) Hexists(key string, field interface{}) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("hexists", key, field)
	return res.Int()
}

// prod环境，redis禁用该功能
func (r *RedisClient) KeysPrefix(keyPrefix string) ([]string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("keys", keyPrefix+"*")
	return res.List()
}

func (r *RedisClient) DelKeys(keys ...string) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("del", keys)
	return res.Int()
}

// 线上指令incr, decr, incrby, decrby, incrbyfloat, decrbyfloat不会将有时限的key重写为永久key
// 编程时应注意这一点，确保过期时间再次调用Expire或ExpireAt, 确保更新为永久key调用Persist
func (r *RedisClient) Expire(key string, durations int) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("expire", key, durations)
	return res.Int()
}

func (r *RedisClient) ExpireAt(key string, timestamp int64) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("expireat", key, timestamp)
	return res.Int()
}

func (r *RedisClient) Persist(key string) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("persist", key)
	return res.Int()
}

func (r *RedisClient) Ttl(key string) (int64, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("ttl", key)
	return res.Int64()
}

func (r *RedisClient) SetEx(key string, value interface{}, expire int) (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("set", key, value, "ex", expire)
	return res.Str()
}

func (r *RedisClient) SetNx(key string, value interface{}) (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("set", key, value, "nx")
	if res.IsType(redis.Nil) {
		return "", nil
	}
	return res.Str()
}

func (r *RedisClient) SetExNx(key string, value interface{}, expire int) (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("set", key, value, "ex", expire, "nx")
	if res.IsType(redis.Nil) {
		return "", nil
	}

	return res.Str()
}

func (r *RedisClient) Set(key string, value interface{}) (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("set", key, value)
	return res.Str()
}

func (r *RedisClient) SetExBySet(key string, value interface{}, expire int) (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("set", key, value, "ex", expire)
	return res.Str()
}

func (r *RedisClient) Scan(cursor int, match string, count uint32) (next int, list []string, err error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("scan", cursor, "match", match, "count", count)

	var respArr []*redis.Resp
	respArr, err = res.Array()
	if err != nil {
		return -1, nil, err
	}

	if len(respArr) < 2 {
		return -1, nil, fmt.Errorf("redis result data is error")
	}

	next, err = respArr[0].Int()
	if err != nil {
		return -1, nil, err
	}

	list, err = respArr[1].List()
	if err != nil {
		return -1, nil, err
	}

	return next, list, nil
}

func (r *RedisClient) GetStr(key string) (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("get", key)
	if res.IsType(redis.Nil) {
		return "", nil
	}

	return res.Str()
}

func (r *RedisClient) GetInt(key string) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("get", key)
	if res.IsType(redis.Nil) {
		return 0, nil
	}

	return res.Int()
}

func (r *RedisClient) GetSetInt64(key string, value interface{}) (int64, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("getset", key, value)
	if res.IsType(redis.Nil) {
		return 0, nil
	}

	return res.Int64()
}

func (r *RedisClient) GetInt64(key string) (int64, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("get", key)
	if res.IsType(redis.Nil) {
		return 0, nil
	}

	return res.Int64()
}

func (r *RedisClient) Exists(key string) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("exists", key)
	return res.Int()
}

func (r *RedisClient) Incr(key string) (int64, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("incr", key)
	return res.Int64()
}

func (r *RedisClient) Incrby(key string, increment int64) (int64, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("incrby", key, increment)
	return res.Int64()
}

func (r *RedisClient) Decr(key string) (int64, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("decr", key)
	return res.Int64()
}

func (r *RedisClient) Decrby(key string, increment int64) (int64, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("decrby", key, increment)
	return res.Int64()
}

func (r *RedisClient) MgetStr(keys ...string) ([]string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("mget", keys)
	return res.List()
}

// 虽然mset是多key操作，但不需要所有的key映射在同一slot中
func (r *RedisClient) Mset(args ...interface{}) (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("mset", args)
	return res.Str()
}

// msetnx prod环境文档中未提及支持该指令，当且仅当所有keys映射在同一slot中时，目前可以保证该指令正确执行并返回预期结果
func (r *RedisClient) MsetNx(args ...interface{}) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("msetnx", args)
	if res.IsType(redis.Nil) {
		return -1, nil
	}
	return res.Int()
}

func (r *RedisClient) LpopStr(key string) (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("lpop", key)
	if res.IsType(redis.Nil) {
		return "", nil
	}

	return res.Str()
}

func (r *RedisClient) RpopStr(key string) (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("rpop", key)
	if res.IsType(redis.Nil) {
		return "", nil
	}

	return res.Str()
}

// rpoplpush使用要求：两个key必须映射在同一slot中，需要使用hashtag
func (r *RedisClient) RpoplpushStr(source, destination string) (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("rpoplpush", source, destination)
	if res.IsType(redis.Nil) {
		return "", nil
	}

	return res.Str()
}

func (r *RedisClient) LpopInt64(key string) (int64, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("lpop", key)
	if res.IsType(redis.Nil) {
		return 0, nil
	}

	return res.Int64()
}

func (r *RedisClient) LlenInt64(key string) (int64, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("llen", key)
	return res.Int64()
}

func (r *RedisClient) LpushInt(key, value string) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("lpush", key, value)
	return res.Int()
}

func (r *RedisClient) LpushMultiInt(key string, value ...interface{}) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("lpush", key, value)
	return res.Int()
}

func (r *RedisClient) RpushInt(key, value string) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("rpush", key, value)
	return res.Int()
}

func (r *RedisClient) Lrange(key string, start, stop int64) ([]string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("lrange", key, start, stop)
	return res.List()
}

func (r *RedisClient) Lrem(key, value string, count int64) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("lrem", key, count, value)
	return res.Int()
}

func (r *RedisClient) Ltrim(key string, start, stop int64) (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("ltrim", key, start, stop)
	return res.Str()
}

func (r *RedisClient) Zadd(key, member string, score int64) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("zadd", key, score, member)
	return res.Int()
}

func (r *RedisClient) ZscoreInt64(key, member string) (int64, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("zscore", key, member)
	return res.Int64()
}

func (r *RedisClient) Zrevrange(key string, start, stop int64) (map[string]string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("zrevrange", key, start, stop, "WITHSCORES")
	return res.Map()
}

func (r *RedisClient) ZrevrangeForList(key string, start, stop int64) ([]string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("zrevrange", key, start, stop, "WITHSCORES")
	return res.List()
}

// 只返回排名，不返回score信息
func (r *RedisClient) ZrevrangeForListNoScore(key string, start, stop int64) ([]string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("zrevrange", key, start, stop)
	return res.List()
}

func (r *RedisClient) Zrevrangebyscore(key, max, min string, offset, count uint32) ([]string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("zrevrangebyscore", key, max, min, "limit", offset, count)
	return res.List()
}

func (r *RedisClient) Zrevrank(key, member string) (int64, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("zrevrank", key, member)

	if res.IsType(redis.Nil) {
		return -1, nil
	}
	return res.Int64()
}

func (r *RedisClient) Zcard(key string) (int64, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("zcard", key)
	return res.Int64()
}

func (r *RedisClient) Zremrangebyscore(key string, min, max int64) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("zremrangebyscore", key, min, max)
	return res.Int()
}

func (r *RedisClient) Zremrangebyrank(key string, min, max int64) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("zremrangebyrank", key, min, max)
	return res.Int()
}

func (r *RedisClient) Zrem(key string, members ...string) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("zrem", key, members)
	return res.Int()
}

func (r *RedisClient) Hdel(key string, field string) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("hdel", key, field)
	return res.Int()
}

func (r *RedisClient) Hdels(key string, fields ...string) (int, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("hdel", key, fields)
	return res.Int()
}

func (r *RedisClient) Hscan(key string, index, count int) (int, map[string]string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("hscan", key, index, "count", count)
	arr, err := res.Array()
	if err != nil {
		return index, nil, err
	}
	if len(arr) < 2 {
		return index, nil, errors.New("hscan return error")
	}

	index, err = arr[0].Int()
	if err != nil {
		return index, nil, fmt.Errorf("get index fail.err:%v", err)
	}
	if arr[1].IsType(redis.Err) {
		return index, nil, fmt.Errorf("hscan result is error")
	}
	if arr[1].IsType(redis.Nil) {
		return index, nil, nil
	}
	resMap, err := arr[1].Map()
	if err != nil {
		return index, nil, fmt.Errorf("map hscan result fail.err:%v", err)
	}

	return index, resMap, nil
}

func (r *RedisClient) Hmset(key string, value ...interface{}) (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("hmset", key, value)
	return res.Str()
}

func (r *RedisClient) Hmget(key string, fields ...interface{}) ([]string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("hmget", key, fields)
	if res.IsType(redis.Nil) {
		return nil, fmt.Errorf("key not exist.key:%s", key)
	}

	return res.List()
}

func (r *RedisClient) Hlen(key string) (int64, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("hlen", key)
	return res.Int64()
}

func (r *RedisClient) Hkeys(key string) ([]string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	res := r.rdsCli.Cmd("hkeys", key)
	return res.List()
}

// eval使用要求：多个key时，保证script中所有的key落在同一slot中，需要使用hashtag
func (r *RedisClient) Eval(script string, keys []string, args ...interface{}) *redis.Resp {
	r.lock.RLock()
	defer r.lock.RUnlock()

	return r.rdsCli.Cmd("EVAL", script, len(keys), keys, args)
}

// evalsha使用要求：多个key时，保证script中所有的key落在同一slot中，需要使用hashtag
func (r *RedisClient) EvalSHA(script string, keys []string, args ...interface{}) *redis.Resp {
	r.lock.RLock()
	defer r.lock.RUnlock()

	sumRaw := sha1.Sum([]byte(script))
	sum := hex.EncodeToString(sumRaw[:])

	resp := r.rdsCli.Cmd("EVALSHA", sum, len(keys), keys, args)

	if resp.Err != nil && strings.HasPrefix(resp.Err.Error(), "NOSCRIPT") {
		resp = r.rdsCli.Cmd("EVAL", script, len(keys), keys, args)
	}

	return resp
}

func (r *RedisClient) Watch(watchKey string) (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	return r.rdsCli.Cmd("WATCH", watchKey).Str()
}

func (r *RedisClient) MULTI() (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	return r.rdsCli.Cmd("MULTI").Str()
}

func (r *RedisClient) EXEC() ([]*redis.Resp, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	resp := r.rdsCli.Cmd("EXEC")
	if resp.Err != nil {
		return nil, resp.Err
	}
	array, err := resp.Array()
	if err != nil {
		return nil, nil
	}
	return array, nil
}
