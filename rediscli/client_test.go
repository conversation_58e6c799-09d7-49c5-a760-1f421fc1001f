package rediscli

import (
	"fmt"
	"reflect"
	"testing"
	"time"
)

func TestSadd(t *testing.T) {
	redis, err := NewRedisByHost("************:8810", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key := "test:test"
	res, err := redis.Sadd(key, 12345677)
	fmt.Println(res, err)
}

func TestSpopInt64(t *testing.T) {
	redis, err := NewRedisByHost("************:8810", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key := "test:test"

	res, err := redis.Scard(key)
	fmt.Println(res, err)
	res, err = redis.SpopInt64(key)
	fmt.Println(res, err)

	res, err = redis.Scard(key)
	fmt.Println(res, err)
	res, err = redis.SpopInt64(key)
	fmt.Println(res, err)
}

func TestMget(t *testing.T) {
	redis, err := NewRedisByHost("************:8810", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}

	key := "n-skynet:bf:2016101717:uids:ip"
	res, err := redis.MgetStr("123", "234", key)
	fmt.Println(res, err)
}

func TestSetEx(t *testing.T) {
	redis, err := NewRedisByHost("127.0.0.1:6379", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key := "watch-key1"
	res, err := redis.SetEx(key, "test", 600)
	fmt.Println(res, err)
}

func TestSetNx(t *testing.T) {
	redis, err := NewRedisByHost("127.0.0.1:6379", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key := "watch-key1"
	res, err := redis.SetNx(key, "test")
	fmt.Println(res, err)

	res, err = redis.SetNx(key, "test")
	fmt.Println(res, err)
}

func TestSetExNx(t *testing.T) {
	redis, err := NewRedisByHost("************:8810", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key := "nd-pmalls-increment_count_lock"
	res, err := redis.SetExNx(key, "1", 1800)
	fmt.Println(res, err)
}

func TestGet(t *testing.T) {
	redis, err := NewRedisByHost("127.0.0.1:6379", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key := "n-stbf:querycache:1401170263"
	res, err := redis.GetStr(key)
	fmt.Println(res, err)
}

func TestDel(t *testing.T) {
	redis, err := NewRedisByHost("************:8810", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key := "n-stbf:querycache:1401170263"
	res, err := redis.DelKeys(key)
	fmt.Println(res, err)
}

func TestZadd(t *testing.T) {
	redis, err := NewRedisByHost("************:8810", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key := "nd-test:heartbeat:1401170263"
	res, err := redis.Zadd(key, "cp01-netdisk-od24.cp01", time.Now().Unix())
	fmt.Println(res, err)
}

func TestZscoreInt64(t *testing.T) {
	redis, err := NewRedisByHost("************:8810", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key := "nd-test:heartbeat:1401170263"
	res, err := redis.ZscoreInt64(key, "cp01-netdisk-od24.cp01")
	fmt.Println(res, err)
}

func TestZrevrange(t *testing.T) {
	redis, err := NewRedisByHost("*************:6379", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key := "nd-youas:tools:msg"
	res, err := redis.Zrevrange(key, 0, 9)
	fmt.Println(res, err)
}

func TestZrevrangebyscore(t *testing.T) {
	redis, err := NewRedisByHost("*************:6379", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key := "nd-youas:tools:msg"
	res, err := redis.Zrevrangebyscore(key, "+inf", "0", 0, 9)
	fmt.Println(res, err)
}

func TestHscan(t *testing.T) {
	redis, err := NewRedisByHost("************:8810", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	res, list, err := redis.Hscan("nd-bells:flight-que:201901031", 0, 1)
	fmt.Println(res, list, err)
}

func TestHmset(t *testing.T) {
	redis, err := NewRedisByHost("************:8810", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	res, err := redis.Hmset("nd-bells:logid:test", "123211", "asdaAad1",
		"123213", "asdaAad3", "123214", "asdaAad4")
	fmt.Println(res, err)
}

func TestHmget(t *testing.T) {
	redis, err := NewRedisByHost("************:8810", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	list, err := redis.Hmget("nd-bells:logid:test", "123211", "123212", "123213", "123214")
	fmt.Println(list, err)

	list2, err := redis.Hmget("nd-bells:logid:test", "123212")
	fmt.Println(list2, err)
}

func TestSrandmember(t *testing.T) {
	redis, err := NewRedisByHost("************:8810", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	list, err := redis.Srandmember("nd-bells:listcard:test", 2)
	fmt.Println(list, err)
}

func TestHvals(t *testing.T) {
	redis, err := NewRedisByHost("************:8810", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	list, err := redis.Hvals("nd-bells:relation:123")
	fmt.Println(list, err)
}

func TestHexists(t *testing.T) {
	redis, err := NewRedisByHost("************:8810", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	list, err := redis.Hexists("nd-bells:relation:123", "456")
	fmt.Println(list, err)

	list, err = redis.Hexists("nd-bells:relation:123", "666")
	fmt.Println(list, err)
}

func TestLrange(t *testing.T) {
	redis, err := NewRedisByHost("************:8810", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	list, err := redis.Lrange("nd-bells:msg:test", 0, -1)
	fmt.Println(list, err)
}

func TestLrem(t *testing.T) {
	redis, err := NewRedisByHost("127.0.0.1:6389", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	list, err := redis.Lrem("nd-bells:msg:test", "test", -1)
	fmt.Println(list, err)
}

func TestHlen(t *testing.T) {
	redis, err := NewRedisByHost("************:8810", 3*time.Second)
	if err != nil {
		t.Fatalf("new redis by host fail.err:%v", err)
	}
	defer redis.Close()

	key := "nd:redis:client:test"
	length, err := redis.Hlen(key)
	if err != nil {
		t.Fatalf("redis hlen fail.err:%v", err)
	}
	t.Logf("redis hlen success.key:%s len:%d", key, length)
}

func TestIncrby(t *testing.T) {
	redis, err := NewRedisByHost("************:8810", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key := "test:incrby"
	res, err := redis.Incrby(key, 10)
	if err != nil {
		t.Errorf("redis.incrby fail, err: %v", err)
	}

	fmt.Println("res: ", res)
}

func TestZrem(t *testing.T) {
	redis, err := NewRedisByHost("yike-rd-comdev.bcc-bdbl.baidu.com:6379", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key := "test:zrem:20210112"
	var mems []string
	for i := 1; i <= 5; i++ {
		mem := fmt.Sprintf("mem_%d", i)
		score := int64(100 - i)
		_, err = redis.Zadd(key, mem, score)
		if err != nil {
			fmt.Println("zadd fail.err: ", err.Error())
			return
		}

		mems = append(mems, mem)
	}

	fmt.Println("zadd succ")

	resCnt, err := redis.Zrem(key, mems...)
	if err != nil {
		t.Errorf("redis.zrem fail, err: %v", err)
	}

	fmt.Println("zrem succ, resCnt: ", resCnt)
}

func TestSscan(t *testing.T) {
	redis, err := NewRedisByHost("yike-rd-comdev.bcc-bdbl.baidu.com:6379", 3*time.Second)
	if err != nil {
		t.Errorf("new redis client fail, err: %v", err)
		return
	}
	defer redis.Close()

	key := "nums"
	cursor, list, err := redis.Sscan(key, 0, "*", 400)
	if err != nil {
		t.Errorf("redis.sscan fail, err: %v", err)
		return
	}

	fmt.Printf("sscan succ, cursor: %d, list: %v \n", cursor, list)
}
func TestZcard(t *testing.T) {
	redis, err := NewRedisByHost("*************:6379", 3*time.Second)
	if err != nil {
		t.Fatalf("new redis by host fail.err:%v", err)
	}
	defer redis.Close()

	key := "nd-youas:tools:msg"
	length, err := redis.Zcard(key)
	if err != nil {
		t.Fatalf("redis zcard fail.err:%v", err)
	}
	t.Logf("redis zcard success.key:%s len:%d", key, length)
}

// go test -test.run TestEval
// go test -v --run TestEval
func TestEval(t *testing.T) {
	cli, err := NewRedisByHost("*************:6379", 3*time.Second)
	if err != nil {
		t.Fatalf("new redis by host fail.err:%v", err)
	}
	defer cli.Close()

	key1 := "test:key1"
	val1 := "1234567890"

	cli.Set(key1, val1)

	script := `
	if redis.call('GET', KEYS[1]) == ARGV[1] then
		redis.call('DEL', KEYS[1]);
		return 1;
		end;
	return 0;
		`

	resp := cli.Eval(script, []string{key1}, val1)

	if resp.Err != nil {
		t.Fatalf("resp err: %v", resp.Err)
	}
	ret, err := resp.Int()
	if err != nil {
		t.Fatalf("resp int err: %v", err)
	}

	if ret != 1 {
		t.Fatalf("resp int not 1 err: %v", err)
	}

	t.Log("ok:", ret)
}

// go env -w GO111MODULE=off
// go test -test.run TestEvalSHA
// go test -v --run TestEval
func TestEvalSHA(t *testing.T) {
	cli, err := NewRedisByHost("*************:6379", 3*time.Second)
	if err != nil {
		t.Fatalf("new redis by host fail.err:%v", err)
	}
	defer cli.Close()

	key1 := "test:key21"
	val1 := "2234567890"

	script := `
	redis.call('SET', KEYS[2], KEYS[3])
	redis.call('EXPIRE', KEYS[2], 60)
	if redis.call('GET', KEYS[1]) == ARGV[1] then
		redis.call('DEL', KEYS[1]);
		return 1;
		end;
	return 0;
		`

	for i := 0; i < 5; i++ {
		cli.Set(key1, val1)

		resp := cli.EvalSHA(script, []string{key1, key1 + "2", key1 + "3"}, val1)

		if resp.Err != nil {
			t.Fatalf("resp int err: %v", err)
		}
		ret, err := resp.Int()
		if err != nil {
			t.Fatalf("resp int err: %v", err)
		}
		if ret != 1 {
			t.Fatalf("resp int not 1 err: %v", err)
		}
	}

	t.Log("ok")
}

func TestMset(t *testing.T) {
	redis, err := NewRedisByHost("*************:6379", 3*time.Second)
	if err != nil {
		t.Fatalf("new redis by host fail.err:%v", err)
	}
	defer redis.Close()

	k1, v1 := "test:mset-key1", "mset-val1"
	k2, v2 := "test:mset-key2", "mset-val2"
	k3, v3 := "test:mset-key3", "mset-val3"
	resp, err := redis.Mset(k1, v1, k2, v2, k3, v3)
	if err != nil {
		t.Fatalf("redis mset fail.err:%v", err)
	}
	if resp != "OK" {
		t.Errorf("redis mset response unexpect.got:%v expect:%v", resp, "OK")
	}

	vals, err := redis.MgetStr(k1, k2, k3)
	if err != nil {
		t.Fatalf("redis mget fail.err:%v", err)
	}
	if len(vals) != 3 {
		t.Errorf("MgetStr response len unexpect.got:%v want:%v", len(vals), 3)
	}

	want := []string{v1, v2, v3}
	if !reflect.DeepEqual(vals, want) {
		t.Errorf("Mgetstr response content unexptect.got:%v want:%v", vals, want)
	}
}

func TestHkeys(t *testing.T) {
	redis, err := NewRedisByHost("*************:6379", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key := "nd-youas:works:medal_user_works"
	res, err := redis.Hkeys(key)
	if err != nil {
		t.Fatalf("redis.incrby fail, err: %v", err)
	}

	t.Logf("res: %v", res)
}

func TestDecr(t *testing.T) {
	redis, err := NewRedisByHost("*************:6389", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key := "test:decr"
	res, err := redis.Decr(key)
	if err != nil {
		t.Errorf("redis.decr fail, err: %v", err)
	}

	fmt.Println("res: ", res)
}

func TestDecrby(t *testing.T) {
	redis, err := NewRedisByHost("*************:6389", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key := "test:decrby"
	res, err := redis.Decrby(key, 10)
	if err != nil {
		t.Errorf("redis.decrby fail, err: %v", err)
	}

	fmt.Println("res: ", res)
}

func TestMsetNx(t *testing.T) {
	redis, err := NewRedisByHost("127.0.0.1:6389", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	key1, key2, key3 := "{testmsetnx}:1", "{testmsetnx}:2", "{testmsetnx}:3"
	res, err := redis.MsetNx(key1, 1, key2, 2, key3, 3)
	if err != nil {
		t.Errorf("redis.msetnx fail, err: %v", err)
	}
	fmt.Println("res: ", res)

	strs, err := redis.MgetStr(key1, key2, key3)
	if err != nil {
		t.Errorf("redis.mget fail, err: %v", err)
	}
	fmt.Println("strs: ", strs)
}

func TestWatchAndSetEx(t *testing.T) {
	redis, err := NewRedisByHost("127.0.0.1:6379", 3*time.Second)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer redis.Close()

	redis.Watch("watchkey1")
	time.Sleep(5 * time.Second)
	redis.MULTI()
	redis.SetEx("watchkey1", 1, 600)
	ok, err := redis.EXEC()
	if err != nil {
		t.Error(err)
	}
	fmt.Println(ok)
}

// 由于事务中只有一个命令，所以返回结果有两种：1.事务不会执行，返回空 2.事务执行，返回一个执行结果
func (r *RedisClient) WatchAndSetEx(watchKey, key string, value interface{},
	expire int) (string, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	resp := r.rdsCli.Cmd("WATCH", watchKey)
	if resp.Err != nil {
		return resp.Str()
	}
	resp = r.rdsCli.Cmd("MULTI")
	if resp.Err != nil {
		return resp.Str()
	}
	resp = r.rdsCli.Cmd("SET", key, value, "EX", expire)
	if resp.Err != nil {
		return resp.Str()
	}
	resp = r.rdsCli.Cmd("EXEC")
	if resp.Err != nil {
		return resp.Str()
	}
	array, err := resp.Array()
	if err != nil {
		return "", err
	}
	if len(array) == 0 {
		return "", nil
	}
	return array[0].Str()
}
