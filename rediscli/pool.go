package rediscli

import (
	"time"

	"github.com/mediocregopher/radix.v2/pool"
	"github.com/mediocregopher/radix.v2/redis"
)

type RedisPool struct {
	redisPool *pool.Pool
	closeChan chan int
}

const KEEP_ALIVE_CHECK_TIME_S = 3

// 并发安全，addr被用来初始化连接池和需要新增加连接时创建新连接
// 可以自己*redis.Client put到连接池中
func NewRedisPool(addr string, size int) (*RedisPool, error) {
	redisPool, err := pool.New("tcp", addr, size)
	if err != nil {
		return nil, err
	}

	obj := new(RedisPool)
	obj.redisPool = redisPool
	obj.closeChan = make(chan int)
	go obj.keepAlive()

	return obj, nil
}

func NewRedisCli(addr string) (*redis.Client, error) {
	return redis.Dial("tcp", addr)
}

func (t *RedisPool) keepAlive() {
	ticker := time.NewTicker(KEEP_ALIVE_CHECK_TIME_S * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			t.redisPool.Cmd("PING")
		case <-t.closeChan:
			return
		}
	}
}

// get一个链接执行redis命令
func (t *RedisPool) GetConn() (*redis.Client, error) {
	return t.redisPool.Get()
}

// put一个空闲连接到连接池
func (t *RedisPool) PutConn(conn *redis.Client) {
	t.redisPool.Put(conn)
}

// 关闭连接池
func (t *RedisPool) Close() {
	t.closeChan <- 1
	t.redisPool.Empty()
}

// 获取连接数
func (t *RedisPool) GetAvail() int {
	return t.redisPool.Avail()
}

func (r *RedisPool) GetStr(key string) (string, error) {
	res := r.redisPool.Cmd("get", key)
	if res.IsType(redis.Nil) {
		return "", nil
	}

	return res.Str()
}
