package rediscli

import (
	"fmt"
	"testing"
	"time"
)

func TestGetStr(t *testing.T) {
	p, err := NewRedisPool("10.92.172.59:8810", 10)
	if err != nil {
		fmt.Println(err)
		return
	}

	num := p.GetAvail()
	fmt.Println(num)

	str, err := p.GetStr("test:test")
	if err != nil {
		fmt.Println(err)
	}

	fmt.Println("str:", str)

	time.Sleep(10 * time.Second)

	p.Close()

	num = p.GetAvail()
	fmt.Println(num)

	time.Sleep(10 * time.Second)
}
