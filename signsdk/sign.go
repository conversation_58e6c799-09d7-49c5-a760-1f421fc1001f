package signsdk

import (
	"crypto/ecdsa"
	"encoding/hex"
	"strconv"

	"github.com/xuperchain/crypto/core/sign"

	"github.com/xuperchain/xuper-sdk-go/v2/crypto"

	"icode.baidu.com/baidu/blockchain/xasset-golib/utils"
)

type sCli struct {
	privateKey *ecdsa.PrivateKey
}

func InitSClient(path string) *sCli {
	privateKey, err := crypto.GetCryptoClient().GetEcdsaPrivateKeyFromFile(path)
	if err != nil {
		return nil
	}

	return &sCli{privateKey: privateKey}
}

// 签名商品：生成随机数、assetId, 对其进行签名并返回
func (sc *sCli) SignAsset(appId uint64) (int64, int64, string, error) {
	nonce := int64(utils.GenRequestId())
	strNonce := strconv.FormatInt(nonce, 10)
	assetId := int64(utils.GenIdHelp(appId, 0))
	strAssetId := strconv.FormatInt(assetId, 10)
	msgByte := []byte(strAssetId + strNonce)
	hash := utils.HashUsingSha256(msgByte)
	sign, err := signECDSA(sc.privateKey, hash)
	if err != nil {
		return 0, 0, "", err
	}

	return assetId, nonce, hex.EncodeToString(sign), nil
}

// 签名创建订单：根据参数组装代签名订单、对其签名并返回
func (sc *sCli) SignCreateOrder(assetId, oid int64) (string, error) {
	strAssetId := strconv.FormatInt(assetId, 10)
	strOid := strconv.FormatInt(oid, 10)
	msgByte := []byte(strAssetId + strOid)
	hash := utils.HashUsingSha256(msgByte)
	sign, err := signECDSA(sc.privateKey, hash)
	if err != nil {
		return "", err
	}

	return hex.EncodeToString(sign), nil
}

// 签名非创建订单接口：生成随机数、对订单编号和随机数签名并返回
func (sc *sCli) SignNonCreateOrder(oid int64) (int64, string, error) {
	nonce := int64(utils.GenRequestId())
	strNonce := strconv.FormatInt(nonce, 10)
	strOid := strconv.FormatInt(oid, 10)
	msgByte := []byte(strOid + strNonce)
	hash := utils.HashUsingSha256(msgByte)
	sign, err := signECDSA(sc.privateKey, hash)
	if err != nil {
		return 0, "", err
	}

	return nonce, hex.EncodeToString(sign), nil
}

func signECDSA(k *ecdsa.PrivateKey, msg []byte) ([]byte, error) {
	signature, err := sign.SignECDSA(k, msg)
	return signature, err
}
