package signsdk

import (
	"fmt"
	"testing"
)

func TestSignAsset(t *testing.T) {
	sCli := InitSClient("")
	assetId, nonce, sign, err := sCli.SignAsset(10086)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(assetId, nonce, sign)
}

func TestSignCreateOrder(t *testing.T) {
	sCli := InitSClient("")
	sign, err := sCli.SignCreateOrder(12345, 54321)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(sign)
}

func TestSignNonCreateOrder(t *testing.T) {
	sCli := InitSClient("")
	nonce, sign, err := sCli.SignNonCreateOrder(324121)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(nonce, sign)
}
