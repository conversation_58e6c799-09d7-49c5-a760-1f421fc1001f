package sms

import (
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type Conf struct {
	SmsSign  string
	Template string
	Tpl      string
	SmsType  string
}

type Client struct {
	host string
	conf *Conf
}

func NewClient(host string, conf *Conf) *Client {
	return &Client{
		host: host,
		conf: conf,
	}
}

// SendSmsByUid 通过pass uid发送短信
// http://dev.passport.baidu.com/v1/#/features/view/79/225
func (c *Client) SendSmsByUid(uid int64, content map[string]interface{}) error {
	if err := c.init(); err != nil {
		return err
	}
	args := map[string]interface{}{
		"user":     []string{fmt.Sprintf("%d", uid)},
		"mode":     SendModeUid,
		"content":  content,
		"sign":     c.conf.SmsSign,
		"template": c.conf.Template,
		"tpl":      c.conf.Tpl,
		"type":     c.conf.SmsType,
	}
	data, err := json.Marshal(args)
	if err != nil {
		return err
	}
	reqRes, err := c.doRequest(UrlSendSmsByPass, string(data))
	if err != nil {
		return fmt.Errorf("request sms service fail.err:%v", err)
	}
	var sendRes SendSmsResult
	if err = json.Unmarshal([]byte(reqRes.Body), &sendRes); err != nil {
		return fmt.Errorf("unmarshal sms resp body fail.err:%v body:%s", err, reqRes.Body)
	}
	if sendRes.Code != SuccCode {
		return fmt.Errorf("send sms fail. code: %d, data: %s", sendRes.Code, reqRes.Body)
	}
	return nil
}

// SendSmsByPhone 通过手机号发送短信
// http://dev.passport.baidu.com/v1/#/features/view/79/225
func (c *Client) SendSmsByPhone(phone string, content map[string]interface{}) error {
	if err := c.init(); err != nil {
		return err
	}
	args := map[string]interface{}{
		"user":     []string{phone},
		"mode":     SendModePhone,
		"content":  content,
		"sign":     c.conf.SmsSign,
		"template": c.conf.Template,
		"tpl":      c.conf.Tpl,
		"type":     c.conf.SmsType,
	}
	data, err := json.Marshal(args)
	if err != nil {
		return err
	}
	reqRes, err := c.doRequest(UrlSendSmsByPass, string(data))
	if err != nil {
		return fmt.Errorf("request sms service fail.err:%v", err)
	}
	var sendRes SendSmsResult
	if err = json.Unmarshal([]byte(reqRes.Body), &sendRes); err != nil {
		return fmt.Errorf("unmarshal sms resp body fail.err:%v body:%s", err, reqRes.Body)
	}
	if sendRes.Code != SuccCode {
		return fmt.Errorf("send sms fail. code: %d, data: %s", sendRes.Code, reqRes.Body)
	}
	return nil
}

func (c *Client) doRequest(api string, data string) (*RequestRes, error) {
	reqUrl := fmt.Sprintf("%s%s", c.host, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	fmt.Println(reqUrl)

	header := make(map[string]string)
	header["Content-Type"] = "application/json"
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		return result, fmt.Errorf("request sms service fail.url:%s err:%v", reqUrl, err)
	}
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode != 200 {
		return result, fmt.Errorf("request sms service http code != 200.url:%s http_code:%d, body:%s",
			reqUrl, resp.StatusCode, result.Body)
	}

	return result, nil
}

func (c *Client) init() error {
	if c == nil || c.host == "" || c.conf == nil {
		return fmt.Errorf("not init")
	}

	return nil
}
