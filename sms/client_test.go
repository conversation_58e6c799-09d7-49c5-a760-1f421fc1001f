package sms

import (
	"fmt"
	"testing"
)

func TestClient_SendSmsByUid(t *testing.T) {
	conf := &Conf{
		SmsSign:  "百度",
		Template: "sms-tmpl-uCJqpr28113",
		Tpl:      "xchain",
		SmsType:  SendTypeNormal,
	}
	cli := NewClient("http://passport-msg.baidu-int.com", conf)
	err := cli.SendSmsByUid(*********, map[string]interface{}{"code": "123456", "content": "1"})
	fmt.Println(err)
}

func TestClient_SendSmsByPhone(t *testing.T) {
	conf := &Conf{
		SmsSign:  "百度",
		Template: "sms-tmpl-uCJqpr28113",
		Tpl:      "xchain",
		SmsType:  SendTypeNormal,
	}
	cli := NewClient("http://passport-msg.baidu-int.com", conf)
	err := cli.SendSmsByPhone("15321298064", map[string]interface{}{"code": "123456", "content": "1"})
	fmt.Println(err)
}
