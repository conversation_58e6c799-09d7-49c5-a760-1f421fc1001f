package sms

import "net/http"

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000
)

const (
	SuccCode         = 1000
	UrlSendSmsByPass = "/api/sms/sapi/smssend"
	SendModeUid      = 0        // 通过uid发送短信
	SendModeUsername = 1        // 通过用户名发送短信
	SendModePhone    = 2        // 通过手机号发送短信
	SendTypeNormal   = "normal" // 普通短信
	SendTypeSale     = "sales"  // 营销短信
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type SendSmsResult struct {
	Code      int         `json:"code"`
	RequestId string      `json:"requestId"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data"`
}
