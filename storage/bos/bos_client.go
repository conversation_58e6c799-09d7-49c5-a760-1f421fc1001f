package bos

import (
	"fmt"
	"time"

	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/baidubce/bce-sdk-go/services/sts"
)

type BceBosClient struct {
	Client *bos.Client
	Bucket string
}

// endpoind可选
// 北京区域：https://bj.bcebos.com
// 广州区域：https://gz.bcebos.com
// 苏州区域：https://su.bcebos.com
func NewClient(endpoint, ak, sk, bucket string) (*BceBosClient, error) {
	clientConfig := bos.BosClientConfiguration{
		Ak:               ak,
		Sk:               sk,
		Endpoint:         endpoint,
		RedirectDisabled: false,
	}

	// 初始化一个BosClient
	bosClient, err := bos.NewClientWithConfig(&clientConfig)
	if err != nil {
		return nil, err
	}
	return &BceBosClient{
		Client: bosClient,
		Bucket: bucket,
	}, nil
}

/**
* 从本地文件上传到bos，不允许覆盖已有文件
* @objectName 存储到bos的文件路径，如值为asset/test.png，最终在bos中存储的路径为bucket/asset/test.png
* @sourcePath 需要上传的本地文件路径
 */
func (c *BceBosClient) UploadFromFile(objectName, sourcePath string) error {
	// 判断文件是否存在
	var err error
	_, err = c.Client.GetObjectMeta(c.Bucket, objectName)
	if err == nil {
		return fmt.Errorf("put object %s has exist", objectName)
	}
	retryCount := 0
	maxRetryTimes := 3

	for {
		if retryCount < maxRetryTimes {
			_, err = c.Client.PutObjectFromFile(c.Bucket, objectName, sourcePath, nil)
			if err == nil {
				return nil
			}
		} else {
			break
		}
		retryCount++
		time.Sleep(1 * time.Second)
	}
	return fmt.Errorf("put object %s from file failed: %v", objectName, err)
}

/**
* 从字节数组上传到bos
* @objectName 存储到bos的文件路径，如值为asset/test.png，最终在bos中存储的路径为bucket/asset/test.png
* @content 上传内容的字节数组
 */
func (c *BceBosClient) UploadFromBytes(objectName string, content []byte) error {
	// 判断文件是否存在
	var err error
	_, err = c.Client.GetObjectMeta(c.Bucket, objectName)
	if err == nil {
		return fmt.Errorf("put object %s has exist", objectName)
	}
	retryCount := 0
	maxRetryTimes := 3
	for {
		if retryCount < maxRetryTimes {
			_, err = c.Client.PutObjectFromBytes(c.Bucket, objectName, content, nil)
			if err == nil {
				return nil
			}
		} else {
			break
		}
		retryCount++
		time.Sleep(1 * time.Second)
	}
	return fmt.Errorf("put object %s from bytes failed: %v", objectName, err)
}

/**
* 获取指定路径下可写权限的临时访问token
* @objectPath bos的指定路径，不包含bucket
* @expireInSeconds 临时访问token有效期
 */
func (c *BceBosClient) GetSessionTokenWithWrite(objectPath string,
	expireInSeconds int) (*AccessSessionToken, error) {

	stsClient, err := sts.NewClient(c.Client.GetBceClientConfig().Credentials.AccessKeyId,
		c.Client.GetBceClientConfig().Credentials.SecretAccessKey)
	if err != nil {
		return nil, err
	}

	aclString := fmt.Sprintf(`{
    	"accessControlList":[
        	{
				"service":"bce:bos",
				"region":"%s",
				"effect": "Allow",
            	"resource": ["%s/%s/*"],
            	"permission": ["WRITE"]
        	}
    	]
	}`, c.Client.GetBceClientConfig().Region, c.Bucket, objectPath)
	sts, err := stsClient.GetSessionToken(expireInSeconds, aclString)
	if err != nil {
		return nil, err
	}

	return &AccessSessionToken{
		Bucket:          c.Bucket,
		ObjectPath:      fmt.Sprintf("%s/", objectPath),
		AccessKeyId:     sts.AccessKeyId,
		SecretAccessKey: sts.SecretAccessKey,
		SessionToken:    sts.SessionToken,
		Endpoint:        c.Client.GetBceClientConfig().Endpoint,
		CreateTime:      sts.CreateTime,
		Expiration:      sts.Expiration,
	}, nil
}
