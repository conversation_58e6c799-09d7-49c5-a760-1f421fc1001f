package bos

type AccessSessionToken struct {
	Bucket          string `json:"bucket"`
	ObjectPath      string `json:"object_path"`
	AccessKeyId     string `json:"access_key_id"`
	SecretAccessKey string `json:"secret_access_key"`
	SessionToken    string `json:"session_token"`
	Endpoint        string `json:"endpoint"`
	CreateTime      string `json:"create_time"`
	Expiration      string `json:"expiration"`
}
