package bos

import (
	"fmt"
	"path"
	"strings"

	"github.com/baidubce/bce-sdk-go/services/bos"
)

var supportImageType = []string{"jpg", "jpeg", "png", "bmp", "webp", "heic", "gif"}

type BceBosDlink struct {
	Client *bos.Client
	Bucket string
}

func NewBosDlink(endpoind, ak, sk, bucket string) (*BceBosDlink, error) {
	clientConfig := bos.BosClientConfiguration{
		Ak:               ak,
		Sk:               sk,
		Endpoint:         endpoind,
		RedirectDisabled: false,
	}

	// 初始化一个BosClient
	bosClient, err := bos.NewClientWithConfig(&clientConfig)
	if err != nil {
		return nil, err
	}
	return &BceBosDlink{
		Client: bosClient,
		Bucket: bucket,
	}, nil
}

/**
* 获取文件的下载地址，图片可直接访问
* @objectName bos上的文件
* @expireInSeconds 过期时间，-1表示永不过期
 */
func (c *BceBosDlink) GetFileUrl(objectName string, expireInSeconds int) string {
	url := c.Client.BasicGeneratePresignedUrl(c.Bucket, objectName, expireInSeconds)
	return url
}

/**
 * 根据bucket object生成同比例图片的url 不用为了判断文件是否存在发一次请求
 */
func (c *BceBosDlink) GetThumbOriUrl(objectName string, expireInSeconds int) string {
	url := c.Client.BasicGeneratePresignedUrl(c.Bucket, objectName, expireInSeconds)
	return url
}

/**
* 获取图片的缩略图
* @objectName bos上的文件，仅支持图片
* @expireInSeconds 过期时间，-1表示永不过期
* @width 按照最大宽度等比例缩放，单位px，如最大宽度大于原图宽度，返回换图大小
 */
func (c *BceBosDlink) GetThumbFileUrl(objectName string, expireInSeconds, width int) (string, error) {
	hasSuffix := false
	for _, imageSuffix := range supportImageType {
		ext := path.Ext(objectName)
		if strings.HasSuffix(strings.ToLower(ext), imageSuffix) {
			hasSuffix = true
			break
		}
	}
	if !hasSuffix {
		return "", fmt.Errorf("invalid image format")
	}
	params := map[string]string{
		"x-bce-process": fmt.Sprintf("image/resize,m_lfit,w_%d,limit_1", width),
	}
	url := c.Client.GeneratePresignedUrl(c.Bucket, objectName, expireInSeconds, "", nil, params)
	return url, nil
}
