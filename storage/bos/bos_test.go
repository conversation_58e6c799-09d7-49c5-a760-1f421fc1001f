package bos

import (
	"io/ioutil"
	"testing"

	"github.com/baidubce/bce-sdk-go/auth"
	"github.com/baidubce/bce-sdk-go/services/bos"
)

var bosClient *BceBosClient
var bosDlink *BceBosDlink

func init() {
	ak := "xxx"
	sk := "xxx"
	bosClient, _ = NewClient("https://bj.bcebos.com", ak, sk, "xasset-trade")
	bosDlink, _ = NewBosDlink("https://cdn.bcebos.com", ak, sk, "xasset-trade")
}

func TestBceBosClient_UploadFile(t *testing.T) {
	err := bosClient.UploadFromFile("asset/mock_01.txt", "./mock.txt")
	if err != nil {
		t.Fatalf("%v", err)
	}
}

func TestBceBosClient_UploadFromBytes(t *testing.T) {
	contents, _ := ioutil.ReadFile("./mock.txt")
	err := bosClient.UploadFromBytes("asset/mock_02.txt", contents)
	if err != nil {
		t.Fatalf("%v", err)
	}
}

func TestBceBosClient_GetFileUrl(t *testing.T) {
	url := bosDlink.GetFileUrl("asset/mock_01.txt", -1)
	t.Log(url)
}

func TestBceBosClient_GetThumbOriUrl(t *testing.T) {
	url := bosDlink.GetThumbOriUrl("asset/test.webp", -1)
	t.Log(url)
}

func TestBceBosClient_GetThumbFileUrl(t *testing.T) {
	url, err := bosDlink.GetThumbFileUrl("asset/test.webp", -1, 200)
	if err != nil {
		t.Fatalf("%v", err)
	}
	t.Log(url)
}

func TestBceBosClient_GetSessionTokenWithWrite(t *testing.T) {
	sts, err := bosClient.GetSessionTokenWithWrite("asset/1001", 30)
	if err != nil {
		t.Fatalf("%v", err)
	}
	// 使用申请的临时STS创建BOS服务的Client对象，Endpoint使用默认值
	bosClient, err := bos.NewClient(sts.AccessKeyId, sts.SecretAccessKey, "")
	if err != nil {
		t.Fatalf("%v", err)
		return
	}
	stsCredential, err := auth.NewSessionBceCredentials(
		sts.AccessKeyId,
		sts.SecretAccessKey,
		sts.SessionToken)
	if err != nil {
		t.Fatalf("%v", err)
		return
	}
	bosClient.Config.Credentials = stsCredential
	_, err = bosClient.PutObjectFromFile("xasset-trade", "asset/1001/mock.txt", "./mock.txt", nil)
	if err != nil {
		t.Fatalf("%v", err)
		return
	}
	_, err = bosClient.ListObjects("xasset-trade", nil)
	if err == nil {
		t.Fatalf("acl err")
		return
	}
	t.Logf("delete err %v", err)
}
