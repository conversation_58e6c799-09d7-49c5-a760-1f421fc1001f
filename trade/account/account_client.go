package account

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type AccountClient struct {
	addr *addr.Addr
	dev  bool // 是否沙盒环境
}

func NewAccountClient(addr *addr.Addr, dev bool) *AccountClient {
	return &AccountClient{
		addr: addr,
		dev:  dev,
	}
}

func (t *AccountClient) ApplyAccount(param *ApplyAccountParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId
	v := url.Values{}
	v.Set("business", param.Business)
	v.Set("scope", fmt.Sprintf("%d", param.Scope))
	v.Set("description", param.Description)
	query := v.Encode()

	reqRes, err := t.doPut(AccountApiApplyAccount, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for apply account fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) OverviewAccount(param *OverviewAccountParam) (*OverviewAccountResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	reqRes, err := t.doGet(AccountApiOverviewAccount, "", header)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for overview account fail.err:%v", err)
	}
	var result OverviewAccountResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) PkgOptions(accountId string) (*PkgOptionResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = accountId

	reqRes, err := t.doGet(AccountApiPkgOptions, "", header)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for pkg options fail.err:%v", err)
	}
	var result PkgOptionResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) SubmitOrder(param *SubmitOrderParam) (*SubmitOrderResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	v := url.Values{}
	v.Set("product_type", param.ProductType)
	v.Set("submit_type", param.SubmitType)
	query := v.Encode()

	body, err := json.Marshal(param.SubmitOrderBody)
	if err != nil {
		return nil, nil, err
	}

	reqRes, err := t.doPost(AccountApiSubmitOrder, query, header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for submit order fail.err:%v", err)
	}
	var result SubmitOrderResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) CloseService(param *CloseServiceParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	reqRes, err := t.doPost(AccountApiCloseService, "", header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for close service fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) ReopenService(param *ReopenServiceParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	reqRes, err := t.doPost(AccountApiReopenService, "", header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for reopen service fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) OrderDetail(param *OrderDetailParam) (*OrderDetailResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	v := url.Values{}
	v.Set("uuid", param.OrderId)
	query := v.Encode()

	reqRes, err := t.doGet(AccountApiOrderDetail, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for order detail fail.err:%v", err)
	}
	var result OrderDetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) PreviewAccount(param *PreviewAccountParam) (*PreviewAccountResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("ak", param.AK)
	v.Set("account_id", param.AccountId)
	v.Set("need_mnemonic", strconv.FormatBool(param.NeedMnemonic))
	query := v.Encode()

	reqRes, err := t.doGet(AccountApiPreviewAccount, query, make(map[string]string))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for preview account fail.err:%v", err)
	}
	var result PreviewAccountResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) SyncAccount(param *SyncAccountParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	body, err := json.Marshal(param)
	if err != nil {
		return nil, nil, err
	}

	reqRes, err := t.doPut(AccountApiSyncAccount, "", header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for syncAccount fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) ListAccount(param *ListAccountsParam) (*ListAccountsResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("page_no", strconv.Itoa(param.PageNo))
	v.Set("page_size", strconv.Itoa(param.PageSize))
	v.Set("account_type", strconv.Itoa(param.AccountType))
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("status", strconv.Itoa(param.Status))
	v.Set("non_stop", strconv.Itoa(param.NonStop))
	v.Set("ak", param.AK)
	v.Set("account_id", param.AccountId)
	v.Set("ban", strconv.Itoa(param.Ban))
	v.Set("pause", strconv.Itoa(param.Pause))
	v.Set("auto_pause", strconv.Itoa(param.AutoPause))
	v.Set("business", param.Business)
	v.Set("pgc_status", strconv.Itoa(param.PgcStatus))
	v.Set("need_total_count", strconv.FormatBool(param.NeedTotalCount))
	query := v.Encode()

	reqRes, err := t.doGet(AccountApiListAccount, query, make(map[string]string))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for list account fail.err:%v", err)
	}
	var result ListAccountsResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) QueryCompanyFile(param *QueryCompanyFileParam) (*QueryCompanyFileResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || len(param.AccountId) == 0 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("account_id", param.AccountId)
	query := v.Encode()

	reqRes, err := t.doGet(AccountApiCompanyFile, query, make(map[string]string))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for query company file fail.err:%v", err)
	}
	var result QueryCompanyFileResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) OrderExecute(param *OrderExecuteParam) (*OrderExecuteResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	body, err := json.Marshal(param)
	if err != nil {
		return nil, nil, err
	}

	reqRes, err := t.doPost(AccountApiOrderExecute, "", header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for order execute fail.err:%v", err)
	}
	var result OrderExecuteResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) OrderCheck(param *OrderCheckParam) (*OrderCheckResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("orderId", param.OrderId)
	query := v.Encode()

	reqRes, err := t.doGet(AccountApiOrderCheck, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for order check fail.err:%v", err)
	}
	var result OrderCheckResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) ResourceQuery(param *ResourceQueryParam) (*ResourceQueryResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("region", param.Region)
	query := v.Encode()
	api := fmt.Sprintf(AccountApiResourceQuery, param.AccountId, param.Service, param.ResourceId)
	reqRes, err := t.doGet(api, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for resource query fail.err:%v", err)
	}
	var result ResourceQueryResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) ResourceAction(param *ResourceActionParam) (*ResourceActionResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("action", param.Action)
	query := v.Encode()

	body, err := json.Marshal(param.ResourceActionBody)
	if err != nil {
		return nil, nil, err
	}

	api := fmt.Sprintf(AccountApiResourceQuery, param.AccountId, param.Service, param.ResourceId)
	reqRes, err := t.doPut(api, query, header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for resource action fail.err:%v", err)
	}
	var result ResourceActionResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) ResourceDumpStatus(param *ResourceDumpStatusParam) (*ResourceDumpStatusResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	body, err := json.Marshal(param.ResourceDumpStatusBody)
	if err != nil {
		return nil, nil, err
	}

	api := fmt.Sprintf(AccountApiResourceDumpStatus, param.Service)
	reqRes, err := t.doPost(api, "", header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for resource dump status fail.err:%v", err)
	}
	var result ResourceDumpStatusResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) QueryVilgTask(param *QueryVilgTaskParam) (*QueryVilgTaskResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("task_id", strconv.FormatInt(param.TaskId, 10))
	query := v.Encode()

	reqRes, err := t.doGet(AccountApiVilgTask, query, make(map[string]string))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset account for vilg task fail.err:%v", err)
	}

	var result QueryVilgTaskResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal vilg task response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *AccountClient) getReqRWTimeout() time.Duration {
	if t.dev {
		return ReqRWTimeoutMsForDev
	}

	return ReqRWTimeoutMs
}

// post
func (t *AccountClient) doPost(api string, query string, header map[string]string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl, err := url.Parse(fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api))
	if err != nil {
		return nil, fmt.Errorf("parse url fail, err: %v", err)
	}
	if len(query) != 0 {
		reqUrl.RawQuery = query
	}
	result := &RequestRes{
		ReqUrl: reqUrl.String(),
	}
	header["Content-Type"] = "application/json;charset=utf-8"
	resp, err := httpclient.Post(reqUrl.String(), header, ReqConnTimeoutMs, t.getReqRWTimeout(), data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// get
func (t *AccountClient) doGet(api string, query string, header map[string]string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl, err := url.Parse(fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api))
	if err != nil {
		return nil, fmt.Errorf("parse url fail, err: %v", err)
	}
	if len(query) != 0 {
		reqUrl.RawQuery = query
	}
	result := &RequestRes{
		ReqUrl: reqUrl.String(),
	}
	resp, err := httpclient.Get(reqUrl.String(), header, ReqConnTimeoutMs, t.getReqRWTimeout(), nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// put
func (t *AccountClient) doPut(api string, query string, header map[string]string,
	data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl, err := url.Parse(fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api))
	if err != nil {
		return nil, fmt.Errorf("parse url fail, err: %v", err)
	}
	if len(query) != 0 {
		reqUrl.RawQuery = query
	}
	result := &RequestRes{
		ReqUrl: reqUrl.String(),
	}
	header["Content-Type"] = "application/json;charset=utf-8"
	resp, err := httpclient.Put(reqUrl.String(), header, ReqConnTimeoutMs, t.getReqRWTimeout(), data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *AccountClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
