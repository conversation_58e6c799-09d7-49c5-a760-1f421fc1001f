package account

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/testings"
)

var hosts string = "gzns-ps-www17-73986.gzns.baidu.com:8380"

func getAccountClient() *AccountClient {
	a, _ := addr.NewAddrByHost([]string{hosts})
	return NewAccountClient(a, true)
}

func TestApplyAccount(t *testing.T) {
	cli := getAccountClient()
	resp, reqInfo, err := cli.ApplyAccount(&ApplyAccountParam{
		AccountId:   "a79bc7163e844e12a1843b1581a3a80c",
		Business:    "testBusiness",
		Scope:       0,
		Description: "",
	})
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp, reqInfo)
}

func TestOverviewAccount(t *testing.T) {
	cli := getAccountClient()
	resp, _, err := cli.OverviewAccount(&OverviewAccountParam{
		AccountId: "d028fb5cc79142cdb3a580e068d3a06f",
	})
	if err != nil {
		t.Error(err)
	}
	fmt.Println(testings.ToJSON(resp))
}

func TestSubmitOrder(t *testing.T) {
	cli := getAccountClient()
	resp, _, err := cli.SubmitOrder(&SubmitOrderParam{
		SubmitType:  "saas-package",
		AccountId:   "d028fb5cc79142cdb3a580e068d3a06f",
		ProductType: "prepay",
		SubmitOrderBody: SubmitOrderBody{
			Items: []OrderItem{
				{
					Flavor: "10CB",
					Count:  1,
				},
			},
		},
	})
	if err != nil {
		t.Error(err)
	}
	fmt.Println(testings.ToJSON(resp))
}

func TestCloseService(t *testing.T) {
	cli := getAccountClient()
	resp, reqInfo, err := cli.CloseService(&CloseServiceParam{
		AccountId: "b785ff69f83c408fba228dfd562ea961",
	})
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp, reqInfo)
}

func TestReopenService(t *testing.T) {
	cli := getAccountClient()
	resp, reqInfo, err := cli.ReopenService(&ReopenServiceParam{
		AccountId: "b785ff69f83c408fba228dfd562ea961",
	})
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp, reqInfo)
}

func TestPreviewAccount(t *testing.T) {
	cli := getAccountClient()
	resp, _, err := cli.PreviewAccount(&PreviewAccountParam{
		AK:           "",
		AppId:        100106,
		AccountId:    "",
		NeedMnemonic: true,
	})
	if err != nil {
		t.Error(err)
	}
	fmt.Println(testings.ToJSON(resp))
}

func TestPushTest(t *testing.T) {
	cli := getAccountClient()
	resp, reqInfo, err := cli.SyncAccount(&SyncAccountParam{
		EncryptedAcc: nil,
		Nonce:        "",
	})
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp, reqInfo)
}

func TestOrderExecute(t *testing.T) {
	cli := getAccountClient()
	resp, reqInfo, err := cli.OrderExecute(&OrderExecuteParam{
		OrderId: "123",
	})
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp, reqInfo)
}

func TestOrderCheck(t *testing.T) {
	cli := getAccountClient()
	resp, reqInfo, err := cli.OrderCheck(&OrderCheckParam{
		OrderId: "123",
	})
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp, reqInfo)
}

func TestResourceQuery(t *testing.T) {
	cli := getAccountClient()
	resp, reqInfo, err := cli.ResourceQuery(&ResourceQueryParam{
		Region:     "global",
		AccountId:  "b785ff69f83c408fba228dfd562ea961",
		Service:    "postpay",
		ResourceId: "123",
	})
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp, reqInfo)
}

func TestResourceAction(t *testing.T) {
	cli := getAccountClient()
	resp, reqInfo, err := cli.ResourceAction(&ResourceActionParam{
		Action:     "START",
		AccountId:  "b785ff69f83c408fba228dfd562ea961",
		Service:    "postpay",
		ResourceId: "123",
	})
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp, reqInfo)
}

func TestResourceDumpStatus(t *testing.T) {
	cli := getAccountClient()
	resp, reqInfo, err := cli.ResourceDumpStatus(&ResourceDumpStatusParam{
		Service: "postpay",
	})
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp, reqInfo)
}
