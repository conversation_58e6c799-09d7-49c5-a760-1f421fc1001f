package account

import (
	"encoding/json"
	"net/http"

	"icode.baidu.com/baidu/blockchain/xasset-golib/billing/orderfacade/model"
	"icode.baidu.com/baidu/blockchain/xasset-golib/ernie/vilg"
)

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000

	// account 模块沙盒环境 api 处理时间较长，3s 不够
	ReqRWTimeoutMsForDev = 6000
)

const (
	BCE_Account_Header = "X-Bce-Account"
	User_Id_Header     = "X-User-Id"
)

const (
	AccountApiApplyAccount    = "/internal/account/v1/frontend/account/apply"
	AccountApiOverviewAccount = "/internal/account/v1/frontend/account/overview"
	AccountApiPkgOptions      = "/internal/account/v1/frontend/order/pkgoptions"
	AccountApiSubmitOrder     = "/internal/account/v1/frontend/order/submit"
	AccountApiOrderDetail     = "/internal/account/v1/frontend/order/detail"
	AccountApiCloseService    = "/internal/account/v1/frontend/service/close"
	AccountApiReopenService   = "/internal/account/v1/frontend/service/reopen"

	AccountApiPreviewAccount = "/internal/account/v1/backend/account/preview"
	AccountApiSyncAccount    = "/internal/account/v1/backend/account/sync"
	AccountApiListAccount    = "/internal/account/v1/backend/account/list"
	AccountApiCompanyFile    = "/internal/account/v1/backend/account/companyfile"

	AccountApiOrderExecute = "/internal/account/v1/order_executor/execute"
	AccountApiOrderCheck   = "/internal/account/v1/order_executor/check"

	AccountApiResourceQuery      = "/internal/account/v1/billing/%s/%s/resources/%s"
	AccountApiResourceAction     = "/internal/account/v1/billing/%s/%s/resources/%s"
	AccountApiResourceDumpStatus = "/internal/account/v1/billing/%s/resources/dump-status"

	AccountApiVilgTask = "/internal/account/v1/vilg/task"
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

type ApplyAccountParam struct {
	AccountId   string
	Business    string
	Scope       int
	Description string
	UserId      string
}

func (a *ApplyAccountParam) Valid() bool {
	return len(a.AccountId) > 0 && len(a.Business) > 0 && a.Scope >= 0 &&
		len(a.Description) > 0
}

type OverviewAccountParam struct {
	AccountId string
	UserId    string
}

func (a *OverviewAccountParam) Valid() bool {
	return len(a.AccountId) > 0
}

type AccountOverview struct {
	AccountId   string `json:"account_id"`
	RealName    string `json:"realname"` // 实名认证信息，为空表示尚未实名认证
	Approved    bool   `json:"approved"` // 准入状态
	Business    string `json:"business"`
	AppId       uint64 `json:"app_id"`
	AK          string `json:"ak"`
	SK          string `json:"sk"`
	Pause       bool   `json:"pause"`         // 是否暂停自动扣费
	AutoPause   bool   `json:"auto_pause"`    // 是否自动暂停
	Status      string `json:"status"`        // saas服务状态 INIT|RUNNING|STOPPED。其中INIT允许直接开通
	PgcStatus   string `json:"pgc_status"`    // pgc服务状态  INIT|RUNNING|STOPPED。INIT 表示未开通
	PgcExpireAt uint64 `json:"pgc_expire_at"` // 服务过期时间
	NonStop     bool   `json:"non_stop"`      // 是否欠费不停服
	Package     uint64 `json:"package"`       // 量包余量
	WhiteList   bool   `json:"white_list"`
	Ctime       uint64 `json:"ctime"`
}

type OverviewAccountResp struct {
	BaseResp
	Account AccountOverview `json:"account"`
}

type PkgOption struct {
	Amount    int64  // 量包规格
	Price     int64  // 价格
	OrderSpec string // 订单标识（用于下单）
	ActSpec   int64  // 活动类型 0:常规价 1:活动价 2:新客专享
}

type PkgOptionResp struct {
	BaseResp
	Options []PkgOption `json:"options"`
}

type OrderItem struct {
	Flavor        string               `json:"flavor"` // 规格
	Count         int                  `json:"count"`  // 数量
	PaymentMethod []model.PaymentModel `json:"paymentMethod"`
}

type SubmitOrderBody struct {
	Items         []OrderItem          `json:"items"`
	Coupon        []string             `json:"coupon"`
	PaymentMethod []model.PaymentModel `json:"paymentMethod"`
}

type SubmitOrderParam struct {
	SubmitType  string // 下单购买的商品类型
	AccountId   string
	ProductType string
	PricePolicy string // 已废弃
	UserId      string
	SubmitOrderBody
}

func (s *SubmitOrderParam) Valid() bool {
	return len(s.AccountId) > 0
}

type SubmitOrderResp struct {
	BaseResp
	OrderId string `json:"order_id"`
}

type CloseServiceParam struct {
	AccountId string
	UserId    string
}

func (c *CloseServiceParam) Valid() bool {
	return len(c.AccountId) > 0
}

type ReopenServiceParam = CloseServiceParam

// PreviewAccountParam (AppId、AK、AccountId 三选一，用于定位账户)
type PreviewAccountParam struct {
	AppId        int
	AK           string
	AccountId    string
	NeedMnemonic bool
}

func (a *PreviewAccountParam) Valid() bool {
	return len(a.AK) > 0 || a.AppId > 0 || len(a.AccountId) > 0
}

type AccountPreview struct {
	AppId       uint64 `json:"app_id"`        // 登记平台的用户标识
	AccountType int    `json:"account_type"`  // 账户类型
	AccountId   string `json:"account_id"`    // 百度云账户的唯一标识
	Status      int    `json:"status"`        // 费用状态 0:INIT 1:RUNNING 2:STOPPED
	NonStop     int    `json:"non_stop"`      // 是否欠费不停服
	Pause       int    `json:"pause"`         // 是否手动暂停 0:FALSE 1:TRUE
	Ban         int    `json:"ban"`           // 是否被封禁 0:FALSE 1:TRUE
	AK          string `json:"ak"`            // 登记平台的用户ak
	SK          string `json:"sk"`            // 登记平台的用户sk
	AuthGroup   string `json:"auth_group"`    // 权限组
	BanApis     string `json:"ban_apis"`      // 被封禁的api
	Business    string `json:"business"`      // 业务说明,公司名称
	ShowPrice   int    `json:"show_price"`    // 是否显示价格 0/1
	PgcStatus   int    `json:"pgc_status"`    // pgc服务状态 INIT|RUNNING|STOPPED。INIT 表示未开通
	PgcExpireAt uint64 `json:"pgc_expire_at"` // pgc服务过期时间
	PgcAddr     string `json:"pgc_addr"`      // pgc链上账户地址
	PgcMnemonic string `json:"pgc_mnemonic"`  // pgc链上账户
	Ctime       uint64 `json:"ctime"`         // 创建时间
}

type PreviewAccountResp struct {
	BaseResp
	Account AccountPreview `json:"account"`
}

type ListAccountsParam struct {
	PageNo         int
	PageSize       int
	AccountType    int
	AppId          int64
	Status         int
	NonStop        int
	AK             string
	AccountId      string
	Ban            int
	Pause          int
	AutoPause      int
	Business       string
	PgcStatus      int
	NeedTotalCount bool
}

type ListAccountsResp struct {
	BaseResp
	Accounts []AccountTabNode `json:"accounts"`
	Total    int64            `json:"total"`
}

type AccountTabNode struct {
	Id          uint64 `json:"id"`           // 自增id
	AppId       uint64 `json:"app_id"`       // 登记平台的用户标识
	AccountType int    `json:"account_type"` // 账户类型
	AccountId   string `json:"account_id"`   // 百度云账户的唯一标识
	Status      int    `json:"status"`       // 费用状态 0:INIT 1:RUNNING 2:STOPPED
	NonStop     int    `json:"non_stop"`     // 是否欠费不停服 0:FALSE 1:TRUE
	Pause       int    `json:"pause"`        // 是否手动暂停 0:FALSE 1:TRUE
	AutoPause   int    `json:"auto_pause"`   // 是否开启自动暂停 0:FALSE 1:TRUE
	Ban         int    `json:"ban"`          // 是否被封禁 0:FALSE 1:TRUE
	AK          string `json:"ak"`           // 登记平台的用户ak
	SK          string `json:"sk"`           // 登记平台的用户sk
	AuthGroup   string `json:"auth_group"`   // 权限组
	BanApis     string `json:"ban_apis"`     // 被封禁的api
	Business    string `json:"business"`     // 业务说明,公司名称
	Scope       int    `json:"scope"`        // 经营范围
	Description string `json:"description"`  // 应用描述，业务场景
	Contacts    string `json:"contacts"`     // 业务联系方式
	ShowPrice   int    `json:"show_price"`   // 是否显示价格 0/1
	Ctime       uint64 `json:"ctime"`        // 创建时间
}

func (p *AccountTabNode) CreateValid() bool {
	accountValid := p.AccountType >= 0 && len(p.AK) != 0 &&
		len(p.SK) != 0 && len(p.Business) != 0
	return accountValid && p.AppId >= 1
}

type SyncAccountParam struct {
	EncryptedAcc []byte
	Nonce        string
}

func (p *SyncAccountParam) Valid() bool {
	return len(p.EncryptedAcc) != 0 && len(p.Nonce) != 0
}

type CompanyFile struct {
	AccountId    string `json:"accountId"`
	File         string `json:"file"`
	Suffix       string `json:"suffix"`
	MaterialType string `json:"materialType"`
	Type         string `json:"type"`
	Name         string `json:"name"`
	Number       string `json:"number"`
}
type QueryCompanyFileParam struct {
	AccountId string
}

type QueryCompanyFileResp struct {
	BaseResp
	File CompanyFile `json:"file"`
}

type OrderExecuteParam struct {
	OrderId string `json:"orderId"`
}

func (p *OrderExecuteParam) Valid() bool {
	return len(p.OrderId) != 0
}

type OrderExecuteResp struct {
	BaseResp
	ExecutionStatus string `json:"executionStatus"`
}

type OrderCheckParam = OrderExecuteParam

type OrderCheckResp = OrderExecuteResp

type ResourceQueryParam struct {
	Region     string
	AccountId  string
	Service    string
	ResourceId string
}

func (p *ResourceQueryParam) Valid() bool {
	return len(p.Region) != 0 && len(p.AccountId) != 0 && len(p.Service) != 0 && len(p.ResourceId) != 0
}

type ResourceQueryResp struct {
	BaseResp
	Id        string `json:"id"`
	Region    string `json:"region"`
	AccountId string `json:"accountId"`
	Service   string `json:"service"`
	Name      string `json:"name"`
	Status    string `json:"status"` // 只可以是 RUNNING|STOPPED|DESTROYED|CLEAR 之一
}

type ResourceActionBody struct {
	Region         string   `json:"region"`
	Status         string   `json:"status"`         // 当前状态
	ProductType    string   `json:"productType"`    // PREPAY|POSTPAY|POSTPAY_ITEM： 预付费资源|后付费资源|预付费资源的后付费计费项
	Reason         string   `json:"reason"`         // OVERDUE|RECHARGE|FORCE_STOP|FORCE_START|BID_FAILED|EXPIRED|REFUND|RESOURCE_REFUND_IMMEDIATELY|RELEASE|DESTROYED|RENEW
	ChargeItemList []string `json:"chargeItemList"` // 需要启停的计费项
}

type ResourceActionParam struct {
	Action     string `json:"action"` // START|STOP|DELETE
	AccountId  string `json:"accountId"`
	Service    string `json:"service"`
	ResourceId string `json:"resourceId"`
	ResourceActionBody
}

func (p *ResourceActionParam) Valid() bool {
	return len(p.Action) != 0 && len(p.AccountId) != 0 && len(p.Service) != 0 && len(p.ResourceId) != 0
}

type ResourceActionResp struct {
	BaseResp
	Status string `json:"status"`
}

type ResourceDumpStatusBody struct {
	AccountId string `json:"accountId"`
	Region    string `json:"region"`
	Status    string `json:"status"` // RUNNING|STOPPED|DESTROYED|CLEAR
	PageNo    uint64 `json:"pageNo"` // 从 1 开始
	PageSize  uint64 `json:"pageSize"`
}

type ResourceDumpStatusParam struct {
	Service string `json:"service"`

	ResourceDumpStatusBody
}

func (p *ResourceDumpStatusParam) Valid() bool {
	return len(p.Service) != 0
}

type ResourceItem struct {
	ResourceId string `json:"resourceId"`
	Region     string `json:"region"`
	Status     string `json:"status"` // RUNNING|STOPPED|DESTROYED|CLEAR
	AccountId  string `json:"accountId"`
}

type ResourceDumpStatusResp struct {
	BaseResp
	TotalCount uint64         `json:"totalCount"`
	PageNo     uint64         `json:"pageNo"`
	PageSize   uint64         `json:"pageSize"`
	Result     []ResourceItem `json:"result"`
}

type OrderDetailParam struct {
	AccountId string `json:"account_id"`
	OrderId   string `json:"order_id"`
}

func (p *OrderDetailParam) Valid() bool {
	return len(p.AccountId) != 0 && len(p.OrderId) != 0
}

type OrderDetailResp struct {
	BaseResp
	Result json.RawMessage `json:"result"`
}

type QueryVilgTaskParam struct {
	TaskId int64 `json:"task_id"`
}

func (p *QueryVilgTaskParam) Valid() bool {
	return p.TaskId >= 0
}

type VilgTask struct {
	TaskId     int64            `json:"task_id"`
	AppId      int64            `json:"app_id"`
	Extend     string           `json:"extend"`
	Text       string           `json:"text"`
	Style      int64            `json:"style"`
	Resolution int64            `json:"resolution"`
	Num        int64            `json:"num"`
	Result     *vilg.GetImgResp `json:"result"`
	Ctime      int64            `json:"ctime"`
	Mtime      int64            `json:"mtime"` // 任务完成的时间，且不会被更新
}

type QueryVilgTaskResp struct {
	BaseResp
	Task *VilgTask `json:"task"`
}
