package activity

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type ActivityClient struct {
	addr *addr.Addr
}

func NewActivityClient(addr *addr.Addr) *ActivityClient {
	return &ActivityClient{addr}
}

func (t *ActivityClient) GetBannerList() (*GetBannerListResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	reqRes, err := t.doRequest(ActivityApiGetBannerList, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for get banner list fail.err:%v", err)
	}
	var result GetBannerListResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) GetVerifyList() (*GetVerifyListResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	reqRes, err := t.doRequest(ActivityApiGetVerifyList, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for get verify list fail.err:%v", err)
	}
	var result GetVerifyListResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) GetActList(param *ListActParam) (*GetActListResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("cursor", param.Cursor)
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}
	v.Set("type", fmt.Sprintf("%d", param.Type))
	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiGetActList, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for get act list fail.err:%v", err)
	}
	var result GetActListResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) GetLabList(param *ListLabParam) (*GetActComposeListResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("cursor", param.Cursor)
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiGetLabList, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for get lab list fail.err:%v", err)
	}
	var result GetActComposeListResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) GetActDetail(actId int) (*GetActDetailResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", actId))
	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiGetActDetail, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for get act detail fail.err:%v", err)
	}
	var result GetActDetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// GetActByAsset 通过资产id获取活动信息，需要注意该接口使用规则
func (t *ActivityClient) GetActByAsset(assetId int64) (*GetActByAssetResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetGetActByAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for get act info by asset fail.err:%v", err)
	}
	var result GetActByAssetResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) GetAssetDetail(actId int, assetId int64) (*GetAssetDetailResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	v.Set("act_id", fmt.Sprintf("%d", actId))
	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiGetAssetDetail, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for get asset detail fail.err:%v", err)
	}
	var result GetAssetDetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraCreateAct(param *CreateOrUpdateActParam) (*CreateOrUpdateActResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.CreateValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("owner_type", fmt.Sprintf("%d", param.OwnerType))
	v.Set("link_type", fmt.Sprintf("%d", param.LinkType))
	v.Set("jump_link", param.JumpLink)
	v.Set("position", fmt.Sprintf("%d", param.Position))
	v.Set("issuer", param.Issuer)
	v.Set("act_name", param.ActName)
	v.Set("short_desc", param.ShortDesc)
	v.Set("thumb", param.Thumb)
	v.Set("img_desc", param.ImgDesc)
	v.Set("start", fmt.Sprintf("%d", param.Start))
	v.Set("end", fmt.Sprintf("%d", param.End))
	v.Set("act_style", fmt.Sprintf("%d", param.ActStyle))
	v.Set("bookable", fmt.Sprintf("%d", param.Bookable))
	v.Set("weight", fmt.Sprintf("%d", param.Weight))
	v.Set("ext_info", param.ExtInfo)

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetCreateAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for create act detail fail.err:%v", err)
	}
	var result CreateOrUpdateActResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraAlterAct(param *CreateOrUpdateActParam) (*CreateOrUpdateActResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.ModifyValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("owner_type", fmt.Sprintf("%d", param.OwnerType))
	v.Set("link_type", fmt.Sprintf("%d", param.LinkType))
	v.Set("jump_link", param.JumpLink)
	v.Set("position", fmt.Sprintf("%d", param.Position))
	v.Set("issuer", param.Issuer)
	v.Set("act_name", param.ActName)
	v.Set("short_desc", param.ShortDesc)
	v.Set("thumb", param.Thumb)
	v.Set("img_desc", param.ImgDesc)
	v.Set("start", fmt.Sprintf("%d", param.Start))
	v.Set("end", fmt.Sprintf("%d", param.End))
	v.Set("act_style", fmt.Sprintf("%d", param.ActStyle))
	v.Set("bookable", fmt.Sprintf("%d", param.Bookable))
	v.Set("weight", fmt.Sprintf("%d", param.Weight))
	v.Set("ext_info", param.ExtInfo)

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetAlterAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for alter act detail fail.err:%v", err)
	}
	var result CreateOrUpdateActResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraCreateOrAlterPubAct(param *CreateOrUpdateActParam) (*CreateOrUpdateActResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.CreateValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("jump_link", param.JumpLink)
	v.Set("position", fmt.Sprintf("%d", param.Position))
	v.Set("issuer", param.Issuer)
	v.Set("act_name", param.ActName)
	v.Set("short_desc", param.ShortDesc)
	v.Set("thumb", param.Thumb)
	v.Set("img_desc", param.ImgDesc)
	v.Set("start", fmt.Sprintf("%d", param.Start))
	v.Set("end", fmt.Sprintf("%d", param.End))
	v.Set("act_style", fmt.Sprintf("%d", param.ActStyle))
	v.Set("bookable", fmt.Sprintf("%d", param.Bookable))
	v.Set("weight", fmt.Sprintf("%d", param.Weight))
	v.Set("ext_info", param.ExtInfo)

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetCreateOrAlterPubAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for create or alter pub act detail fail.err:%v", err)
	}
	var result CreateOrUpdateActResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraCreateAst(param *CreateOrUpdateAstParam) (*CreateOrUpdateAstResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.CreateValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("asset_cate", fmt.Sprintf("%d", param.AssetCate))
	v.Set("asset_info", param.AssetInfo)
	v.Set("start", fmt.Sprintf("%d", param.Start))
	v.Set("end", fmt.Sprintf("%d", param.End))
	v.Set("jump_link", param.JumpLink)
	v.Set("evi_cert", param.EviCert)
	v.Set("ext_info", param.ExtInfo)
	if param.GrantMode >= 0 {
		v.Set("grant_mode", fmt.Sprintf("%d", param.GrantMode))
	}
	if param.Price > 0 {
		v.Set("price", fmt.Sprintf("%d", param.Price))
	}
	if param.OriPrice > 0 {
		v.Set("ori_price", fmt.Sprintf("%d", param.OriPrice))
	}
	if param.Series > 0 {
		v.Set("series", fmt.Sprintf("%d", param.Series))
	}
	v.Set("amount", fmt.Sprintf("%d", param.Amount))
	v.Set("apply_form", fmt.Sprintf("%d", param.ApplyForm))

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetCreateAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for create ast detail fail.err:%v", err)
	}
	var result CreateOrUpdateAstResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraAlterAst(param *CreateOrUpdateAstParam) (*CreateOrUpdateAstResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.ModifyValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	if param.AssetCate >= 0 {
		v.Set("asset_cate", fmt.Sprintf("%d", param.AssetCate))
	}
	if param.Start > 0 {
		v.Set("start", fmt.Sprintf("%d", param.Start))
	}
	if param.End > 0 {
		v.Set("end", fmt.Sprintf("%d", param.End))
	}
	if param.GrantMode >= 0 {
		v.Set("grant_mode", fmt.Sprintf("%d", param.GrantMode))
	}
	if param.JumpLink != "" {
		v.Set("jump_link", param.JumpLink)
	}
	if param.EviCert != "" {
		v.Set("evi_cert", param.EviCert)
	}
	if param.ExtInfo != "" {
		v.Set("ext_info", param.ExtInfo)
	}
	if param.Price >= 0 {
		v.Set("price", fmt.Sprintf("%d", param.Price))
	}
	if param.OriPrice >= 0 {
		v.Set("ori_price", fmt.Sprintf("%d", param.OriPrice))
	}
	if param.Amount >= 0 {
		v.Set("amount", fmt.Sprintf("%d", param.Amount))
	}
	if param.ApplyForm >= 0 {
		v.Set("apply_form", fmt.Sprintf("%d", param.ApplyForm))
	}
	if param.Series > 0 {
		v.Set("series", fmt.Sprintf("%d", param.Series))
	}
	v.Set("asset_info", param.AssetInfo)
	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetAlterAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for create ast detail fail.err:%v", err)
	}
	var result CreateOrUpdateAstResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraCreateBoxAst(param *CreateOrUpdateBoxAssetParam) (*CreateOrUpdateAstResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("box_id", fmt.Sprintf("%d", param.BoxId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("asset_info", param.AssetInfo)
	v.Set("evi_cert", param.EviCert)
	v.Set("ext_info", param.ExtInfo)
	if param.Price > 0 {
		v.Set("price", fmt.Sprintf("%d", param.Price))
	}
	if param.OriPrice > 0 {
		v.Set("ori_price", fmt.Sprintf("%d", param.OriPrice))
	}
	v.Set("amount", fmt.Sprintf("%d", param.Amount))

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetCreateBoxAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for create ast detail fail.err:%v", err)
	}
	var result CreateOrUpdateAstResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraAlterBoxAst(param *CreateOrUpdateBoxAssetParam) (*CreateOrUpdateAstResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("box_id", fmt.Sprintf("%d", param.BoxId))
	if param.EviCert != "" {
		v.Set("evi_cert", param.EviCert)
	}
	if param.ExtInfo != "" {
		v.Set("ext_info", param.ExtInfo)
	}
	if param.Price >= 0 {
		v.Set("price", fmt.Sprintf("%d", param.Price))
	}
	if param.OriPrice >= 0 {
		v.Set("ori_price", fmt.Sprintf("%d", param.OriPrice))
	}
	if param.Amount >= 0 {
		v.Set("amount", fmt.Sprintf("%d", param.Amount))
	}
	v.Set("asset_info", param.AssetInfo)
	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetAlterBoxAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for create ast detail fail.err:%v", err)
	}
	var result CreateOrUpdateAstResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraRemoveAst(actId int, assetId int64) (*CreateOrUpdateAstResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if assetId < 0 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	v.Set("act_id", fmt.Sprintf("%d", actId))

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetRemoveAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for remove ast detail fail.err:%v", err)
	}
	var result CreateOrUpdateAstResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraCheckSplit(actId int, assetId int64) (*CheckSplitResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if assetId < 0 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	v.Set("act_id", fmt.Sprintf("%d", actId))

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetCheckSplit, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for check asset split status detail fail.err:%v", err)
	}
	var result CheckSplitResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraSearchActivitybyName(act_name string) (*ListAllActResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	if act_name != "" {
		v.Set("act_name", act_name)
	}

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetSearchActName, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for search act name fail.err:%v", err)
	}
	var result ListAllActResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraListAllAct(page, limit int) (*ListAllActResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("page", fmt.Sprintf("%d", page))
	if limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", limit))
	}

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetListAllAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for act list fail.err:%v", err)
	}
	var result ListAllActResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraListAstByAct(actId int) (*ListAstByActResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if actId < 0 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", actId))

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetListAstByAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for ast list fail.err:%v", err)
	}
	var result ListAstByActResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraGetRealAmount(actId, realAst int64) (*GetAmountResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if actId <= 0 || realAst <= 0 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", actId))
	v.Set("asset_id", fmt.Sprintf("%d", realAst))

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetGetAmount, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for get real amount fail.err:%v", err)
	}
	var result GetAmountResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraPublishAct(actId int, opType int) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if actId < 0 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", actId))
	v.Set("op_type", fmt.Sprintf("%d", opType))

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetPublishAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for publish act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraAddComposeAst(composeId, assetId int64, assetInfo string) (*UpdateComposeResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if composeId < 1 || assetId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("compose_id", fmt.Sprintf("%d", composeId))
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	v.Set("asset_info", assetInfo)

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetAddComposeAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for add compose asset fail.err:%v", err)
	}
	var result UpdateComposeResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraDelComposeAst(composeId, assetId int64) (*UpdateComposeResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if composeId < 1 || assetId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("compose_id", fmt.Sprintf("%d", composeId))
	v.Set("asset_id", fmt.Sprintf("%d", assetId))

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetDelComposeAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for del compose asset fail.err:%v", err)
	}
	var result UpdateComposeResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraAlterComposeStatus(actId, assetId int64, opType int) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if actId < 1 || assetId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", actId))
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	v.Set("op_type", fmt.Sprintf("%d", opType))

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetAlterCompose, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for alter compose status fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraRemoveAct(actId int) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if actId < 0 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", actId))

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetRemoveAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity for remove act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) AddDeliveryInfo(param *AddDeliveryParam) (*AddDeliveryResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("tel", param.Tel)
	v.Set("name", param.Name)
	v.Set("detail", param.Detail)
	v.Set("email", param.Email)
	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiAddDeliveryInfo, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity fail @ adddeliveryinfo, err:%v", err)
	}
	var result AddDeliveryResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail @ adddeliveryinfo, url: %s, http_code: %d, "+
			"resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) EditDeliveryInfo(param *EditDeliveryParam) (*EditDeliveryResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("delivery_id", fmt.Sprintf("%d", param.DeliveryId))
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("tel", param.Tel)
	v.Set("name", param.Name)
	v.Set("detail", param.Detail)
	v.Set("email", param.Email)
	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiEditDeliveryInfo, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity fail @ editdeliveryinfo, err:%v", err)
	}
	var result EditDeliveryResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail @ editdeliveryinfo, url: %s, http_code: %d, "+
			"resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) GetDeliveryInfo(param *GetDeliveryParam) (*GetDeliveryResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiGetDeliveryInfo, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity fail @ getdeliveryinfo, err:%v", err)
	}
	var result GetDeliveryResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail @ getdeliveryinfo, url: %s, http_code: %d, "+
			"resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) DeleteDeliveryInfo(param *DeleteDeliveryParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("delivery_id", fmt.Sprintf("%d", param.DeliveryId))
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiDeleteDeliveryInfo, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity fail @ deletedeliveryinfo, err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail @ deletedeliveryinfo, url: %s, http_code: %d, "+
			"resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// post
func (t *ActivityClient) doRequest(api string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *ActivityClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
