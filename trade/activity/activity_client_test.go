package activity

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
)

var (
	hosts     = "127.0.0.1:8260"
	actId     = 7573
	assetId   = int64(3100)
	composeId = int64(201)
)

func TestGetBannerList(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewActivityClient(a).GetBannerList()
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo, "banner count", len(resp.List))
}

func TestGetActList(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewActivityClient(a).GetActList("", 10)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo, "act count", len(resp.List))
}

func TestGetActDetail(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewActivityClient(a).GetActDetail(actId)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo, "act name", resp.ActName)
}

func TestGetAssetDetail(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewActivityClient(a).GetAssetDetail(actId, assetId)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo, "asset title", resp.Meta.Title)
}

func TestIntraCreateAct(t *testing.T) {
	param := &CreateOrUpdateActParam{
		OwnerType: 1,
		LinkType:  1,
		Position:  1,
		ActName:   "测试活动",
		ShortDesc: "活动短描述",
		Issuer:    "活动签发者",
		ImgDesc:   "https://xasset-open.cdn.bcebos.com/activity/prod/5103/ast/1/desc/1.jpg",
		Thumb:     "https://xasset-open.cdn.bcebos.com/activity/prod/5103/ast/1/desc/1.jpg",
		Start:     1682143200,
		End:       1682146800,
		ActStyle:  0,
		JumpLink:  "跳转链接",
		ExtInfo:   "描述信息",
	}
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewActivityClient(a).IntraCreateAct(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(reqInfo, "act id", resp.ActId)
}

func TestIntraAlterAct(t *testing.T) {
	param := &CreateOrUpdateActParam{
		ActId:     9697,
		OwnerType: 1,
		LinkType:  1,
		Position:  2,
	}
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewActivityClient(a).IntraAlterAct(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(reqInfo, "act id", resp.ActId)
}

func TestIntraCreateAst(t *testing.T) {
	param := &CreateOrUpdateAstParam{
		ActId:     9697,
		AssetId:   12345,
		Start:     1682143200,
		End:       1682146800,
		EviCert:   "证书信息",
		GrantMode: 1,
		ExtInfo:   "扩展信息",
	}
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, _, err := NewActivityClient(a).IntraCreateAst(param)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("asset id", resp.AssetId)
}

func TestIntraAlterAst(t *testing.T) {
	param := &CreateOrUpdateAstParam{
		ActId:   9697,
		AssetId: 12345,
		Start:   1682143200,
		End:     1682146800,
		EviCert: "证书",
	}
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, _, err := NewActivityClient(a).IntraAlterAst(param)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("asset id", resp.AssetId)
}

func TestIntraRemoveAst(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, _, err := NewActivityClient(a).IntraRemoveAst(actId, 1234)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("asset id", resp.AssetId)
}

func TestIntraCheckSplit(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, _, err := NewActivityClient(a).IntraCheckSplit(actId, 12345)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("status", resp.SplitStatus)
}

func TestIntraListAllAct(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, _, err := NewActivityClient(a).IntraListAllAct(1, 20)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("count", resp.TotalCnt, "list", resp.List)
}

func TestIntraListAstByAct(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, _, err := NewActivityClient(a).IntraListAstByAct(6622)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("list", resp.List)
}

func TestIntraSearchActivitybyName(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, _, err := NewActivityClient(a).IntraSearchActivitybyName("测试")
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("count:", resp.TotalCnt, "list:", resp.List)
}

func TestIntraOperateCompose(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, _, err := NewActivityClient(a).IntraAddComposeAst(composeId, assetId)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("add compose asset succ, asset_id: ", resp.AssetId)
	resp, _, err = NewActivityClient(a).IntraDelComposeAst(composeId, assetId)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("del compose asset succ, asset_id: ", resp.AssetId)
}
