package activity

import (
	"net/http"
)

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000
)

const (
	ActivityApiGetBannerList         = "/internal/activity/v1/listbanner"
	ActivityApiGetActList            = "/internal/activity/v1/listactivity"
	ActivityApiGetVerifyList         = "/internal/activity/v1/listverify"
	ActivityApiGetLabList            = "/internal/activity/v1/listlab"
	ActivityApiGetActDetail          = "/internal/activity/v1/describe"
	ActivityApiGetAssetDetail        = "/internal/activity/v1/details"
	ActivityApiIntranetCreateAct     = "/internal/activity/v1/createact"
	ActivityApiIntranetAlterAct      = "/internal/activity/v1/alteract"
	ActivityApiIntranetCreateAst     = "/internal/activity/v1/createast"
	ActivityApiIntranetAlterAst      = "/internal/activity/v1/alterast"
	ActivityApiIntranetRemoveAst     = "/internal/activity/v1/removeast"
	ActivityApiIntranetCheckSplit    = "/internal/activity/v1/checksplit"
	ActivityApiIntranetListAllAct    = "/internal/activity/v1/listallactivity"
	ActivityApiIntranetListAstByAct  = "/internal/activity/v1/listastbyact"
	ActivityApiIntranetPublishAct    = "/internal/activity/v1/publishact"
	ActivityApiIntranetRemoveAct     = "/internal/activity/v1/removeact"
	ActivityApiIntranetCreateBoxAst  = "/internal/activity/v1/createboxast"
	ActivityApiIntranetAlterBoxAst   = "/internal/activity/v1/alterboxast"
	ActivityApiIntranetSearchActName = "/internal/activity/v1/searchactbyname"
	ActivityApiIntranetAddComposeAst = "/internal/activity/v1/addcomposeast"
	ActivityApiIntranetDelComposeAst = "/internal/activity/v1/delcomposeast"
	ActivityApiIntranetAlterCompose  = "/internal/activity/v1/altercomstatus"
	ActivityApiAddDeliveryInfo       = "/internal/activity/v1/adddeliveryinfo"
	ActivityApiEditDeliveryInfo      = "/internal/activity/v1/editdeliveryinfo"
	ActivityApiGetDeliveryInfo       = "/internal/activity/v1/getdeliveryinfo"
	ActivityApiDeleteDeliveryInfo    = "/internal/activity/v1/deletedeliveryinfo"
	ActivityApiIntranetGetAmount     = "/internal/activity/v1/getrealamount"
	ActivityApiIntranetGetActByAsset = "/internal/activity/v1/getactbyasset"
)

const (
	AstStatusPreRelease = 0 // 预发布
	AstStatusInProgress = 1 // 进行中
	AstStatusOutOfStock = 2 // 已售罄
	AstStatusOutOfDate  = 3 // 已结束

	GrantModeUnordered = 0 // 碎片授予 无序
	GrantModeOrdered   = 1 // 碎片授予有序
	GrantModeInfinite  = 2 // 碎片授予不限量

	AssetApplyFormAirdrop  = 0 // 空投
	AssetApplyFormBuy      = 1 // 购买
	AssetApplyFormExchange = 2 // 兑换
	AssetApplyFormCompose  = 3 // 合成

	AssetCateNormal   = 0 // 普通藏品
	AssetCateBlindBox = 1 // 盲盒藏品
	AssetCateCompose  = 2 // 合成藏品

	ActListTypeSquare   = 1 // 活动广场
	ActListTypeCalendar = 2 // 活动日历

	ActOpTypePublish = 0 // 发布活动
	ActOpTypeOffline = 1 // 下线活动
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

type GetUInfoResp struct {
	BaseResp
	Addr     string `json:"address"`
	IsNew    int    `json:"is_new"`
	HasAsset int    `json:"has_asset"`
	Mnemonic string `json:"mnemonic"`
}

type GetBannerListResp struct {
	BaseResp
	List []*ActInfo `json:"list"`
}

type GetVerifyListResp struct {
	BaseResp
	List []*ActInfo `json:"list"`
}

type GetActListResp struct {
	BaseResp
	HasMore int        `json:"has_more"`
	Cursor  string     `json:"cursor"`
	List    []*ActInfo `json:"list"`
}

type GetActComposeListResp struct {
	BaseResp
	HasMore int             `json:"has_more"`
	Cursor  string          `json:"cursor"`
	List    []*ActAssetInfo `json:"list"`
}

type ActAssetInfo struct {
	ActId   int64    `json:"act_id"`
	Title   string   `json:"title"`
	Thumb   []string `json:"thumb"`
	AssetId int64    `json:"asset_id"`
}

type ActInfo struct {
	ActId         int64    `json:"act_id"`
	Title         string   `json:"title"`
	Issuer        string   `json:"issuer"`
	Status        int      `json:"status"`
	Position      int      `json:"position"`
	Thumb         []string `json:"thumb"`
	ActStyle      int      `json:"act_style"`
	ShortDesc     string   `json:"short_desc"`
	ImgDesc       []string `json:"img_desc"`
	Start         int64    `json:"start"`
	End           int64    `json:"end"`
	Bookable      int      `json:"bookable"`
	Weight        int      `json:"weight"`
	OwnerType     int      `json:"owner_type"`
	LinkType      int      `json:"link_type"`
	JumpLink      string   `json:"jump_link"`
	ExtInfo       string   `json:"ext_info"`
	PublishStatus int      `json:"publish_status"`
	StoreId       int      `json:"store_id"`
	StoreName     string   `json:"store_name"`
}

type GetActDetailResp struct {
	BaseResp
	ActId         int64            `json:"act_id"`
	StoreId       int              `json:"store_id"`
	StoreName     string           `json:"store_name"`
	ActStyle      int              `json:"act_style"`
	ActName       string           `json:"act_name"`
	ShortDesc     string           `json:"short_desc"`
	Status        int              `json:"status"`
	ImgDesc       []string         `json:"img_desc"`
	Start         int              `json:"start"`
	End           int              `json:"end"`
	Bookable      int              `json:"bookable"`
	ExtInfo       string           `json:"ext_info"`
	Issuer        string           `json:"issuer"`
	Position      int              `json:"position"`
	Thumb         []string         `json:"thumb"`
	Weight        int              `json:"weight"`
	OwnerType     int              `json:"owner_type"`
	LinkType      int              `json:"link_type"`
	JumpLink      string           `json:"jump_link"`
	PublishStatus int              `json:"publish_status"`
	AstInfoList   []*SimpleAstInfo `json:"ast_info_list"`
}

type GetActByAssetResp struct {
	BaseResp
	Meta *CollectActInfo
}

type CollectActInfo struct {
	ActId     int64  `json:"act_id"`
	Title     string `json:"title"`
	AssetCate int    `json:"asset_cate"` // 记录活动类型，分为普通、盲盒、合成
}

type SimpleAstInfo struct {
	AssetId  int64    `json:"asset_id"`
	Amount   int      `json:"amount"`
	Title    string   `json:"title"`
	Thumb    []string `json:"thumb"`
	Status   int      `json:"status"`
	Start    int64    `json:"start"`
	JumpLink string   `json:"jump_link"`
	End      int64    `json:"end"`
}

type GetAssetDetailResp struct {
	BaseResp
	Meta *AssetInfo `json:"meta"`
}

type CreateOrUpdateActParam struct {
	ActId     int    `json:"act_id"`
	StoreId   int    `json:"store_id"`
	OwnerType int    `json:"owner_type"`
	LinkType  int    `json:"link_type"`
	Position  int    `json:"position"`
	ActName   string `json:"act_name"`
	ShortDesc string `json:"short_desc"`
	Issuer    string `json:"issuer"`
	ImgDesc   string `json:"img_desc"`
	Thumb     string `json:"thumb"`
	Start     int64  `json:"start"`
	End       int64  `json:"end"`
	ActStyle  int    `json:"act_style"`
	Bookable  int    `json:"bookable"`
	Weight    int    `json:"weight"`
	JumpLink  string `json:"jump_link"`
	ExtInfo   string `json:"ext_info"`
}

func (p *CreateOrUpdateActParam) CreateValid() bool {
	if p.ActId < 1 || p.End < p.Start {
		return false
	}
	return true
}

func (p *CreateOrUpdateActParam) ModifyValid() bool {
	if p.ActId < 1 {
		return false
	}
	return true
}

type CreateOrUpdateActResp struct {
	BaseResp
	ActId int `json:"act_id"`
}

type CreateOrUpdateAstParam struct {
	ActId     int    `json:"act_id"`
	AssetId   int64  `json:"asset_id"`
	AssetCate int    `json:"asset_cate"`
	AssetInfo string `json:"asset_info"`
	Start     int64  `json:"start"`
	End       int64  `json:"end"`
	EviCert   string `json:"evi_cert"`
	GrantMode int    `json:"grant_mode"`
	JumpLink  string `json:"jump_link"`
	ExtInfo   string `json:"ext_info"`
	Price     int64  `json:"price"`
	OriPrice  int64  `json:"ori_price"`
	Amount    int    `json:"amount"`
	ApplyForm int    `json:"apply_form"`
	Series    int    `json:"series"`
}

func (p *CreateOrUpdateAstParam) CreateValid() bool {
	if p.ActId < 1 || p.AssetId < 1 || p.End < p.Start {
		return false
	}
	return true
}

func (p *CreateOrUpdateAstParam) ModifyValid() bool {
	if p.AssetId < 1 || p.ActId < 1 {
		return false
	}
	return true
}

type CreateOrUpdateAstResp struct {
	BaseResp
	AssetId int64 `json:"asset_id"`
}

type CheckSplitResp struct {
	BaseResp
	SplitStatus int `json:"split_status"`
}

type ListAllActResp struct {
	BaseResp
	TotalCnt int        `json:"total_cnt"`
	List     []*ActInfo `json:"list"`
}

type ListAstByActResp struct {
	BaseResp
	List []*AssetInfo `json:"list"`
}

type AssetInfo struct {
	AssetId   int64    `json:"asset_id"`
	AssetCate int      `json:"asset_cate"`
	Thumb     []string `json:"thumb"`
	Title     string   `json:"title"`
	ShortDesc string   `json:"short_desc"`
	TxId      string   `json:"tx_id"`
	AssetUrl  []string `json:"asset_url"`
	EviCert   string   `json:"evi_cert"`
	ImgDesc   []string `json:"img_desc"`
	Status    int      `json:"status"`
	ActId     int64    `json:"act_id"`
	Start     int64    `json:"start"`
	End       int64    `json:"end"`
	Price     int64    `json:"price"`
	OriPrice  int64    `json:"ori_price"`
	Amount    int64    `json:"amount"`
	ApplyForm int      `json:"apply_form"`
	Series    int      `json:"series"`
	GrantMode int      `json:"grant_mode"`
	JumpLink  string   `json:"jump_link"`
	ExtInfo   string   `json:"ext_info"`
	ComStatus int      `json:"com_status"`
}

type GetAmountResp struct {
	BaseResp
	Amount int64 `json:"amount"`
}

type ListActParam struct {
	Cursor string
	Limit  int
	Type   int
}

type ListLabParam struct {
	Cursor string
	Limit  int
}

func (p *ListActParam) Valid() bool {
	return p.Type == ActListTypeSquare || p.Type == ActListTypeCalendar
}

type CreateOrUpdateBoxAssetParam struct {
	BoxId     int64  `json:"box_id"`
	AssetId   int64  `json:"asset_id"`
	AssetInfo string `json:"asset_info"`
	Amount    int    `json:"amount"`
	EviCert   string `json:"evi_cert"`
	ExtInfo   string `json:"ext_info"`
	Price     int64  `json:"price"`
	OriPrice  int64  `json:"ori_price"`
}

func (p *CreateOrUpdateBoxAssetParam) Valid() bool {
	if p.BoxId < 1 || p.AssetId < 1 {
		return false
	}
	return true
}

type UpdateComposeResp struct {
	BaseResp
	AssetId int64 `json:"asset_id"`
}

type AddDeliveryParam struct {
	Uid    int64  `json:"uid"`
	Tel    string `json:"tel"`
	Name   string `json:"name"`
	Detail string `json:"detail"`
	Email  string `json:"email"`
}

type AddDeliveryResp struct {
	BaseResp
	DeliveryId int64 `json:"delivery_id"`
}

type EditDeliveryParam struct {
	DeliveryId int64  `json:"delivery_id"`
	Uid        int64  `json:"uid"`
	Tel        string `json:"tel"`
	Name       string `json:"name"`
	Detail     string `json:"detail"`
	Email      string `json:"email"`
}

type EditDeliveryResp struct {
	BaseResp
	DeliveryId int64 `json:"delivery_id"`
	Mtime      int64 `json:"mtime"`
}

type GetDeliveryParam struct {
	Uid int64 `json:"uid"`
}

type GetDeliveryResp struct {
	BaseResp
	DeliveryId int64  `json:"delivery_id"`
	Tel        string `json:"tel"`
	Name       string `json:"name"`
	Detail     string `json:"detail"`
	Email      string `json:"email"`
	Ctime      int64  `json:"ctime"`
	Mtime      int64  `json:"mtime"`
}

type DeleteDeliveryParam struct {
	DeliveryId int64 `json:"delivery_id"`
	Uid        int64 `json:"uid"`
}
