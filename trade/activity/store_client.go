package activity

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
)

func (t *ActivityClient) IntraCreateStore(param *CreateOrUpdateStoreParam) (*CreateOrUpdateStoreResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.CreateValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("store_name", param.StoreName)
	v.Set("store_type", fmt.Sprintf("%d", param.StoreType))
	v.Set("logo", param.Logo)
	v.Set("cover", param.Cover)
	v.Set("short_desc", param.ShortDesc)
	v.Set("weight", fmt.Sprintf("%d", param.Weight))
	v.Set("ext_info", param.ExtInfo)

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetCreateStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity to create store fail.err:%v", err)
	}
	var result CreateOrUpdateStoreResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraAlterStore(param *CreateOrUpdateStoreParam) (*CreateOrUpdateStoreResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.ModifyValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("store_name", param.StoreName)
	v.Set("store_type", fmt.Sprintf("%d", param.StoreType))
	v.Set("logo", param.Logo)
	v.Set("cover", param.Cover)
	v.Set("short_desc", param.ShortDesc)
	v.Set("weight", fmt.Sprintf("%d", param.Weight))
	v.Set("ext_info", param.ExtInfo)

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetAlterStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity to alter store fail.err:%v", err)
	}
	var result CreateOrUpdateStoreResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraPublishStore(param *BaseStoreParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("op_type", fmt.Sprintf("%d", param.OpType))

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetPublishStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity to publish store fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraRemoveStore(param *BaseStoreParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetRemoveStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity to remove store fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraGetStore(param *BaseStoreParam) (*GetStoreResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetGetStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity to get store fail.err:%v", err)
	}
	var result GetStoreResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraListStore(param *ListAllStoreParam) (*ListStoreResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}
	v.Set("page", fmt.Sprintf("%d", param.Page))

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetListStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity to list store fail.err:%v", err)
	}
	var result ListStoreResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) ListStoreDetail(param *ListStoreDetailParam) (*ListStoreDetailResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("cursor", param.Cursor)
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiListStoreDetail, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity to list store detail fail.err:%v", err)
	}
	var result ListStoreDetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) ListActByStore(param *ListActByStoreParam) (*ListActByStoreResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("cursor", param.Cursor)
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiListActByStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity to list store detail fail.err:%v", err)
	}
	var result ListActByStoreResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ActivityClient) IntraCreateOrAlterPubStore(param *CreateOrUpdateStoreParam) (*CreateOrUpdateStoreResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.CreateValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("store_name", param.StoreName)
	v.Set("store_type", fmt.Sprintf("%d", param.StoreType))
	v.Set("logo", param.Logo)
	v.Set("cover", param.Cover)
	v.Set("short_desc", param.ShortDesc)
	v.Set("weight", fmt.Sprintf("%d", param.Weight))
	v.Set("ext_info", param.ExtInfo)

	body := v.Encode()
	reqRes, err := t.doRequest(ActivityApiIntranetCreateOrAlterPubStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset activity to create or alter pub store fail.err:%v", err)
	}
	var result CreateOrUpdateStoreResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal activity response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}
