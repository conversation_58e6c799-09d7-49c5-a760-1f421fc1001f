package activity

const (
	ActivityApiIntranetCreateStore  = "/internal/activity/v1/createstore"
	ActivityApiIntranetAlterStore   = "/internal/activity/v1/alterstore"
	ActivityApiIntranetGetStore     = "/internal/activity/v1/querystore"
	ActivityApiIntranetListStore    = "/internal/activity/v1/liststore"
	ActivityApiIntranetPublishStore = "/internal/activity/v1/publishstore"
	ActivityApiIntranetRemoveStore  = "/internal/activity/v1/removestore"

	ActivityApiListStoreDetail = "/internal/activity/v1/liststoredetail"
	ActivityApiListActByStore  = "/internal/activity/v1/listactbystore"

	ActivityApiIntranetCreateOrAlterPubStore = "/internal/activity/v1/capstore"
	ActivityApiIntranetCreateOrAlterPubAct   = "/internal/activity/v1/capact"
)

const (
	StoreStatusInit    = 0 // 店铺初始状态，未发布
	StoreStatusPublish = 1 // 店铺已发布
	StoreTypeSelf      = 1 // 自营店铺
	StoreTypeExt       = 2 // 外部店铺
	StoreOpTypePublish = 0 // 发布店铺
	StoreOpTypeOffline = 1 // 下线店铺
)

type ActStore struct {
	StoreId   int    `json:"store_id"`
	StoreName string `json:"store_name"`
	StoreType int    `json:"store_type"`
	Logo      string `json:"logo"`
	Cover     string `json:"cover"`
	ShortDesc string `json:"short_desc"`
	Weight    int    `json:"weight"`
	Likes     int64  `json:"likes"`
	RealLikes int64  `json:"real_likes"`
	Status    int    `json:"status"`
	ExtInfo   string `json:"ext_info"`
	Ctime     int64  `json:"ctime"`
	Mtime     int64  `json:"mtime"`
}

type CreateOrUpdateStoreParam struct {
	StoreId   int    `json:"store_id"`
	StoreName string `json:"store_name"`
	StoreType int    `json:"store_type"`
	Logo      string `json:"logo"`
	Cover     string `json:"cover"`
	ShortDesc string `json:"short_desc"`
	Weight    int    `json:"weight"`
	ExtInfo   string `json:"ext_info"`
}

func (p *CreateOrUpdateStoreParam) CreateValid() bool {
	if p.StoreId < 1 {
		return false
	}
	return true
}

func (p *CreateOrUpdateStoreParam) ModifyValid() bool {
	if p.StoreId < 1 {
		return false
	}
	return true
}

type CreateOrUpdateStoreResp struct {
	BaseResp
	StoreId int `json:"store_id"`
}

type BaseStoreParam struct {
	StoreId int `json:"store_id"`
	OpType  int `json:"op_type"`
}

func (p *BaseStoreParam) Valid() bool {
	if p.StoreId < 1 {
		return false
	}
	return true
}

type GetStoreResp struct {
	BaseResp
	ActStore
}

type ListAllStoreParam struct {
	Limit int `json:"limit"`
	Page  int `json:"page"`
}

func (p *ListAllStoreParam) Valid() bool {
	if p.Limit < 0 || p.Page < 1 {
		return false
	}
	return true
}

type ListStoreResp struct {
	BaseResp
	TotalCnt int         `json:"total_cnt"`
	List     []*ActStore `json:"list"`
}

type ListActByStoreParam struct {
	Cursor  string `json:"cursor"`
	Limit   int    `json:"limit"`
	StoreId int    `json:"store_id"`
}

func (p *ListActByStoreParam) Valid() bool {
	if p.StoreId < 1 {
		return false
	}
	return true
}

type ListActByStoreResp struct {
	BaseResp

	StoreId   int        `json:"store_id"`
	StoreName string     `json:"store_name"`
	StoreType int        `json:"store_type"`
	ShortDesc string     `json:"short_desc"`
	Likes     int64      `json:"likes"`
	Logo      string     `json:"logo"`
	Cover     string     `json:"cover"`
	ActList   []*ActInfo `json:"act_list"`

	HasMore int    `json:"has_more"`
	Cursor  string `json:"cursor"`
}

type SimpleActInfo struct {
	ActId int      `json:"act_id"`
	Thumb []string `json:"thumb"`
}

type ListStoreDetailParam struct {
	Cursor string
	Limit  int
}

type StoreDetail struct {
	StoreId   int        `json:"store_id"`
	StoreName string     `json:"store_name"`
	StoreType int        `json:"store_type"`
	Likes     int64      `json:"likes"`
	Logo      string     `json:"logo"`
	Cover     string     `json:"cover"`
	ActList   []*ActInfo `json:"act_list"`
}

type ListStoreDetailResp struct {
	BaseResp

	List    []*StoreDetail `json:"list"`
	Cursor  string         `json:"cursor"`
	HasMore int            `json:"has_more"`
}
