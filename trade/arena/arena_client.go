package arena

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type ArenaClient struct {
	addr *addr.Addr
}

func NewArenaClient(addr *addr.Addr) *ArenaClient {
	return &ArenaClient{addr}
}

func (t *ArenaClient) ActDetail(actId int64) (*ActDetailResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if actId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", actId))

	body := v.Encode()
	reqRes, err := t.doRequest(ArenaActDetail, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xarena for act detail fail.err:%v", err)
	}
	var result ActDetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal arena response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ArenaClient) AstDetail(actId, assetId int64) (*AstDetailResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if actId < 1 && assetId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", actId))
	v.Set("asset_id", fmt.Sprintf("%d", assetId))

	body := v.Encode()
	reqRes, err := t.doRequest(ArenaAstDetail, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xarena for ast detail fail.err:%v", err)
	}
	var result AstDetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal arena response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *ArenaClient) ApplyShard(actId, assetId, shardId int64, bduss string) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if actId < 1 && assetId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", actId))
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	v.Set("shard_id", fmt.Sprintf("%d", shardId))

	body := v.Encode()
	reqRes, err := t.doRequest(ArenaApply, body, bduss)
	if err != nil {
		return nil, nil, fmt.Errorf("request xarena for asset apply fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal arena response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// post
func (t *ArenaClient) doRequest(api string, data string, bduss ...string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"

	if bduss != nil && len(bduss) > 0 {
		header["BDUSS"] = bduss[0]
	}

	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *ArenaClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
