package arena

import "net/http"

const (
	ArenaActDetail = "/xarena/pgcsell/v1/actdetail"
	ArenaAstDetail = "/xarena/pgcsell/v1/astdetail"
	ArenaApply     = "/xarena/pgcsell/v1/apply"

	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

type ActDetailResp struct {
	BaseResp
	Meta *ActInfo `json:"data"`
}

type ActInfo struct {
	ActId         string                  `json:"act_id"`
	ActName       string                  `json:"act_name"`
	Status        int                     `json:"status"`
	PublishStatus int                     `json:"publish_status"`
	ImgDesc       []string                `json:"img_desc"`
	ShortDesc     string                  `json:"short_desc"`
	Start         int64                   `json:"start"`
	End           int64                   `json:"end"`
	ExtInfo       string                  `json:"ext_info"`
	AstInfoList   []*DisplaySimpleAstInfo `json:"ast_info_list"`
	Bookable      int                     `json:'bookable'`
	StoreId       int                     `json:"store_id"`
	StoreName     string                  `json:"store_name"`
}

type DisplaySimpleAstInfo struct {
	AssetId       string   `json:"asset_id"`
	Title         string   `json:"title"`
	Thumb         []string `json:"thumb"`
	Status        int      `json:"status"`
	PublishStatus int      `json:"publish_status"`
	Start         int64    `json:"start"`
	JumpLink      string   `json:"jump_link"`
	End           int64    `json:"end"`
}

type AstDetailResp struct {
	BaseResp
	Meta *AstInfo `json:"data"`
}

type AstInfo struct {
	AssetId       string   `json:"asset_id"`
	ShardId       string   `json:"shard_id"`
	Thumb         []string `json:"thumb"`
	Title         string   `json:"title"`
	AssetCate     int      `json:"asset_cate"`
	ShortDesc     string   `json:"short_desc"`
	TxId          string   `json:"tx_id"`
	AssetUrl      []string `json:"asset_url"`
	AssetFormat   []string `json:"asset_format"`
	ImgDesc       []string `json:"img_desc"`
	Stock         int64    `json:"stock"`
	Status        int      `json:"status"`
	PublishStatus int      `json:"publish_status"`
	Start         int64    `json:"start"`
	End           int64    `json:"end"`
	Price         int64    `json:"price"`
	OriPrice      int64    `json:"ori_price"`
	ExtInfo       string   `json:"ext_info"`
	ApplyForm     int      `json:"apply_form"`
	HasLikes      int      `json:"has_likes"`
}
