package arena

import (
	"strconv"
	"strings"
)

// reverseBits 反转比特位（忽略高位无意义的0）
func reverseBits(n int64) int64 {
	var r int64 = 0
	for i := 0; n > 0; i++ {
		r <<= 1
		r |= n & 0x1
		n >>= 1
	}

	return r
}

// EncodePgcID 用于将PGC的数字类型ID编码成不可遍历的字符类型
func EncodePgcID(id int64) string {
	// 1、二进制反转。反转前左移1位，低位补1，确保不丢低位的0
	reverse := id << 1
	reverse = reverseBits(reverse | 1)

	// 2、如果数字过大溢出，无法转回，则直接返回特定格式
	if reverseBits(reverse)>>1 != id {
		return "z" + strconv.FormatInt(id, 33)
	}

	// 3、使用33进制编码，保留 x y z
	str := strconv.FormatInt(reverse, 33)

	// 4、替换某些字符，增加反解难度
	str = strings.NewReplacer("1", "x", "8", "y").Replace(str)

	return str
}

func DecodePgcID(str string) int64 {
	if len(str) == 0 {
		return 0
	}

	// 0、兼容一些已经发出去的固定链接
	if value, exist := isStockId(str); exist {
		return value
	}

	// 1、以 z 开头的，直接解析
	if strings.HasPrefix(str, "z") {
		id, _ := strconv.ParseInt(str[1:], 33, 64)
		return id
	}

	// 2、替换回字符
	str = strings.NewReplacer("x", "1", "y", "8").Replace(str)

	// 3、按33进制解析
	id, err := strconv.ParseInt(str, 33, 64)
	if err != nil {
		return id
	}

	// 4、二进制反转，并右移一位
	id = reverseBits(id) >> 1

	return id
}

func isStockId(str string) (int64, bool) {
	if value, exist := stockIds[str]; exist {
		return value, true
	}

	if value, exist := stockIdsOfSandbox[str]; exist {
		return value, true
	}

	return 0, false
}

var stockIds = map[string]int64{
	"1001":     1001,
	"1002":     1002,
	"328004":   328004,
	"38908193": 38908193,
	"48345374": 48345374,
	"65911871": 65911871,

	"5107":           5107,
	"5108":           5108,
	"5109":           5109,
	"5110":           5110,
	"5111":           5111,
	"5112":           5112,
	"5113":           5113,
	"5114":           5114,
	"5115":           5115,
	"5116":           5116,
	"5117":           5117,
	"5118":           5118,
	"5119":           5119,
	"5120":           5120,
	"5121":           5121,
	"5122":           5122,
	"5123":           5123,
	"5124":           5124,
	"5125":           5125,
	"5126":           5126,
	"5128":           5128,
	"5129":           5129,
	"5130":           5130,
	"5131":           5131,
	"5132":           5132,
	"5133":           5133,
	"5134":           5134,
	"5135":           5135,
	"5136":           5136,
	"5137":           5137,
	"5138":           5138,
	"5139":           5139,
	"5140":           5140,
	"5141":           5141,
	"5142":           5142,
	"5143":           5143,
	"5144":           5144,
	"5145":           5145,
	"5146":           5146,
	"5147":           5147,
	"5148":           5148,
	"5149":           5149,
	"5150":           5150,
	"5151":           5151,
	"5152":           5152,
	"5153":           5153,
	"5154":           5154,
	"5155":           5155,
	"5156":           5156,
	"5157":           5157,
	"5158":           5158,
	"5159":           5159,
	"5160":           5160,
	"5161":           5161,
	"5162":           5162,
	"5163":           5163,
	"5164":           5164,
	"5165":           5165,
	"5166":           5166,
	"5167":           5167,
	"5168":           5168,
	"5169":           5169,
	"5170":           5170,
	"5171":           5171,
	"5172":           5172,
	"5173":           5173,
	"5174":           5174,
	"5175":           5175,
	"5176":           5176,
	"5177":           5177,
	"5178":           5178,
	"5179":           5179,
	"5180":           5180,
	"5181":           5181,
	"5182":           5182,
	"5183":           5183,
	"5184":           5184,
	"5185":           5185,
	"5186":           5186,
	"5187":           5187,
	"5188":           5188,
	"5189":           5189,
	"5190":           5190,
	"5192":           5192,
	"5193":           5193,
	"5194":           5194,
	"5195":           5195,
	"5196":           5196,
	"5197":           5197,
	"5198":           5198,
	"5199":           5199,
	"5200":           5200,
	"5201":           5201,
	"5202":           5202,
	"5203":           5203,
	"5204":           5204,
	"5205":           5205,
	"5207":           5207,
	"5208":           5208,
	"5209":           5209,
	"5210":           5210,
	"5211":           5211,
	"5213":           5213,
	"5214":           5214,
	"5215":           5215,
	"5216":           5216,
	"5217":           5217,
	"5219":           5219,
	"5220":           5220,
	"5222":           5222,
	"5223":           5223,
	"5224":           5224,
	"5225":           5225,
	"5226":           5226,
	"5227":           5227,
	"5228":           5228,
	"5229":           5229,
	"5230":           5230,
	"5231":           5231,
	"5232":           5232,
	"5233":           5233,
	"5235":           5235,
	"5236":           5236,
	"5237":           5237,
	"5238":           5238,
	"5239":           5239,
	"5240":           5240,
	"5241":           5241,
	"5242":           5242,
	"5243":           5243,
	"5244":           5244,
	"5245":           5245,
	"5246":           5246,
	"5247":           5247,
	"5248":           5248,
	"5249":           5249,
	"5250":           5250,
	"5251":           5251,
	"5252":           5252,
	"5253":           5253,
	"5255":           5255,
	"5256":           5256,
	"5257":           5257,
	"5258":           5258,
	"5259":           5259,
	"5260":           5260,
	"5261":           5261,
	"5262":           5262,
	"5263":           5263,
	"5264":           5264,
	"5265":           5265,
	"5266":           5266,
	"5267":           5267,
	"5268":           5268,
	"5269":           5269,
	"5270":           5270,
	"5271":           5271,
	"5272":           5272,
	"5273":           5273,
	"5275":           5275,
	"5277":           5277,
	"5278":           5278,
	"5279":           5279,
	"5280":           5280,
	"5281":           5281,
	"5282":           5282,
	"5283":           5283,
	"5284":           5284,
	"5285":           5285,
	"5286":           5286,
	"5287":           5287,
	"5288":           5288,
	"5289":           5289,
	"5290":           5290,
	"5291":           5291,
	"5292":           5292,
	"5293":           5293,
	"5295":           5295,
	"5296":           5296,
	"5297":           5297,
	"5299":           5299,
	"5300":           5300,
	"5301":           5301,
	"5304":           5304,
	"5305":           5305,
	"5306":           5306,
	"5308":           5308,
	"5309":           5309,
	"5310":           5310,
	"5311":           5311,
	"5312":           5312,
	"5313":           5313,
	"5315":           5315,
	"5316":           5316,
	"5318":           5318,
	"5319":           5319,
	"5320":           5320,
	"5322":           5322,
	"5323":           5323,
	"5324":           5324,
	"5325":           5325,
	"5326":           5326,
	"5327":           5327,
	"5328":           5328,
	"5329":           5329,
	"5330":           5330,
	"5331":           5331,
	"5332":           5332,
	"5334":           5334,
	"5335":           5335,
	"5336":           5336,
	"5337":           5337,
	"5338":           5338,
	"5339":           5339,
	"5340":           5340,
	"5341":           5341,
	"5342":           5342,
	"5344":           5344,
	"5346":           5346,
	"5347":           5347,
	"5349":           5349,
	"5350":           5350,
	"5351":           5351,
	"5352":           5352,
	"5353":           5353,
	"5355":           5355,
	"5356":           5356,
	"5358":           5358,
	"5359":           5359,
	"5360":           5360,
	"5361":           5361,
	"5362":           5362,
	"5363":           5363,
	"5364":           5364,
	"5366":           5366,
	"5368":           5368,
	"5369":           5369,
	"5370":           5370,
	"5372":           5372,
	"5373":           5373,
	"5374":           5374,
	"5375":           5375,
	"5377":           5377,
	"5378":           5378,
	"5379":           5379,
	"5380":           5380,
	"5382":           5382,
	"5383":           5383,
	"5385":           5385,
	"5388":           5388,
	"5390":           5390,
	"5391":           5391,
	"5392":           5392,
	"5393":           5393,
	"5395":           5395,
	"5396":           5396,
	"5397":           5397,
	"5398":           5398,
	"5399":           5399,
	"5400":           5400,
	"5401":           5401,
	"5402":           5402,
	"5404":           5404,
	"5405":           5405,
	"5406":           5406,
	"5407":           5407,
	"5408":           5408,
	"5410":           5410,
	"5414":           5414,
	"5415":           5415,
	"5416":           5416,
	"5417":           5417,
	"5419":           5419,
	"5420":           5420,
	"5421":           5421,
	"5423":           5423,
	"5424":           5424,
	"5426":           5426,
	"5427":           5427,
	"5429":           5429,
	"5431":           5431,
	"5434":           5434,
	"5435":           5435,
	"5438":           5438,
	"5439":           5439,
	"5440":           5440,
	"5441":           5441,
	"5442":           5442,
	"5443":           5443,
	"5445":           5445,
	"5446":           5446,
	"5447":           5447,
	"5449":           5449,
	"5451":           5451,
	"5452":           5452,
	"5453":           5453,
	"5454":           5454,
	"5455":           5455,
	"5456":           5456,
	"5457":           5457,
	"5458":           5458,
	"5459":           5459,
	"5460":           5460,
	"5461":           5461,
	"5462":           5462,
	"5463":           5463,
	"5464":           5464,
	"5465":           5465,
	"5466":           5466,
	"5467":           5467,
	"5469":           5469,
	"5470":           5470,
	"5471":           5471,
	"5473":           5473,
	"5474":           5474,
	"5476":           5476,
	"5477":           5477,
	"5480":           5480,
	"5481":           5481,
	"5482":           5482,
	"5483":           5483,
	"5485":           5485,
	"5486":           5486,
	"5487":           5487,
	"5488":           5488,
	"5489":           5489,
	"5490":           5490,
	"5491":           5491,
	"5492":           5492,
	"5493":           5493,
	"5494":           5494,
	"5495":           5495,
	"5496":           5496,
	"5648":           5648,
	"5649":           5649,
	"5888":           5888,
	"9565":           9565,
	"12905":          12905,
	"99999":          99999,
	"16716943552804": 16716943552804,
	"16716959451341": 16716959451341,
	"16765400242074": 16765400242074,
	"16819722203667": 16819722203667,
	"16819743433087": 16819743433087,
	"16850879223936": 16850879223936,
	"16860291024092": 16860291024092,
	"16862801442108": 16862801442108,
	"16863093252315": 16863093252315,
	"16867331142752": 16867331142752,
	"16867407513005": 16867407513005,
	"16872283261543": 16872283261543,
	"16872312592837": 16872312592837,
	"16872531253906": 16872531253906,
	"16872551801818": 16872551801818,
	"16873277933237": 16873277933237,
	"16877494702309": 16877494702309,
	"16884651403096": 16884651403096,
	"16884659123634": 16884659123634,
	"16889889461843": 16889889461843,
	"16889907233968": 16889907233968,
}

var stockIdsOfSandbox = map[string]int64{
	"111":        111,
	"123":        123,
	"222":        222,
	"321":        321,
	"333":        333,
	"1001":       1001,
	"1111":       1111,
	"1234":       1234,
	"2222":       2222,
	"3138":       3138,
	"3212":       3212,
	"3213":       3213,
	"12345":      12345,
	"54321":      54321,
	"100001":     100001,
	"102254":     102254,
	"123456":     123456,
	"159550":     159550,
	"164940":     164940,
	"360705":     360705,
	"383375":     383375,
	"476135":     476135,
	"587893":     587893,
	"634792":     634792,
	"675631":     675631,
	"687977":     687977,
	"701085":     701085,
	"739570":     739570,
	"826968":     826968,
	"913173":     913173,
	"37848878":   37848878,
	"49383213":   49383213,
	"53577515":   53577515,
	"54626079":   54626079,
	"59868958":   59868958,
	"64063284":   64063284,
	"1231342143": 1231342143,

	"1":              1,
	"12":             12,
	"99":             99,
	"101":            101,
	"117":            117,
	"170":            170,
	"175":            175,
	"177":            177,
	"178":            178,
	"179":            179,
	"180":            180,
	"181":            181,
	"191":            191,
	"199":            199,
	"234":            234,
	"241":            241,
	"407":            407,
	"408":            408,
	"421":            421,
	"422":            422,
	"423":            423,
	"424":            424,
	"425":            425,
	"426":            426,
	"430":            430,
	"431":            431,
	"433":            433,
	"434":            434,
	"438":            438,
	"440":            440,
	"441":            441,
	"442":            442,
	"443":            443,
	"444":            444,
	"445":            445,
	"446":            446,
	"450":            450,
	"460":            460,
	"461":            461,
	"465":            465,
	"466":            466,
	"467":            467,
	"468":            468,
	"469":            469,
	"470":            470,
	"471":            471,
	"472":            472,
	"473":            473,
	"474":            474,
	"475":            475,
	"476":            476,
	"477":            477,
	"478":            478,
	"482":            482,
	"483":            483,
	"484":            484,
	"485":            485,
	"486":            486,
	"488":            488,
	"489":            489,
	"490":            490,
	"492":            492,
	"496":            496,
	"497":            497,
	"512":            512,
	"786":            786,
	"830":            830,
	"876":            876,
	"890":            890,
	"892":            892,
	"893":            893,
	"894":            894,
	"895":            895,
	"896":            896,
	"897":            897,
	"898":            898,
	"901":            901,
	"902":            902,
	"908":            908,
	"909":            909,
	"1008":           1008,
	"1009":           1009,
	"1010":           1010,
	"1011":           1011,
	"1012":           1012,
	"1112":           1112,
	"1113":           1113,
	"1114":           1114,
	"1232":           1232,
	"1259":           1259,
	"1357":           1357,
	"1358":           1358,
	"1359":           1359,
	"1360":           1360,
	"1367":           1367,
	"1368":           1368,
	"1369":           1369,
	"1422":           1422,
	"1449":           1449,
	"1467":           1467,
	"1729":           1729,
	"1749":           1749,
	"1770":           1770,
	"1906":           1906,
	"1931":           1931,
	"1992":           1992,
	"1999":           1999,
	"2000":           2000,
	"2001":           2001,
	"2004":           2004,
	"2022":           2022,
	"2342":           2342,
	"2721":           2721,
	"2901":           2901,
	"3001":           3001,
	"3002":           3002,
	"3003":           3003,
	"3004":           3004,
	"3005":           3005,
	"3006":           3006,
	"3007":           3007,
	"3009":           3009,
	"3100":           3100,
	"3120":           3120,
	"3200":           3200,
	"3201":           3201,
	"3202":           3202,
	"3203":           3203,
	"3205":           3205,
	"3206":           3206,
	"3207":           3207,
	"3208":           3208,
	"3209":           3209,
	"3210":           3210,
	"3211":           3211,
	"3214":           3214,
	"3300":           3300,
	"3333":           3333,
	"3334":           3334,
	"3335":           3335,
	"3336":           3336,
	"3337":           3337,
	"3400":           3400,
	"3402":           3402,
	"3403":           3403,
	"3404":           3404,
	"3406":           3406,
	"3610":           3610,
	"3976":           3976,
	"4001":           4001,
	"4002":           4002,
	"4003":           4003,
	"4004":           4004,
	"4005":           4005,
	"4211":           4211,
	"4322":           4322,
	"4333":           4333,
	"5121":           5121,
	"5247":           5247,
	"8116":           8116,
	"8201":           8201,
	"8381":           8381,
	"9562":           9562,
	"9563":           9563,
	"9569":           9569,
	"9571":           9571,
	"9607":           9607,
	"9612":           9612,
	"9617":           9617,
	"9618":           9618,
	"9620":           9620,
	"9621":           9621,
	"9662":           9662,
	"9663":           9663,
	"9876":           9876,
	"9898":           9898,
	"9899":           9899,
	"9988":           9988,
	"10000":          10000,
	"10003":          10003,
	"10004":          10004,
	"10007":          10007,
	"10009":          10009,
	"10010":          10010,
	"10011":          10011,
	"11112":          11112,
	"11123":          11123,
	"12321":          12321,
	"12598":          12598,
	"12997":          12997,
	"13278":          13278,
	"19231":          19231,
	"20001":          20001,
	"20002":          20002,
	"22312":          22312,
	"22801":          22801,
	"23423":          23423,
	"29086":          29086,
	"31001":          31001,
	"31002":          31002,
	"31003":          31003,
	"31004":          31004,
	"31005":          31005,
	"32101":          32101,
	"32195":          32195,
	"33001":          33001,
	"33231":          33231,
	"33232":          33232,
	"33331":          33331,
	"33333":          33333,
	"33334":          33334,
	"33335":          33335,
	"34001":          34001,
	"34002":          34002,
	"34003":          34003,
	"34044":          34044,
	"34046":          34046,
	"34047":          34047,
	"34048":          34048,
	"34049":          34049,
	"34050":          34050,
	"34051":          34051,
	"34052":          34052,
	"34053":          34053,
	"34054":          34054,
	"34055":          34055,
	"34057":          34057,
	"34058":          34058,
	"34060":          34060,
	"34061":          34061,
	"38901":          38901,
	"41001":          41001,
	"41003":          41003,
	"41004":          41004,
	"41005":          41005,
	"41070":          41070,
	"52001":          52001,
	"52002":          52002,
	"52003":          52003,
	"52005":          52005,
	"52006":          52006,
	"54322":          54322,
	"54323":          54323,
	"56421":          56421,
	"65400":          65400,
	"65500":          65500,
	"65501":          65501,
	"65534":          65534,
	"65535":          65535,
	"78211":          78211,
	"78301":          78301,
	"78621":          78621,
	"78623":          78623,
	"78624":          78624,
	"78625":          78625,
	"78632":          78632,
	"78633":          78633,
	"78634":          78634,
	"78635":          78635,
	"89001":          89001,
	"89002":          89002,
	"89271":          89271,
	"96011":          96011,
	"96012":          96012,
	"99999":          99999,
	"100002":         100002,
	"100003":         100003,
	"100004":         100004,
	"100005":         100005,
	"100006":         100006,
	"100007":         100007,
	"100008":         100008,
	"100011":         100011,
	"100012":         100012,
	"100879":         100879,
	"100974":         100974,
	"114514":         114514,
	"123121":         123121,
	"123122":         123122,
	"123123":         123123,
	"123211":         123211,
	"123501":         123501,
	"123502":         123502,
	"124123":         124123,
	"220617":         220617,
	"220701":         220701,
	"234234":         234234,
	"334555":         334555,
	"340006":         340006,
	"340500":         340500,
	"350081":         350081,
	"456789":         456789,
	"890881":         890881,
	"890882":         890882,
	"1001111":        1001111,
	"1111111":        1111111,
	"1231231":        1231231,
	"1231236":        1231236,
	"1234567":        1234567,
	"1235001":        1235001,
	"1243525":        1243525,
	"3404441":        3404441,
	"4324212":        4324212,
	"5566677":        5566677,
	"20220929":       20220929,
	"20221229":       20221229,
	"20230113":       20230113,
	"20230116":       20230116,
	"20230221":       20230221,
	"20230222":       20230222,
	"124352534":      124352534,
	"432421244":      432421244,
	"2908632141":     2908632141,
	"12435253456":    12435253456,
	"12435253456232": 12435253456232,
}
