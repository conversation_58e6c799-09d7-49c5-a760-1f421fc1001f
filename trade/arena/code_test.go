package arena

import (
	"fmt"
	"math"
	"testing"
)

func TestEncodeDecode(t *testing.T) {

	test(t, 10)
	test(t, 16867407513005)
	test(t, 16867407513000)
	test(t, 16867408513006)
	test(t, math.MaxInt64)
	test(t, 1001)
	test(t, 46932569)
	test(t, 33094376)

}

func test(t *testing.T, n int64) {
	after := EncodePgcID(n)

	fmt.Println("raw:   ", n)
	fmt.Println("tarns: ", after)

	raw := DecodePgcID(after)
	if raw != n {
		t.Fatal("not equal")
	}
}

func TestStockIds(t *testing.T) {
	stockId := "5151"

	decoded := DecodePgcID(stockId)
	if decoded != 5151 {
		t.Fatal()
	}
}
