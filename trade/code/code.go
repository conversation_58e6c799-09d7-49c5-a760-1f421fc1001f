package code

import (
	"fmt"
	"strings"
)

var (
	InfCodeTable = []rune("ABCDEFGHIJKLMNOPQRSTUVWXYZ234567")
)

func decodeCode(code string) (uint64, error) {
	code = formatDecodeCode(code)

	codeId := uint64(0)
	for _, ch := range code {
		codeId <<= 5
		idx, err := char2idx(ch)
		if err != nil {
			return 0, err
		}
		codeId |= uint64(idx)
	}
	return codeId, nil
}

// 按5位为一组映射到编码表字符, 长度按要编码的长度设计
func encodeCode(codeId uint64, codeLen int) (string, error) {
	code := []rune{}
	mask := uint64(0x000000000000001f)
	for i := 0; i < codeLen; i++ {
		idx := int(codeId & mask)
		ch, err := idx2char(idx)
		if err != nil {
			return "", fmt.Errorf("idx2char fail.err:%v idx:%d", err, idx)
		}
		code = append(code, ch)
		codeId >>= 5
	}

	for l, r := 0, len(code)-1; l < r; l, r = l+1, r-1 {
		code[l], code[r] = code[r], code[l]
	}

	return formatEncodeCode(string(code)), nil
}

func idx2char(idx int) (rune, error) {
	if idx < 0 || idx >= 32 {
		return 0, fmt.Errorf("idx invalid")
	}
	return InfCodeTable[idx], nil
}

func char2idx(ch rune) (int, error) {
	if ch >= 'A' && ch <= 'Z' {
		return int(ch - 'A'), nil
	} else if ch >= '2' && ch <= '7' {
		return int(26 + ch - '2'), nil
	} else {
		return -1, fmt.Errorf("invalid code table char")
	}
}

// I和1, 0和O容易造成视觉上的歧义，将其替换为没有歧义的字符
// I -> 8, O -> 9
func formatEncodeCode(code string) string {
	r := strings.NewReplacer("I", "8", "O", "9")
	return r.Replace(code)
}

// formatEncodeInfCode的逆过程
// 8 -> I, 9 -> O
func formatDecodeCode(code string) string {
	r := strings.NewReplacer("8", "I", "9", "O")
	return r.Replace(strings.ToUpper(code))
}

func reversal(str string) string {
	code := []rune(str)

	for l, r := 0, len(code)-1; l < r; l, r = l+1, r-1 {
		code[l], code[r] = code[r], code[l]
	}

	return string(code)
}
