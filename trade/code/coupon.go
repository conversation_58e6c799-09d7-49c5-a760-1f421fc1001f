package code

import (
	"crypto/md5"
	"errors"
	"fmt"
	"io"

	"icode.baidu.com/baidu/blockchain/xasset-golib/utils"
)

// act_id:16位 nonce:16位 sign:8位
func EncodeActCode(actId uint16) (string, error) {
	actId = actId & 0xFFFF
	nonce := utils.GenNonce() & 0xFFFF

	// 计算校验位
	h := md5.New()
	io.WriteString(h, fmt.Sprintf("%d-%d", actId, nonce))
	digest := h.Sum(nil)
	signNo := uint16(digest[0] & 0xFF)

	// 编码
	codeId := (uint64(actId) << 24) | (uint64(nonce) << 8) | uint64(signNo)
	code, err := encodeCode(codeId, 8)
	if err != nil {
		return "", err
	}
	code = reversal(code)

	return code, nil
}

func DecodeActCode(code string) (uint16, error) {
	// 处理格式
	code = reversal(code)
	if len(code) != 8 {
		return 0, errors.New("code format error")
	}

	// 解码
	codeId, err := decodeCode(code)
	if err != nil {
		return 0, err
	}
	actId := uint16(codeId >> 24 & 0xFFFF)
	nonce := uint16(codeId >> 8 & 0xFFFF)
	signNo := uint16(codeId & 0xFF)

	// 计算校验位，校验位对不上报错
	h := md5.New()
	io.WriteString(h, fmt.Sprintf("%d-%d", actId, nonce))
	digest := h.Sum(nil)
	sign := uint16(digest[0] & 0xFF)
	if sign != signNo {
		return 0, errors.New("code sign error")
	}

	return actId, nil
}

// 总共使用24位，第24位为标记位，astId不能拆过23位
func EncodeAstNo(astId uint32, isNonce bool) uint32 {
	astId = astId & 0x7FFFFF
	if isNonce {
		astId = astId | 0x800000
	}

	return astId
}

// 返回astId和是否nonce填充
func DecodeeAstNo(astNo uint32) (uint32, bool) {
	isisNonce := false
	if astNo>>23 == 1 {
		isisNonce = true
	}

	return astNo & 0x7FFFFF, isisNonce
}

// act_id:16位 ast_no:24位 shard_id:32位 校验位:8位
// 输入参数不允许超过允许长度，超过按截断处理
func EncodeCouponId(actId uint16, astNo uint32, shardId uint32) (string, error) {
	// 取固定位
	actId = actId & 0xFFFF
	astNo = astNo & 0xFFFFFF
	shardId = shardId & 0xFFFFFFFF

	// 计算校验位
	h := md5.New()
	io.WriteString(h, fmt.Sprintf("%d-%d-%d", actId, astNo, shardId))
	digest := h.Sum(nil)
	signNo := uint16(digest[0] & 0xFF)

	// 分为两组编码
	var codeId1, codeId2 uint64
	codeId1 = (uint64(actId) << 24) | uint64(astNo)
	codeId2 = (uint64(shardId) << 8) | uint64(signNo)
	code1, err := encodeCode(codeId1, 8)
	if err != nil {
		return "", err
	}
	code2, err := encodeCode(codeId2, 8)
	if err != nil {
		return "", err
	}
	code := reversal(code1 + code2)

	// 格式化
	//codeArr := make([]string, 4)
	//for i := 0; i < len(code); i += 4 {
	//	codeArr[i/4] = code[i : i+4]
	//}
	//return strings.Join(codeArr, "-"), nil

	return code, nil
}

func DecodeCouponId(code string) (uint16, uint32, uint32, error) {
	// 处理格式
	//code = strings.ReplaceAll(code, "-", "")
	code = reversal(code)
	if len(code) != 16 {
		return 0, 0, 0, errors.New("code format error")
	}

	// 解码
	codeId1, err := decodeCode(string(code[0:8]))
	if err != nil {
		return 0, 0, 0, err
	}
	codeId2, err := decodeCode(string(code[8:16]))
	if err != nil {
		return 0, 0, 0, err
	}
	actId := uint16(codeId1 >> 24 & 0xFFFF)
	astNo := uint32(codeId1 & 0xFFFFFF)
	shardId := uint32(codeId2 >> 8 & 0xFFFFFFFF)
	signNo := uint16(codeId2 & 0xFF)

	// 计算校验位，校验位对不上报错
	h := md5.New()
	io.WriteString(h, fmt.Sprintf("%d-%d-%d", actId, astNo, shardId))
	digest := h.Sum(nil)
	sign := uint16(digest[0] & 0xFF)
	if sign != signNo {
		return 0, 0, 0, errors.New("code sign error")
	}

	return actId, astNo, shardId, nil
}
