package code

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/utils"
)

func TestEncodeActCode(t *testing.T) {
	actId := 5300
	fmt.Println(actId)

	code, err := EncodeActCode(uint16(actId))
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(code)

	a, err := DecodeActCode(code)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(a)
}

// act_id:16位 藏品ID:24位 碎片ID:32位 校验位:8位 共计80位，80/5=16个字符长度的字符串
func TestEncodeCouponId(t *testing.T) {
	actId := 2300
	// 活动级 0x800000
	astId := utils.GenNonce() & 0x7FFFFF
	isNonce := false
	astNo := EncodeAstNo(uint32(astId), isNonce)
	// 藏品级
	shardId := utils.GenNonce() & 0xFFFFFFFF
	fmt.Printf("act_id:%d ast_id:%d flag:%v shard_id:%d\n", actId, astId, isNonce, shardId)

	code, err := EncodeCouponId(uint16(actId), uint32(astNo), uint32(shardId))
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Printf("code:%s\n", code)

	a, n, s, err := DecodeCouponId(code)
	if err != nil {
		fmt.Println(err)
		return
	}
	i, f := DecodeeAstNo(n)

	fmt.Printf("act_id:%d ast_id:%d flag:%v shard_id:%d\n", a, i, f, s)
}
