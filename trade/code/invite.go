package code

import (
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/utils"
)

const (
	// 邀请码长度
	InviteCodeLen = 8
)

// 生成邀请码code id，长度为40位，低8位为uid低8位
func GenInviteCodeId(uid int64) uint64 {
	nonce := utils.GenNonce()
	codeId := ((nonce << 8) | (uid & 0xFF)) & 0xFFFFFFFFFF
	return uint64(codeId)
}

// codeId只取低40位
func EncodeInviteCode(codeId uint64) (string, error) {
	codeId = codeId & 0xFFFFFFFFFF
	return encodeCode(codeId, InviteCodeLen)
}

func DecodeInviteCode(inviteCode string) (uint64, error) {
	if len(inviteCode) != InviteCodeLen {
		return 0, fmt.Errorf("invite code fotmat error")
	}

	return decodeCode(inviteCode)
}
