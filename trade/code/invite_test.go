package code

import (
	"fmt"
	"testing"
)

func TestEncodeInviteCode(t *testing.T) {
	uid := 98860980
	fmt.Println("Uid:", uid, "Tab:", (uid & 0xFF))

	codeId := GenInviteCodeId(int64(uid))
	fmt.Println("CodeId:", codeId)

	invite, err := EncodeInviteCode(codeId)
	if err != nil {
		fmt.Println("encode code id failed.err:", err)
		return
	}
	fmt.Println("InviteCode:", invite)

	codeId, err = DecodeInviteCode(invite)
	if err != nil {
		fmt.Println("decode code id failed.err:", err)
		return
	}
	fmt.Println("CodeId:", codeId, "Tab:", (codeId & 0xFF))
}
