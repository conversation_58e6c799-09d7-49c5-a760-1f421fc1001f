package did

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type DidClient struct {
	addr *addr.Addr
}

func NewDidClient(addr *addr.Addr) *DidClient {
	return &DidClient{addr}
}

func (t *DidClient) LoadOrStoreAcc(param *LoadOrStoreAccParam) (*LoadOrStoreAccResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param.Uid < 1 || param.AddrType < 1 || param.MnemLang < 1 || param.MnemStrg < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("addr_type", fmt.Sprintf("%d", param.AddrType))
	v.Set("group_id", fmt.Sprintf("%d", param.GroupId))
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("mnem_lang", fmt.Sprintf("%d", param.MnemLang))
	v.Set("mnem_strg", fmt.Sprintf("%d", param.MnemStrg))

	body := v.Encode()
	reqRes, err := t.doRequest(DidApiGetUinfo, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset did for LoadOrStoreAcc fail.err:%v", err)
	}
	var result LoadOrStoreAccResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal did response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *DidClient) GetCloudAccInfo(param *GetCloudAccParam) (*GetCloudAccResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if err := param.IsValid(); err != nil {
		return nil, nil, fmt.Errorf("param error %v", err)
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("addr", param.Addr)

	body := v.Encode()
	reqRes, err := t.doRequest(DidApiGetCloudAccInfo, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset did for GetCloudAccInfo fail.err:%v", err)
	}
	var result GetCloudAccResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal did response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *DidClient) BindByMnem(groupId, uid int64, mnemonic string) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if groupId < 1 || uid < 1 || mnemonic == "" {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("group_id", fmt.Sprintf("%d", groupId))
	v.Set("uid", fmt.Sprintf("%d", uid))
	v.Set("mnemonic", mnemonic)

	body := v.Encode()
	reqRes, err := t.doRequest(DidApiBindByMnemonic, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset did for bind by mnemonic fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal did response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *DidClient) GetAccByAddr(addr string, uid int64) (*GetAccByResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if addr == "" || uid < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("addr", addr)
	v.Set("uid", fmt.Sprintf("%d", uid))

	body := v.Encode()
	reqRes, err := t.doRequest(DidApiGetAccByAddr, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset did for get acc by addr fail.err:%v", err)
	}
	var result GetAccByResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal did response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *DidClient) ListAddr(uid int64) (*ListAddrResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if uid < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", uid))

	body := v.Encode()
	reqRes, err := t.doRequest(DidApiListAccByUid, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset did for list addr fail.err:%v", err)
	}
	var result ListAddrResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal did response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *DidClient) DisApprove(uid int64, addr string) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if uid < 1 || addr == "" {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", uid))
	v.Set("addr", addr)

	body := v.Encode()
	reqRes, err := t.doRequest(DidApiDisApprove, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset did for disapprove addr fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal did response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *DidClient) ListSceneAddr(uid int64, groupIds string) (*ListAddrResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if uid < 1 || groupIds == "" {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", uid))
	v.Set("group_ids", groupIds)

	body := v.Encode()
	reqRes, err := t.doRequest(DidApiListSceneAccByUid, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset did for list addr fail.err:%v", err)
	}
	var result ListAddrResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal did response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *DidClient) HasAddr(uid int64, addr string) (*GetAccByResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if uid < 1 || addr == "" {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", uid))
	v.Set("addr", addr)

	body := v.Encode()
	reqRes, err := t.doRequest(DidApiHasAddrByUid, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset did for has addr fail.err:%v", err)
	}
	var result GetAccByResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal did response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *DidClient) GetAddrAcc(addr string) (*GetAddrAccResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if addr == "" {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("addr", addr)

	body := v.Encode()
	reqRes, err := t.doRequest(DidApiGetAddrAcc, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset did for get addr acc fail.err:%v", err)
	}
	var result GetAddrAccResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal did response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *DidClient) GetAccByUid(uid, groupId int64) (*GetAccByResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if uid < 1 || groupId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("group_id", fmt.Sprintf("%d", groupId))
	v.Set("uid", fmt.Sprintf("%d", uid))

	body := v.Encode()
	reqRes, err := t.doRequest(DidApiGetAccByUid, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset did for get addr by unionid fail.err:%v", err)
	}
	var result GetAccByResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal did response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// post
func (t *DidClient) doRequest(api string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *DidClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
