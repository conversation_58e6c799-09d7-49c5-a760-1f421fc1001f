package did

import (
	"fmt"
	"net/http"
)

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000
)

const (
	DidApiGetUinfo          = "/internal/did/v1/getuinfo"
	DidApiBindByMnemonic    = "/internal/did/v1/bindbymnem"
	DidApiGetAccByAddr      = "/internal/did/v1/getaccbyaddr"
	DidApiGetAccByUid       = "/internal/did/v1/getaccbyuid"
	DidApiListAccByUid      = "/internal/did/v1/listaccbyuid"
	DidApiDisApprove        = "/internal/did/v1/disapprove"
	DidApiListSceneAccByUid = "/internal/did/v1/listsceneaccbyuid"
	DidApiHasAddrByUid      = "/internal/did/v1/hasaddrbyuid"
	DidApiGetAddrAcc        = "/internal/did/v1/getaddracc"
	DidApiGetCloudAccInfo   = "/internal/did/v1/getcloudaccinfo"
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

type LoadOrStoreAccParam struct {
	AddrType int   `json:"addr_type"`
	GroupId  int64 `json:"group_id"`
	Uid      int64 `json:"uid"`
	MnemLang int   `json:"mnem_lang"`
	MnemStrg int   `json:"mnem_strg"`
}

func (t *LoadOrStoreAccParam) IsValid() error {
	if t.Uid < 1 || t.AddrType < 1 {
		return fmt.Errorf("LoadOrStoreAccParam param is invalid")
	}

	return nil
}

type LoadOrStoreAccResp struct {
	BaseResp
	Addr      string `json:"addr"`
	AccountId string `json:"account_id"`
	Mnemonic  string `json:"mnemonic"`
	IsNew     int    `json:"is_new"`
}

type BaseAcc struct {
	Addr     string `json:"addr"`
	AddrType int    `json:"addr_type"`
	Mnemonic string `json:"mnemonic"`
	GroupId  int64  `json:"group_id"`
}

type GetAccByResp struct {
	BaseResp
	Acc *BaseAcc `json:"acc"`
}

type ListAddrResp struct {
	BaseResp
	List []*BaseAcc `json:"list"`
}

type GetAddrAccResp struct {
	BaseResp
	Acc *AccountAcc `json:"acc"`
}

type AccountAcc struct {
	Addr       string `json:"addr"`
	MnemLang   int    `json:"mnem_lang"`
	MnemStrg   int    `json:"mnem_strg"`
	Mnemonic   string `json:"mnemonic"`
	ExtInfo    string `json:"ext_info"`
	CTime      int64  `json:"ctime"`
	DisApprove int    `json:"disapprove"`
	Uid        int64  `json:"uid"`
}

type GetCloudAccParam struct {
	Uid  int64  `json:"uid"`
	Addr string `json:"addr"`
}

func (t *GetCloudAccParam) IsValid() error {
	if t.Uid < 1 {
		return fmt.Errorf("GetCloudAccParam param is invalid")
	}
	return nil
}

type GetCloudAccResp struct {
	BaseResp
	AccountId string `json:"account_id"`
}
