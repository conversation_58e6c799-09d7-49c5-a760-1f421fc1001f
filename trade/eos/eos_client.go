package eos

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type EosClient struct {
	addr *addr.Addr
}

func NewEosClient(addr *addr.Addr) *EosClient {
	return &EosClient{addr}
}

func (t *EosClient) GetUserRank(param *GetUserRankParam) (*GetUserRankResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("type", fmt.Sprintf("%d", param.Type))
	v.Set("pid", fmt.Sprintf("%d", param.Pid))
	v.Set("uid", fmt.Sprintf("%d", param.Uid))

	body := v.Encode()
	reqRes, err := t.doRequest(EosApiGetUserRank, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request eos to get user rank fail.err:%v", err)
	}
	var result GetUserRankResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal eos response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *EosClient) ListRank(param *ListRankParam) (*ListRankResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("type", fmt.Sprintf("%d", param.Type))
	v.Set("pid", fmt.Sprintf("%d", param.Pid))
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	v.Set("cursor", param.Cursor)

	body := v.Encode()
	reqRes, err := t.doRequest(EosApiListRank, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request eos to list rank fail.err:%v", err)
	}
	var result ListRankResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal eos response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *EosClient) ListRecord(param *ListRecordParam) (*ListRecordResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("date", param.Date)
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	v.Set("cursor", param.Cursor)

	body := v.Encode()
	reqRes, err := t.doRequest(EosApiListRecord, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request eos to list record fail.err:%v", err)
	}
	var result ListRecordResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal eos response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *EosClient) GetDevoteConf() (*DevoteInfoResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	reqRes, err := t.doRequest(EosApiDevoteConf, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request eos to get devote info conf fail.err:%v", err)
	}
	var result DevoteInfoResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal eos response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *EosClient) ListAward(param *ListAwardParam) (*ListAwardResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("type", fmt.Sprintf("%d", param.Type))
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	v.Set("award_type", fmt.Sprintf("%d", param.AwardType))
	v.Set("cursor", param.Cursor)

	body := v.Encode()
	reqRes, err := t.doRequest(EosApiListAward, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request eos to list award fail.err:%v", err)
	}
	var result ListAwardResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal eos response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *EosClient) AwardDetail(param *AwardDetailParam) (*AwardDetailResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("award_id", fmt.Sprintf("%d", param.AwardId))
	v.Set("uid", fmt.Sprintf("%d", param.Uid))

	body := v.Encode()
	reqRes, err := t.doRequest(EosApiAwardDetail, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request eos to get user award detail fail.err:%v", err)
	}
	var result AwardDetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal eos response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *EosClient) ListNewUserExchange() (*ListExchangeResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	reqRes, err := t.doRequest(EosApiNUAwardConf, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request eos to get nu award conf fail.err:%v", err)
	}
	var result ListExchangeResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal eos response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *EosClient) ExchangeNewUserAward(param *ExchangeAwardParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}

	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("award_id", fmt.Sprintf("%d", param.AwardId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("addr", param.Addr)

	body := v.Encode()
	reqRes, err := t.doRequest(EosApiExchangeAward, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request eos to exchange nu award fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal eos response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *EosClient) ListRankAward() (*ListRankAwardResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	reqRes, err := t.doRequest(EosApiRankAwardConf, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request eos to get rank award conf fail.err:%v", err)
	}
	var result ListRankAwardResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal eos response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *EosClient) InsertAward(param *InsertAwardParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	thumb, _ := json.Marshal(param.Thumb)

	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("award_id", fmt.Sprintf("%d", param.AwardId))
	v.Set("award_type", fmt.Sprintf("%d", param.AwardType))
	v.Set("status", fmt.Sprintf("%d", param.Status))
	v.Set("stime", fmt.Sprintf("%d", param.Stime))
	v.Set("etime", fmt.Sprintf("%d", param.Etime))
	v.Set("title", param.Title)
	v.Set("thumb", string(thumb))
	v.Set("short_desc", param.ShortDesc)
	v.Set("award_info", param.AwardInfo)
	v.Set("award_sk", param.AwardSK)

	body := v.Encode()
	reqRes, err := t.doRequest(EosApiInsertAward, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request eos to insert award fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal eos response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *EosClient) RecvAward(uid, awardId int64) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("award_id", fmt.Sprintf("%d", awardId))
	v.Set("uid", fmt.Sprintf("%d", uid))

	body := v.Encode()
	reqRes, err := t.doRequest(EosApiRecvAward, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request eos to recv award fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal eos response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *EosClient) CountInviters(uid int64) (*CountInvResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", uid))

	body := v.Encode()
	reqRes, err := t.doRequest(EosApiCountInviters, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request eos to count inviters fail.err:%v", err)
	}
	var result CountInvResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal eos response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// post
func (t *EosClient) doRequest(api string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *EosClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
