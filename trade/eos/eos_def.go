package eos

import (
	"net/http"
)

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000
)

const (
	RecordMaxLimit     = 30
	RecordDefaultLimit = 20

	RankMaxLimit     = 20
	RankDefaultLimit = 10

	AwardMaxLimit     = 30
	AwardDefaultLimit = 20
)

const (
	EosApiGetUserRank   = "/internal/eos/v1/rank"
	EosApiListRank      = "/internal/eos/v1/listrank"
	EosApiListRecord    = "/internal/eos/v1/listrecord"
	EosApiDevoteConf    = "/internal/eos/v1/devoteconf"
	EosApiCountInviters = "/internal/eos/v1/countinviters"

	EosApiListAward     = "/internal/award/v1/listaward"
	EosApiAwardDetail   = "/internal/award/v1/queryaward"
	EosApiExchangeAward = "/internal/award/v1/exchangeaward"
	EosApiNUAwardConf   = "/internal/award/v1/nuawardconf"
	EosApiRankAwardConf = "/internal/award/v1/rankawardconf"
	EosApiInsertAward   = "/internal/award/v1/insertaward"
	EosApiRecvAward     = "/internal/award/v1/recvaward"
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

type GetUserRankParam struct {
	Type int   `json:"type"`
	Pid  int64 `json:"pid"`
	Uid  int64 `json:"uid"`
}

func (t *GetUserRankParam) Valid() bool {
	if t.Uid < 1 {
		return false
	}
	return true
}

type GetUserRankResp struct {
	BaseResp
	RankDetail
}

type RankDetail struct {
	Rank    int64  `json:"rank"`
	Uname   string `json:"uname"`
	Photo   string `json:"photo"`
	Balance int64  `json:"balance"`
}

type ListRankParam struct {
	Type   int    `json:"type"`
	Pid    int64  `json:"pid"`
	Limit  int    `json:"limit"`
	Cursor string `json:"cursor"`
}

type ListRankResp struct {
	BaseResp
	List    []*RankDetail `json:"list"`
	Cursor  string        `json:"cursor"`
	HasMore int           `json:"has_more"`
}

type ListRecordParam struct {
	Uid    int64  `json:"uid"`
	Date   string `json:"date"`
	Limit  int    `json:"limit"`
	Cursor string `json:"cursor"`
}

type DevoteRecord struct {
	OpType int    `json:"op_type"`
	OpId   int64  `json:"op_id"`
	Points int64  `json:"points"`
	Desc   string `json:"desc"`
	Ctime  int64  `json:"ctime"`
}

type ListRecordResp struct {
	BaseResp
	List    []*DevoteRecord `json:"list"`
	Cursor  string          `json:"cursor"`
	HasMore int             `json:"has_more"`
}

type DevoteInfoResp struct {
	BaseResp
	BindDevote   int `json:"bind_devote"`
	OrderDevote  int `json:"order_devote"`
	UserMulti    int `json:"user_multi"`
	InviterMulti int `json:"inviter_multi"`
}

type ListAwardParam struct {
	Uid       int64  `json:"uid"`
	Type      int    `json:"type"`
	Limit     int    `json:"limit"`
	AwardType int    `json:"award_type"`
	Cursor    string `json:"cursor"`
}

type AwardSimpleInfo struct {
	AwardId   int64    `json:"award_id"`
	AwardType int      `json:"award_type"`
	Title     string   `json:"title"`
	Thumb     []string `json:"thumb"`
	ShortDesc string   `json:"s_desc"`
	Status    int      `json:"status"`
	Stime     int64    `json:"stime"`
	Etime     int64    `json:"etime"`
	Ctime     int64    `json:"ctime"`
}

type ListAwardResp struct {
	BaseResp
	List    []*AwardSimpleInfo `json:"list"`
	HasMore int                `json:"has_more"`
	Cursor  string             `json:"cursor"`
}

type AwardDetailParam struct {
	Uid     int64 `json:"uid"`
	AwardId int64 `json:"award_id"`
}

type AwardDetailResp struct {
	BaseResp
	AwardId   int64       `json:"award_id"`
	Title     string      `json:"title"`
	AwardType int         `json:"award_type"`
	Thumb     []string    `json:"thumb"`
	AwardInfo interface{} `json:"award_info"`
	AwardSK   string      `json:"award_sk"`
}

type ExchangeAssetInfo struct {
	Title     string   `json:"title"`
	Thumb     []string `json:"thumb"`
	AssetId   int64    `json:"asset_id"`
	ActId     int      `json:"act_id"`
	ShortDesc string   `json:"short_desc"`
	Amount    int      `json:"amount"`
	Price     int      `json:"price"`
}

type ListExchangeResp struct {
	BaseResp
	List []*ExchangeAssetInfo `json:"list"`
}

type ExchangeAwardParam struct {
	Uid     int64  `json:"uid"`
	Addr    string `json:"addr"`
	AwardId int64  `json:"award_id"`
	AssetId int64  `json:"asset_id"`
}

func (t *ExchangeAwardParam) Valid() bool {
	return t.Uid > 0 && t.Addr != "" && t.AwardId > 0 && t.AssetId > 0
}

type RankAwardInfo struct {
	Id        int      `json:"id"`
	Title     string   `json:"title"`
	Thumb     []string `json:"thumb"`
	ShortDesc string   `json:"short_desc"`
	Value     string   `json:"value"`
}

type ListRankAwardResp struct {
	BaseResp
	List      []*RankAwardInfo `json:"list"`
	Period    int64            `json:"period"`
	Title     string           `json:"title"`
	Stime     int64            `json:"stime"`
	Etime     int64            `json:"etime"`
	AwardName string           `json:"award_name"`
	GrantTime int64            `json:"gtime"`
}

type InsertAwardParam struct {
	Uid       int64    `json:"uid"`
	AwardId   int64    `json:"award_id"`
	AwardType int      `json:"award_type"`
	Status    int      `json:"status"`
	Stime     int64    `json:"stime"`
	Etime     int64    `json:"etime"`
	Title     string   `json:"title"`
	Thumb     []string `json:"thumb"`
	ShortDesc string   `json:"short_desc"`
	AwardInfo string   `json:"award_info"`
	AwardSK   string   `json:"award_sk"`
}

type CountInvResp struct {
	BaseResp
	Count int `json:"count"`
}
