package horae

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
	"icode.baidu.com/baidu/blockchain/xasset-golib/utils"
)

type HoraeFunc func(hc *HoraeClient) (interface{}, *RequestRes, error)

func RegisterNRunWithNum(getAddr func() (*addr.Addr, error), f HoraeFunc, tryTime int) (resp interface{}, reqResp *RequestRes, err error) {
	if f == nil {
		return nil, nil, errors.New("need horae function")
	}
	if getAddr == nil {
		return nil, nil, errors.New("need addr function")
	}
	var horaeAddr *addr.Addr
	for i := 0; i < tryTime; i++ {
		horaeAddr, err = getAddr()
		if err != nil {
			continue
		}
		client := NewHoraeClient(horaeAddr)
		resp, reqResp, err = f(client)
		if err == nil {
			break
		}
	}
	if err != nil {
		return nil, nil, err
	}
	return resp, reqResp, nil
}

func run(hc *HoraeClient, f HoraeFunc) (interface{}, *RequestRes, error) {
	return f(hc)
}

func RegisterNRun(clientHandles []*HoraeClient, f HoraeFunc) (resp interface{}, reqResp *RequestRes, err error) {
	if f == nil {
		return nil, nil, errors.New("need horae function")
	}
	if len(clientHandles) == 0 {
		return nil, nil, errors.New("invalid horae handles")
	}
	for _, handle := range clientHandles {
		resp, reqResp, err = run(handle, f)
		if err == nil {
			break
		}
	}
	if err != nil {
		return nil, nil, err
	}
	return resp, reqResp, nil
}

type HoraeClient struct {
	addr *addr.Addr
}

func NewHoraeClient(addr *addr.Addr) *HoraeClient {
	return &HoraeClient{addr}
}

func (t *HoraeClient) CreateHoraeAsset(param *CreateAssetParam) (*CreateAssetResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.IsValid() {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("nonce", fmt.Sprintf("%d", param.Nonce))
	v.Set("price", fmt.Sprintf("%d", param.Price))
	v.Set("amount", fmt.Sprintf("%d", param.Amount))
	v.Set("asset_info", param.AssetInfo)
	v.Set("create_addr", param.CreateAddr)
	v.Set("create_sign", param.CreateSign)
	v.Set("create_pkey", param.CreatePkey)
	if param.ViewType > 0 {
		v.Set("view_type", fmt.Sprintf("%d", param.ViewType))
	}
	if param.UserId > 0 {
		v.Set("user_id", fmt.Sprintf("%d", param.UserId))
	}
	if param.EvidenceExt != "" {
		v.Set("evidence_ext", param.EvidenceExt)
	}
	if param.AssetParam != "" {
		v.Set("asset_param", param.AssetParam)
	}
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiCreateAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for create asset fail.err:%v", err)
	}
	var result CreateAssetResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ModifyAsset(param *ModifyAssetParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.IsValid() {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("nonce", fmt.Sprintf("%d", param.Nonce))
	v.Set("create_addr", param.CreateAddr)
	v.Set("create_sign", param.CreateSign)
	v.Set("create_pkey", param.CreatePkey)
	v.Set("price", fmt.Sprintf("%d", param.Price))
	if param.ViewType >= 0 {
		v.Set("view_type", fmt.Sprintf("%d", param.ViewType))
	}
	if param.Amount >= 0 {
		v.Set("amount", fmt.Sprintf("%d", param.Amount))
	}
	if param.AssetInfo != "" {
		v.Set("asset_info", param.AssetInfo)
	}
	if param.EvidenceExt != "" {
		v.Set("evidence_ext", param.EvidenceExt)
	}
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiModifyAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae fro modify asset fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) PublishAsset(param *PublishAssetParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.IsValid() {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("evidence_type", fmt.Sprintf("%d", param.EvidenceType))
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiPublishAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for publish asset fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) GrantShard(param *TransferShardParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.IsValid() {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("shard_id", fmt.Sprintf("%d", param.ShardId))
	v.Set("price", fmt.Sprintf("%d", param.Price))
	v.Set("addr", param.Addr)
	v.Set("sign", param.Sign)
	v.Set("pkey", param.PKey)
	v.Set("nonce", fmt.Sprintf("%d", param.Nonce))
	v.Set("to_addr", param.ToAddr)
	if param.ToUserId > 0 {
		v.Set("to_userid", fmt.Sprintf("%d", param.ToUserId))
	}
	if param.ShardParam != "" {
		v.Set("shard_param", fmt.Sprintf("%s", param.ShardParam))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiGrantShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for grant shard fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) TransferShard(param *TransferShardParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.IsValid() {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("shard_id", fmt.Sprintf("%d", param.ShardId))
	v.Set("price", fmt.Sprintf("%d", param.Price))
	v.Set("addr", param.Addr)
	v.Set("sign", param.Sign)
	v.Set("pkey", param.PKey)
	v.Set("nonce", fmt.Sprintf("%d", param.Nonce))
	v.Set("to_addr", param.ToAddr)
	if param.ToUserId > 0 {
		v.Set("to_userid", fmt.Sprintf("%d", param.ToUserId))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiTransShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for transfer shard fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) SellAsset(appId, assetId int64) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || assetId < 1 {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiSellAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for sell asset fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) OffShelveAsset(appId, assetId int64) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || assetId < 1 {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiOffShelve, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for offshelve asset fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) DeleteAsset(appId, assetId int64) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || assetId < 1 {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiDeleteAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for delete asset fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) BanAsset(appId, assetId int64) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || assetId < 1 {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiBanAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for ban asset fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) QueryAssetInfo(appId, assetId int64) (*QueryAssetResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || assetId < 1 {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiQueryAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request horae asset for query asset fail.err:%v", err)
	}
	var result QueryAssetResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListAssetsByAddr(appId int64, addr string, status, page, limit int) (*ListAssetsByPageResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || addr == "" || page < 1 {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("addr", addr)
	v.Set("page", fmt.Sprintf("%d", page))
	if status > 0 {
		v.Set("status", fmt.Sprintf("%d", status))
	}
	if limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", limit))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiListAssetsByAddr, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for list asset by addr fail.err:%v", err)
	}
	var result ListAssetsByPageResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListAssetsByStatus(appId int64, addr string, status, page, limit int) (*ListAssetsByPageResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || addr == "" {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("addr", addr)
	v.Set("status", fmt.Sprintf("%d", status))
	v.Set("page", fmt.Sprintf("%d", page))
	if limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", limit))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiListByStatus, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request horae asset for listbystatus fail.err:%v", err)
	}
	var result ListAssetsByPageResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListAssetsByIds(appId int64, assetIds []int64) (*ListAssetsByPageResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || len(assetIds) < 1 {
		return nil, nil, utils.ERROR_PARAMETER
	}
	assetIdsStr, err := json.Marshal(assetIds)
	if err != nil {
		return nil, nil, errors.New("param error, asset_ids json encode failed")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("asset_ids", string(assetIdsStr))
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiListByIds, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request horae asset for listbyids fail.err:%v", err)
	}
	var result ListAssetsByPageResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) SearchByTitle(appId int64, keyword, addr string) (*ListAssetsByPageResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || keyword == "" {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("keyword", keyword)
	v.Set("addr", addr)
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiSearchTitle, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request horae asset for searchbytitle fail.err:%v", err)
	}
	var result ListAssetsByPageResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) QueryShard(appId, assetId, shardId int64) (*QueryShardResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || assetId < 1 || shardId < 1 {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	v.Set("shard_id", fmt.Sprintf("%d", shardId))
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiQueryShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for query shard fail.err:%v", err)
	}
	var result QueryShardResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListShardsByAddr(appId int64, addr string,
	page, limit int, assetId int64) (*ListShardsByPageResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || addr == "" {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("addr", addr)
	v.Set("page", fmt.Sprintf("%d", page))
	if limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", limit))
	}
	if assetId > 0 {
		v.Set("asset_id", fmt.Sprintf("%d", assetId))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiListShardsByAddr, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for list shards by addr fail.err:%v", err)
	}
	var result ListShardsByPageResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListXShards(param *ListXShardParam) (*IndexListShardResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("addr", param.Addr)
	v.Set("cursor", param.Cursor)
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}
	if param.DaysFilter > 0 {
		v.Set("time", fmt.Sprintf("%d", param.DaysFilter))
	}
	if param.Type >= 0 {
		v.Set("type", fmt.Sprintf("%d", param.Type))
	}
	if param.Title != "" {
		v.Set("title", param.Title)
	}
	if param.AppId > 0 {
		v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiListXShards, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for list xshards fail.err:%v", err)
	}
	var result IndexListShardResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) HasAsset(addr, assetIds, appIds string) (*HasAssetResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("addr", addr)
	v.Set("asset_ids", assetIds)
	v.Set("app_ids", appIds)
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiHasAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for has asset fail.err:%v", err)
	}
	var result HasAssetResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) HasOneOfAssets(addr, assetIds, appIds string) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("addr", addr)
	v.Set("asset_ids", assetIds)
	v.Set("app_ids", appIds)
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiHasOneOfAssets, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for has one_of_assets fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListShardsByAsset(appId, assetId int64, cursor string, limit int) (*ListShardsByCursorResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || assetId < 1 {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	v.Set("cursor", cursor)
	if limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", limit))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiListShardsByAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for list shards by asset fail.err:%v", err)
	}
	var result ListShardsByCursorResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListShardsByShardIds(appId int64, shardList string) (*ListShardsByCursorResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || shardList == "" {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("shard_list", shardList)
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiListShardsBySdIds, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for list shards by shard ids fail.err:%v", err)
	}
	var result ListShardsByCursorResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListShardsByAssetIds(appId int64, address string, assetIds []int64) (*ListShardsByPageResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || address == "" || len(assetIds) < 1 {
		return nil, nil, utils.ERROR_PARAMETER
	}
	jsAstIds, err := json.Marshal(assetIds)
	if err != nil {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("address", address)
	v.Set("asset_ids", string(jsAstIds))
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiListSdsByAstIds, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for list shards by asset_ids fail.err:%v", err)
	}
	var result ListShardsByPageResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListAssetByHistory(appId, assetId int64, page, limit int) (*ListTransLogByPageResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || assetId < 1 || page < 1 {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	v.Set("page", fmt.Sprintf("%d", page))
	if limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", limit))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiListAstHistory, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for list asset history fail.err:%v", err)
	}
	var result ListTransLogByPageResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListShardByHistory(appId, assetId, shardId int64, page, limit int) (*ListTransLogByPageResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || assetId < 1 || shardId < 1 || page < 1 {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	v.Set("shard_id", fmt.Sprintf("%d", shardId))
	v.Set("page", fmt.Sprintf("%d", page))
	if limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", limit))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiListAstHistory, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for list asset history fail.err:%v", err)
	}
	var result ListTransLogByPageResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) FreezeAsset(param *FreezeAssetParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("addr", param.Addr)
	v.Set("sign", param.Sign)
	v.Set("pkey", param.PKey)
	v.Set("nonce", fmt.Sprintf("%d", param.Nonce))
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiFreezeAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for freeze asset fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ConsumeShard(param *ConsumeShardParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("shard_id", fmt.Sprintf("%d", param.ShardId))
	v.Set("nonce", fmt.Sprintf("%d", param.Nonce))
	v.Set("user_addr", param.UAddr)
	v.Set("user_sign", param.USign)
	v.Set("user_pkey", param.UPKey)
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiConsumeShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for consume shard fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) BatchGrantShards(param *BatchGrantParam) (*BatchGrantResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("to_addr", param.ToAddr)
	v.Set("addr", param.Addr)
	v.Set("pkey", param.Pkey)
	if param.ToUserId > 0 {
		v.Set("to_userid", fmt.Sprintf("%d", param.ToUserId))
	}
	astList, err := json.Marshal(param.AstList)
	if err != nil {
		return nil, nil, err
	}
	v.Set("ast_list", string(astList))
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiBatchGrant, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for batch grant shards fail.err:%v", err)
	}
	var result BatchGrantResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) SrdsInCir(appId, assetId int64) (*SrdsInCirResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiSrdsInCir, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for srdscir fail.err:%v", err)
	}
	var result SrdsInCirResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) SceneListShardByAddr(appIds, addr, cursor string,
	limit int) (*SceneListShardsResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appIds == "" || addr == "" {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_ids", appIds)
	v.Set("addr", addr)
	v.Set("cursor", cursor)
	if limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", limit))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(SceneApiListShardByAddr, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for scene list shards by addr fail.err:%v", err)
	}
	var result SceneListShardsResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) SceneListDiffByAddr(appIds, addr, cursor, opTyps string,
	limit int) (*DiffListShardResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appIds == "" || addr == "" {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_ids", appIds)
	v.Set("addr", addr)
	v.Set("cursor", cursor)
	if opTyps != "" {
		v.Set("op_types", opTyps)
	}
	if limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", limit))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(SceneApiListDiffByAddr, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for scene list diff by addr fail.err:%v", err)
	}
	var result DiffListShardResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) AddAssetTag(assetId, tagId int64) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	v.Set("tag_id", fmt.Sprintf("%d", tagId))
	body := v.Encode()
	reqRes, err := t.doRequest(TagApiAddAssetTag, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for add asset tag fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) DelAssetTag(assetId, tagId int64) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	v.Set("tag_id", fmt.Sprintf("%d", tagId))
	body := v.Encode()
	reqRes, err := t.doRequest(TagApiDelAssetTag, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for del asset tag fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) GetAssetTag(assetId int64) (*GetAssetTagResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	body := v.Encode()
	reqRes, err := t.doRequest(TagApiGetAssetTag, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for get tags by asset fail.err:%v", err)
	}
	var result GetAssetTagResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListShardsbyTag(addr, cursor string, tagId int64, limit int) (*IndexListShardbyTagResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if addr == "" {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("addr", addr)
	v.Set("cursor", cursor)
	v.Set("tag_id", fmt.Sprintf("%d", tagId))
	if limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", limit))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(TagApiListShardsbyTag, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for scene list diff by addr fail.err:%v", err)
	}
	var result IndexListShardbyTagResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) CreateCollect(param *CreateCollectParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("addr", param.Addr)
	v.Set("type", fmt.Sprintf("%d", param.Type))
	v.Set("title", param.Title)
	body := v.Encode()
	reqRes, err := t.doRequest(CollectApiCreate, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for create collect fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) DeleteCollect(param *DelCollectParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("addr", param.Addr)
	body := v.Encode()
	reqRes, err := t.doRequest(CollectApiDelete, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for delete collect fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) SetCollectTitle(param *UpdateCollectParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("addr", param.Addr)
	v.Set("title", param.Title)
	body := v.Encode()
	reqRes, err := t.doRequest(CollectApiSetTitle, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for set collect title fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) SetCollectCover(param *UpdateCollectParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("addr", param.Addr)
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("shard_id", fmt.Sprintf("%d", param.ShardId))
	body := v.Encode()
	reqRes, err := t.doRequest(CollectApiSetCover, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for set collect cover fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) QueryCollect(param *QueryCollectParam) (*QueryCollectResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("addr", param.Addr)
	body := v.Encode()
	reqRes, err := t.doRequest(CollectApiQuery, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for query collect fail.err:%v", err)
	}
	var result QueryCollectResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListCollect(param *ListCollectParam) (*ListCollectResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("addr", param.Addr)
	v.Set("cursor", param.Cursor)
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	if param.DaysFilter > 0 {
		v.Set("time", fmt.Sprintf("%d", param.DaysFilter))
	}
	if param.Type >= 0 {
		v.Set("type", fmt.Sprintf("%d", param.Type))
	}
	if len(param.Types) > 0 {
		v.Set("types", param.Types)
	}
	v.Set("sort", fmt.Sprintf("%d", param.Sort))
	body := v.Encode()
	reqRes, err := t.doRequest(CollectApiListCollect, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for list collect fail.err:%v", err)
	}
	var result ListCollectResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) CountCollect(param *CountCollectParam) (*CountCollectResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("addr", param.Addr)
	if len(param.Types) > 0 {
		v.Set("types", param.Types)
	}
	body := v.Encode()
	reqRes, err := t.doRequest(CollectApiCountCollect, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for count collect fail.err:%v", err)
	}
	var result CountCollectResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) AddCollectAssets(param *UpdateCollectAssetParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("addr", param.Addr)
	v.Set("assets", param.Assets)
	body := v.Encode()
	reqRes, err := t.doRequest(CollectApiAddAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for add collect assets fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) DelCollectAssets(param *UpdateCollectAssetParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("addr", param.Addr)
	v.Set("assets", param.Assets)
	body := v.Encode()
	reqRes, err := t.doRequest(CollectApiDelAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for delete collect assets fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListCollectAssets(param *ListCollectAssetParam) (*ListCollectAssetResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("sort", fmt.Sprintf("%d", param.Sort))
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("cursor", param.Cursor)
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	body := v.Encode()
	reqRes, err := t.doRequest(CollectApiListAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for list collect asset fail.err:%v", err)
	}
	var result ListCollectAssetResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListCollectAggAssets(param *ListCollectAggParam) (*ListCollectAggResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("sort", fmt.Sprintf("%d", param.Sort))
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("cursor", param.Cursor)
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	body := v.Encode()
	reqRes, err := t.doRequest(CollectApiListAggAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for list collect agg asset fail.err:%v", err)
	}
	var result ListCollectAggResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) SelectBoxAst(param *SelectBoxAstParam) (*SelectBoxResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("shard_id", fmt.Sprintf("%d", param.ShardId))
	v.Set("address", param.Address)
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiSelBoxAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for select box asset fail.err:%v", err)
	}
	var result SelectBoxResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) GrantBox(param *GrantBoxParam) (*GrantBoxResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("consume_nonce", fmt.Sprintf("%d", param.ConsumeNonce))
	v.Set("grant_nonce", fmt.Sprintf("%d", param.GrantNonce))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("user_id", fmt.Sprintf("%d", param.UserId))
	v.Set("token", param.Token)
	v.Set("user_addr", param.UAddr)
	v.Set("user_sign", param.USign)
	v.Set("user_pkey", param.UPkey)
	v.Set("create_addr", param.CAddr)
	v.Set("create_sign", param.CSign)
	v.Set("create_pkey", param.CPkey)
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiGrantBox, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for grant box asset fail.err:%v", err)
	}
	var result GrantBoxResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) SelectMaterial(param *SelectMaterialParam) (*SelectMaterialResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("strg_no", fmt.Sprintf("%d", param.StrgNo))
	v.Set("addr", param.Addr)
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiSelectMaterial, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for select material fail.err:%v", err)
	}
	var result SelectMaterialResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ComposeShard(param *ComposeParam) (*ComposeShardResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("strg_no", fmt.Sprintf("%d", param.StrgNo))
	v.Set("nonce", fmt.Sprintf("%d", param.Nonce))
	v.Set("addr", param.Addr)
	v.Set("pkey", param.Pkey)
	v.Set("sign", param.Sign)
	v.Set("uaddr", param.UAddr)
	v.Set("upkey", param.UPkey)
	v.Set("ast_list", param.AstList)
	v.Set("token", param.Token)
	v.Set("user_id", fmt.Sprintf("%d", param.UserId))

	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiComposeShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for compose shard fail.err:%v", err)
	}
	var result ComposeShardResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) DivideShard(param *ComposeParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("nonce", fmt.Sprintf("%d", param.Nonce))
	v.Set("addr", param.Addr)
	v.Set("pkey", param.Pkey)
	v.Set("sign", param.Sign)
	v.Set("uaddr", param.UAddr)
	v.Set("upkey", param.UPkey)
	v.Set("ast_list", param.AstList)
	v.Set("token", param.Token)

	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiDivideShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for divide shard fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) CountShardsByAstAddr(appId int64, address string, assetIds []int64) (*CountShardsResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || address == "" || len(assetIds) < 1 {
		return nil, nil, utils.ERROR_PARAMETER
	}
	assetIdsStr, err := json.Marshal(assetIds)
	if err != nil {
		return nil, nil, errors.New("param error, asset_ids json encode failed")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("address", address)
	v.Set("asset_ids", string(assetIdsStr))
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiCountSdsByAddr, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for count shards by ast addr fail.err:%v", err)
	}
	var result CountShardsResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) UpgradeAst(param *UpgradeAstParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.IsValid() {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("param", param.AssetParam)
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiUpgradeAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for upgrade asset param fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) UpgradeSds(param *UpgradeSdsParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.IsValid() {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("shard_id", fmt.Sprintf("%d", param.ShardId))
	v.Set("param", param.ShardParam)
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiUpgradeSds, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for upgrade shard param fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) FreezeShard(param *LockOrFreezeShardParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("shard_id", fmt.Sprintf("%d", param.ShardId))
	v.Set("addr", param.UAddr)

	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiFreezeShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for freeze shard fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) UnFreezeShard(param *LockOrFreezeShardParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("shard_id", fmt.Sprintf("%d", param.ShardId))
	v.Set("addr", param.UAddr)

	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiUnfreezeShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for unfreeze shard fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) LockShard(param *LockOrFreezeShardParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("shard_id", fmt.Sprintf("%d", param.ShardId))
	v.Set("op_type", fmt.Sprintf("%d", param.OpType))
	v.Set("user_addr", param.UAddr)

	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiLockShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for lock shard fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) BatchFreezeShard(param *BatchFreezeShardParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("addr", param.Addr)
	v.Set("sds_list", param.SdsList)

	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiBatchfreezeShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for batch freeze shard fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) BatchUnfreezeShard(param *BatchFreezeShardParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("addr", param.Addr)
	v.Set("sds_list", param.SdsList)

	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiBatchUnfreezeShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for batch unfreeze shard fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) BatchCumfreShard(param *BatchCumfreShardParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("addr", param.Addr)
	v.Set("pkey", param.Pkey)
	v.Set("sds_list", param.SdsList)

	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiBatchcumfresds, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for batch unfreeze shard fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) BatchConsumeShard(param *BatchConsumeParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("addr", param.Addr)
	v.Set("pkey", param.Pkey)
	v.Set("sds_list", param.SdsList)

	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiBatchConsume, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for batch consume shard fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) CountNearExpire(param *CountNearExpireParam) (*CountNearExpireResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("addr", param.Addr)
	v.Set("asset_cate", fmt.Sprintf("%d", param.AssetCate))
	v.Set("expire_time", fmt.Sprintf("%d", param.ExpireTime))

	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiCountNearExpire, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for count near expire shards fail.err:%v", err)
	}
	var result CountNearExpireResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListShardsByCate(param *ListShardsByCateParam) (*ListShardsByCateResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, utils.ERROR_PARAMETER
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("addr", param.Addr)
	v.Set("asset_cate", fmt.Sprintf("%d", param.AssetCate))
	v.Set("sort_key", fmt.Sprintf("%d", param.SortKey))
	v.Set("cursor", param.Cursor)
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}

	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiListSdsByCate, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for list shards by cate fail.err:%v", err)
	}
	var result ListShardsByCateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) CountAggrShards(param *CountAssetsTypeParam) (*CountAssetsTypeResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("address", param.Addr)
	v.Set("type", fmt.Sprintf("%d", param.Type))
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiCountAggrShards, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for countsds by type fail.err:%v", err)
	}
	var result CountAssetsTypeResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListAggrAst(param *AggrAstListParam) (*AggrAstListResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("address", param.Addr)
	v.Set("title", param.Title)
	v.Set("type", fmt.Sprintf("%d", param.Type))
	v.Set("mono", fmt.Sprintf("%d", param.MonoIncr))
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	v.Set("cursor", param.Cursor)
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiListAggrAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for listaggrast fail.err:%v", err)
	}
	var result AggrAstListResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *HoraeClient) ListSdsByAstV3(param *ListSdsByAstV3Param) (*ListSdsByAstV3Resp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("address", param.Addr)
	v.Set("mono", fmt.Sprintf("%d", param.MonoIncr))
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	v.Set("cursor", param.Cursor)
	body := v.Encode()
	reqRes, err := t.doRequest(HoraeApiListSdsByAstV3, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for list_shards_by_asset_v3 by type fail.err:%v", err)
	}
	var result ListSdsByAstV3Resp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// post
func (t *HoraeClient) doRequest(api string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *HoraeClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
