package horae

import (
	"encoding/json"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/storage/bos"
	"icode.baidu.com/baidu/blockchain/xasset-golib/utils"
)

var hosts string = "*************:8310"
var appId int64 = 300100
var assetId = int64(utils.GenIdHelp(uint64(appId), 0))
var shardId = int64(utils.GenIdHelp(uint64(appId), 0))

var bosAddr = "https://cdn.bcebos.com"
var bosAk = "xxx"
var bosSk = "xxx"

var DevAddress = ""
var DevPrivateKey = ``
var DevPublicKey = ``

type AssetInfo struct {
	AssetCate int      `json:"asset_cate,omitempty"`
	Title     string   `json:"title,omitempty"`
	Thumb     []string `json:"thumb,omitempty"`
	ShortDesc string   `json:"short_desc,omitempty"`
	ImgDesc   []string `json:"img_desc,omitempty"`
	AssetUrl  []string `json:"asset_url,omitempty"`
	LongDesc  string   `json:"long_desc,omitempty"`
	AssetExt  string   `json:"asset_ext,omitempty"`
	GroupId   int64    `json:"group_id,omitempty"`
}

func CreateSingleAsset() *CreateAssetParam {
	assetNode := &AssetInfo{
		AssetCate: 1,
		Title:     "虾图",
		Thumb:     []string{"bos_v1://xasset-offline/horse.jpg/1000_500"},
		ShortDesc: "好多好多虾",
		LongDesc:  "长描述长描述长描述",
		ImgDesc:   []string{"bos_v1://xasset-offline/horse.jpg/1000_500"},
		AssetUrl:  []string{"bos_v1://xasset-offline/horse.jpg/1000_500"},
	}
	assetInfo, _ := json.Marshal(assetNode)
	return &CreateAssetParam{
		AssetId:    assetId,
		AppId:      appId,
		Nonce:      int64(utils.GenRequestId()),
		AssetInfo:  string(assetInfo),
		Amount:     100,
		CreateAddr: "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY",
		CreateSign: "3045022049bf3aed2d15f1034dddb56634a4739d13fb1b6ea3c48fd82e16f8c5274942490221008a2cd0a300f60f20324cd34675ca01d110a9b2f074f19bf369d40540789c7cc0",
		CreatePkey: "{\"Curvname\":\"P-256\",\"X\":36505150171354363400464126431978257855318414556425194490762274938603757905292,\"Y\":79656876957602994269528255245092635964473154458596947290316223079846501380076}",
	}
}

func TestCreateAsset(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewHoraeClient(a).CreateHoraeAsset(CreateSingleAsset())
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo, "asset_id", assetId)
}

func TestModifyAsset(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewHoraeClient(a).CreateHoraeAsset(CreateSingleAsset())
	if err != nil {
		fmt.Println(err, resp, reqInfo)
		return
	}

	assetNode := &AssetInfo{
		AssetCate: 1,
		Title:     "虾图",
		Thumb:     []string{"bos_v1://xasset-trade/shrimp.jpg/1000_500"},
		ShortDesc: "好多好多好多的虾。已更新",
		LongDesc:  "我是更新过的长描述",
		AssetExt:  `{"tags":["艺术品","收藏品"]}`,
	}
	assetInfo, _ := json.Marshal(assetNode)
	modifyAssetParam := &ModifyAssetParam{
		AppId:      appId,
		AssetId:    assetId,
		AssetInfo:  string(assetInfo),
		Nonce:      int64(utils.GenRequestId()),
		Amount:     500,
		Price:      -1,
		CreateAddr: "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY",
		CreateSign: "3045022049bf3aed2d15f1034dddb56634a4739d13fb1b6ea3c48fd82e16f8c5274942490221008a2cd0a300f60f20324cd34675ca01d110a9b2f074f19bf369d40540789c7cc0",
		CreatePkey: "{\"Curvname\":\"P-256\",\"X\":36505150171354363400464126431978257855318414556425194490762274938603757905292,\"Y\":79656876957602994269528255245092635964473154458596947290316223079846501380076}",
	}
	mResp, mReqInfo, err := NewHoraeClient(a).ModifyAsset(modifyAssetParam)
	if err != nil {
		fmt.Println(err, mReqInfo)
		return
	}
	fmt.Println(mResp, mReqInfo)
}

func TestQueryProductInfo(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewHoraeClient(a).QueryAssetInfo(appId, assetId)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestListAssetsByIds(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	assetIds := []int64{assetId}
	resp, reqInfo, err := NewHoraeClient(a).ListAssetsByIds(appId, assetIds)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestListAssetsByStatus(t *testing.T) {
	status := 0
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewHoraeClient(a).ListAssetsByStatus(appId, "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY", status, 1, 20)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestSearchByTitle(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewHoraeClient(a).SearchByTitle(appId, "试1", "nbU2WjhorE3rzqPR6fG1KGwwtHRuS7PDD")
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestGrantShard(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	param := &TransferShardParam{
		AppId:   appId,
		AssetId: assetId,
		ShardId: shardId,
		Addr:    "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY",
		Nonce:   int64(utils.GenRequestId()),
		Sign:    "3045022049bf3aed2d15f1034dddb56634a4739d13fb1b6ea3c48fd82e16f8c5274942490221008a2cd0a300f60f20324cd34675ca01d110a9b2f074f19bf369d40540789c7cc0",
		PKey:    "{\"Curvname\":\"P-256\",\"X\":36505150171354363400464126431978257855318414556425194490762274938603757905292,\"Y\":79656876957602994269528255245092635964473154458596947290316223079846501380076}",
		Price:   10010,
		ToAddr:  "SmJG3rH2ZzYQ9ojxhbRCPwFiE9y6pD1Co",
	}
	resp, reqInfo, err := NewHoraeClient(a).GrantShard(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestTransferShard(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	param := &TransferShardParam{
		AppId:   appId,
		AssetId: assetId,
		ShardId: shardId,
		Addr:    "SmJG3rH2ZzYQ9ojxhbRCPwFiE9y6pD1Co",
		Nonce:   int64(utils.GenRequestId()),
		Sign:    "3045022049bf3aed2d15f1034dddb56634a4739d13fb1b6ea3c48fd82e16f8c5274942490221008a2cd0a300f60f20324cd34675ca01d110a9b2f074f19bf369d40540789c7cc0",
		PKey:    "{\"Curvname\":\"P-256\",\"X\":12866043091588565003171939933628544430893620588191336136713947797738961176765,\"Y\":82755103183873558994270855453149717093321792154549800459286614469868720031056}",
		ToAddr:  "iYjtLcW6SVCiousAb5DFKWtWroahhEj4u",
	}
	resp, reqInfo, err := NewHoraeClient(a).TransferShard(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestListAssetsByAddr(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewHoraeClient(a).ListAssetsByAddr(appId, "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY", 0, 1, 10)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestQueryShard(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewHoraeClient(a).QueryShard(appId, assetId, shardId)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestListShardsByAddr(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewHoraeClient(a).ListShardsByAddr(appId, "SmJG3rH2ZzYQ9ojxhbRCPwFiE9y6pD1Co", 1, 10, 1905874352177714244)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestListShardsByAsset(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewHoraeClient(a).ListShardsByAsset(appId, assetId, "", 10)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestListAssetByHistory(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewHoraeClient(a).ListAssetByHistory(appId, assetId, 1, 10)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestListShardsByAssetIds(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewHoraeClient(a).ListShardsByAssetIds(
		appId,
		"aJV4Js1Nxyd8tiCmH4sYfYFjsmhVNWcUc",
		[]int64{
			120948478078915652,
			41880878938625092,
		},
	)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestBosV1Decoder(t *testing.T) {
	thumbStr := "100839/xasset-trade/horse.jpg/400_200"
	thumbObj := bosV1Decoder(thumbStr)
	fmt.Printf("thumb:%+v\n", thumbObj)
}

func TestDecodeStrToThumb(t *testing.T) {
	thumbStr := "bos_v1://xasset-trade/100839/horse.jpg/400_200"
	thumbObj := DecodeStrToThumb(thumbStr)
	fmt.Printf("thumb:%+v\n", thumbObj)
}

func TestStrToThumbMap(t *testing.T) {
	bc, err := bos.NewBosDlink(bosAddr, bosAk, bosSk, "xasset-trade")
	if err != nil {
		fmt.Println(err)
	}

	thumbStr := `[
        "bos_v1://xasset-trade/100839/draw_qmsht.jpeg",
        "bos_v1://xasset-trade/100839/draw_star.jpeg"
       ]`
	thumbs, err := StrToThumbMap(bc, thumbStr)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Printf("thumbs:%+v", thumbs)
}

func TestStrToImgDesc(t *testing.T) {
	bc, err := bos.NewBosDlink(bosAddr, bosAk, bosSk, "xasset-trade")
	if err != nil {
		fmt.Println(err)
	}

	thumbStr := `[
        "bos_v1://xasset-trade/100839/draw_qmsht.jpeg"
       ]`
	thumbs, err := StrToImgDesc(bc, thumbStr)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Printf("thumbs:%+v", thumbs)
}

func TestStrToAssetUrls(t *testing.T) {
	bc, err := bos.NewBosDlink(bosAddr, bosAk, bosSk, "xasset-offline")
	if err != nil {
		fmt.Println(err)
	}

	assetUrlStr := `[
        "bos_v1://xasset-offline/200100/3GP.3gp"
       ]`
	urls, err := StrToAssetUrls(bc, assetUrlStr)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Printf("asset_urls:%+v", urls)
}

func TestBatchGrantShards(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	param := &BatchGrantParam{
		AppId:    300100,
		ToAddr:   "dpr8vv841sVRT1aEA9riRTtgLpx7RxPPy",
		Addr:     DevAddress,
		Pkey:     DevPublicKey,
		ToUserId: 1419046482,
	}
	assetId := int64(66795331387692100)
	shards := make([]*BatchOpAsset, 0)
	for i := 0; i < 5; i++ {
		nonce := utils.GenNonce()
		node := &BatchOpAsset{
			AssetId: assetId,
			ShardId: nonce,
			Price:   100,
			Nonce:   nonce,
		}
		signMsg := fmt.Sprintf("%d%d", assetId, nonce)
		//compute sign xxxx
		node.Sign = signMsg
		shards = append(shards, node)
	}
	param.AstList = shards

	resp, _, err := NewHoraeClient(a).BatchGrantShards(param)
	if err != nil {
		fmt.Println(err)
		return
	}
	for _, v := range resp.List {
		fmt.Printf("%d_%d\n", v.AssetId, v.ShardId)
	}
}

func TestBatchConsumeShard(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	shardList := []*BatchOpAsset{
		&BatchOpAsset{
			AssetId: 46434025308722244,
			ShardId: 2167113074125567419,
			Nonce:   utils.GenNonce(),
			//compute sign Sprintf("%d%d", assetId, nonce)
			Sign: "fake_sign",
		},
		&BatchOpAsset{
			AssetId: 46434025308722244,
			ShardId: 5802659865915474340,
			Nonce:   utils.GenNonce(),
			//compute sign Sprintf("%d%d", assetId, nonce)
			Sign: "fake_sign",
		},
	}
	jsSdsList, _ := json.Marshal(shardList)
	param := &BatchConsumeParam{
		Addr:    "dpr8vv841sVRT1aEA9riRTtgLpx7RxPPy",
		Pkey:    "fake_pkey",
		SdsList: string(jsSdsList),
	}
	resp, _, err := NewHoraeClient(a).BatchConsumeShard(param)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Printf("%v", resp)
}

func TestListShardsByShardIds(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	shardList := `[{"asset_id":12722624397612100,"shard_id":1882428037948750696}]`
	resp, _, err := NewHoraeClient(a).ListShardsByShardIds(300100, shardList)
	if err != nil {
		fmt.Println(err)
		return
	}
	for _, v := range resp.List {
		fmt.Println(v.AssetCate)
		fmt.Println(v.ExpireTime)
	}
}
