package horae

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/blockchain/xasset-golib/storage/bos"
)

const (
	// bos链接默认有效期
	BosDlineExpireSecond = 600
)

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000
)

const (
	ShardsStatusOnChain      = 0  // 已上链
	ShardsStatusInIssue      = 1  // 发行中
	ShardsStatusTransfer     = 4  // 转移中
	ShardsStatusConsuming    = 5  // 核销中
	ShardsStatusConsumed     = 6  // 已核销
	ShardsStatusFrozen       = 7  // 已冻结
	ShardsStatusError        = 10 // 异常
	ShardsStatusRadarRefused = 11 // 拒绝上链访问
)

const (
	HoraeApiQueryAsset        = "/internal/horae/v1/queryasset"
	HoraeApiCreateAsset       = "/internal/horae/v1/createasset"
	HoraeApiModifyAsset       = "/internal/horae/v1/modifyasset"
	HoraeApiPublishAsset      = "/internal/horae/v1/publishasset"
	HoraeApiGrantShard        = "/internal/horae/v1/grant"
	HoraeApiTransShard        = "/internal/horae/v1/transfer"
	HoraeApiSellAsset         = "/internal/horae/v1/sellasset"
	HoraeApiOffShelve         = "/internal/horae/v1/offshelve"
	HoraeApiDeleteAsset       = "/internal/horae/v1/deleteasset"
	HoraeApiBanAsset          = "/internal/horae/v1/ban"
	HoraeApiListByStatus      = "/internal/horae/v1/listbystatus"
	HoraeApiListAssetsByAddr  = "/internal/horae/v1/listastbyaddr"
	HoraeApiListByIds         = "/internal/horae/v1/listbyids"
	HoraeApiSearchTitle       = "/internal/horae/v1/searchbytitle"
	HoraeApiQueryShard        = "/internal/horae/v1/querysds"
	HoraeApiListShardsByAddr  = "/internal/horae/v1/listsdsbyaddr"
	HoraeApiListShardsByAst   = "/internal/horae/v1/listsdsbyast"
	HoraeApiListShardsBySdIds = "/internal/horae/v1/listsdsbysdids"

	HoraeApiFreezeShard        = "/internal/horae/v1/freezesds"
	HoraeApiUnfreezeShard      = "/internal/horae/v1/unfreezesds"
	HoraeApiBatchfreezeShard   = "/internal/horae/v1/batchfreezesds"
	HoraeApiBatchUnfreezeShard = "/internal/horae/v1/batchunfreezesds"
	HoraeApiBatchcumfresds     = "/internal/horae/v1/batchcumfresds"
	HoraeApiLockShard          = "/internal/horae/v1/locksds"

	HoraeApiListSdsByAstIds = "/internal/horae/v1/listsdsbyastids"
	HoraeApiCountSdsByAddr  = "/internal/horae/v1/countsdsbyastids"
	HoraeApiListAstHistory  = "/internal/horae/v1/history"
	HoraeApiFreezeAsset     = "/internal/horae/v1/freeze"
	HoraeApiConsumeShard    = "/internal/horae/v1/consume"
	HoraeApiSrdsInCir       = "/internal/horae/v1/srdscir"
	HoraeApiListXShards     = "/internal/horae/v1/listidxsds"
	HoraeApiHasAsset        = "/internal/horae/v1/hasasset"
	HoraeApiHasOneOfAssets  = "/internal/horae/v1/hasoneof"
	HoraeApiBatchGrant      = "/internal/horae/v1/batchgrant"
	HoraeApiBatchConsume    = "/internal/horae/v1/batchconsume"
	HoraeApiSelBoxAst       = "/internal/horae/v1/selboxast"
	HoraeApiGrantBox        = "/internal/horae/v1/grantbox"
	HoraeApiSelectMaterial  = "/internal/horae/v1/selmaterial"
	HoraeApiComposeShard    = "/internal/horae/v1/compose"
	HoraeApiDivideShard     = "/internal/horae/v1/divide"
	HoraeApiUpgradeAst      = "/internal/horae/v1/upgradeast"
	HoraeApiUpgradeSds      = "/internal/horae/v1/upgradesds"
	HoraeApiCountNearExpire = "/internal/horae/v1/countnearexpire"
	HoraeApiListSdsByCate   = "/internal/horae/v1/listsdsbycate"
	HoraeApiCountAggrShards = "/internal/horae/v1/countsds"
	HoraeApiListAggrAst     = "/internal/horae/v1/listaggrast"
	HoraeApiListSdsByAstV3  = "/internal/horae/v1/listsdsbyastv3"

	SceneApiListShardByAddr = "/internal/horae/v1/scenelistsdsbyaddr"
	SceneApiListDiffByAddr  = "/internal/horae/v1/scenelistdiffbyaddr"

	TagApiAddAssetTag     = "/internal/tag/v1/addtag"
	TagApiDelAssetTag     = "/internal/tag/v1/deltag"
	TagApiGetAssetTag     = "/internal/tag/v1/asttag"
	TagApiListShardsbyTag = "/internal/tag/v1/listshrdsbytag"

	CollectApiCreate       = "/internal/collect/v1/create"
	CollectApiDelete       = "/internal/collect/v1/delete"
	CollectApiSetTitle     = "/internal/collect/v1/settitle"
	CollectApiSetCover     = "/internal/collect/v1/setcover"
	CollectApiQuery        = "/internal/collect/v1/query"
	CollectApiCountCollect = "/internal/collect/v1/count"
	CollectApiListCollect  = "/internal/collect/v1/listbyaddr"
	CollectApiAddAsset     = "/internal/collect/v1/addast"
	CollectApiDelAsset     = "/internal/collect/v1/delast"
	CollectApiListAsset    = "/internal/collect/v1/listastbycid"
	CollectApiListAggAsset = "/internal/collect/v1/listaggastbycid"
)

const (
	ThumbSize60     = "icon"
	ThumbSize140    = "url1"
	ThumbSize360    = "url2"
	ThumbSize850    = "url3"
	ThumbSizeOrigin = "origin"
)

var (
	NormalWidth = map[string]int{
		ThumbSize60:  60,
		ThumbSize140: 140,
		ThumbSize360: 360,
		ThumbSize850: 850,
	}
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

type CreateAssetResp struct {
	BaseResp
	AssetId int64 `json:"asset_id"`
}

type QueryAssetResp struct {
	BaseResp
	Meta *AssetInfoNode `json:"meta"`
}

type AssetInfoNode struct {
	Id           int64  `json:"id"`
	AppId        int64  `json:"app_id"`
	AssetId      int64  `json:"asset_id"`
	GroupId      int64  `json:"group_id"`
	CreateAddr   string `json:"create_addr"`
	ViewType     int    `json:"view_type"`
	AssetCate    int    `json:"asset_cate"`
	Title        string `json:"title"`
	Thumb        string `json:"thumb"`
	ShortDesc    string `json:"short_desc"`
	LongDesc     string `json:"long_desc"`
	ImgDesc      string `json:"img_desc"`
	AssetUrl     string `json:"asset_url"`
	AssetExt     string `json:"asset_ext"`
	Nonce        int64  `json:"nonce"`
	Price        int64  `json:"price"`
	Amount       int    `json:"amount"`
	Status       int    `json:"status"`
	Stock        int    `json:"stock"`
	SaleStatus   int    `json:"sale_status"`
	TxId         string `json:"tx_id"`
	EvidenceType int    `json:"evidence_type"`
	EvidenceExt  string `json:"evidence_ext"`
	ProcScript   string `json:"proc_script"`
	CreateSign   string `json:"create_sign"`
	CreatePkey   string `json:"create_pkey"`
	ExtInfo      string `json:"ext_info"`
	Ctime        int64  `json:"ctime"`
	Mtime        int64  `json:"mtime"`
	AssetParam   string `json:"asset_param,omitempty"`
	Version      int64  `json:"version"`
	ExpireTime   int64  `json:"expire_time"`
}

type ListAssetsByPageResp struct {
	BaseResp
	List     []*AssetInfoNode `json:"list"`
	TotalCnt int              `json:"total_cnt"`
}

type ListAssetsByCursorResp struct {
	BaseResp
	List    []*AssetInfoNode `json:"list"`
	HasMore int              `json:"has_more"`
	Cursor  string           `json:"cursor"`
}

type AssetShardNode struct {
	Id         int64  `json:"id"`
	AppId      int64  `json:"app_id"`
	AssetId    int64  `json:"asset_id"`
	ShardId    int64  `json:"shard_id"`
	Price      int64  `json:"price"`
	Meta       string `json:"meta"`
	TaskId     int64  `json:"task_id"`
	Nonce      int64  `json:"nonce"`
	OwnerAddr  string `json:"owner_addr"`
	UserId     int64  `json:"user_id"`
	Status     int    `json:"status"`
	OwnerSign  string `json:"owner_sign"`
	OwnerPkey  string `json:"owner_pkey"`
	TxId       string `json:"tx_id"`
	ExtInfo    string `json:"ext_info"`
	Ctime      int64  `json:"ctime"`
	Mtime      int64  `json:"mtime"`
	ShardParam string `json:"shard_param,omitempty"`
	Version    int64  `json:"version"`
	AssetCate  int    `json:"asset_cate"`
	ExpireTime int64  `json:"expire_time"`
}

type QueryShardResp struct {
	BaseResp
	Meta *AssetShardNode `json:"meta"`
}

type ListShardsByPageResp struct {
	BaseResp
	List     []*AssetShardNode `json:"list"`
	TotalCnt int               `json:"total_cnt"`
}

type ListShardsByCursorResp struct {
	BaseResp
	List    []*AssetShardNode `json:"list"`
	Cursor  string            `json:"cursor"`
	HasMore int               `json:"has_more"`
}

type AssetTransLogNode struct {
	AssetId int64  `json:"asset_id"`
	ShardId int64  `json:"shard_id"`
	Price   int64  `json:"price"`
	Type    int    `json:"type"`
	From    string `json:"from"`
	To      string `json:"to"`
	TxId    string `json:"tx_id"`
	Ctime   int64  `json:"ctime"`
}

type ListTransLogByPageResp struct {
	BaseResp
	List     []*AssetTransLogNode `json:"list"`
	TotalCnt int                  `json:"total_cnt"`
}

type BasicAssetParam struct {
	AppId   int64 `json:"app_id"`
	AssetId int64 `json:"asset_id"`
	ShardId int64 `json:"shard_id"`
}

func (p *BasicAssetParam) IsValid() bool {
	if p.AppId < 1 || p.AssetId < 1 || p.ShardId < 1 {
		return false
	}
	return true
}

type BasicSignParam struct {
	Addr  string `json:"addr"`
	Sign  string `json:"sign"`
	Pkey  string `json:"pkey"`
	Nonce int64  `json:"nonce"`
}

func (p *BasicSignParam) IsValid() bool {
	if p.Addr == "" || p.Sign == "" || p.Pkey == "" || p.Nonce < 1 {
		return false
	}
	return true
}

type CreateAssetParam struct {
	AppId       int64  `json:"app_id"`
	AssetId     int64  `json:"asset_id"`
	Nonce       int64  `json:"nonce"`
	Price       int64  `json:"price,omitempty"`
	Amount      int    `json:"amount"`
	AssetInfo   string `json:"asset_info"`
	CreateAddr  string `json:"create_addr"`
	CreateSign  string `json:"create_sign"`
	CreatePkey  string `json:"create_pkey"`
	EvidenceExt string `json:"evidence_ext"`
	UserId      int64  `json:"user_id"`
	AssetParam  string `json:"asset_param"`
	ViewType    int    `json:"view_type"`
}

func (t *CreateAssetParam) IsValid() bool {
	if t.AppId < 1 || t.AssetId < 1 || t.AssetInfo == "" || t.CreateAddr == "" ||
		t.CreateSign == "" || t.CreatePkey == "" || t.Nonce < 1 || t.Price < 0 {
		return false
	}
	return true
}

type ModifyAssetParam struct {
	AppId       int64  `json:"app_id"`
	AssetId     int64  `json:"asset_id"`
	Nonce       int64  `json:"nonce"`
	Price       int64  `json:"price,omitempty"`
	Amount      int    `json:"amount"`
	AssetInfo   string `json:"asset_info"`
	EvidenceExt string `json:"evidence_ext"`
	CreateAddr  string `json:"create_addr"`
	CreateSign  string `json:"create_sign"`
	CreatePkey  string `json:"create_pkey"`
	ViewType    int    `json:"view_type"`
}

func (t *ModifyAssetParam) IsValid() bool {
	if t.AppId < 1 || t.AssetId < 1 || t.Nonce < 1 ||
		t.CreateAddr == "" || t.CreateSign == "" || t.CreatePkey == "" {
		return false
	}
	return true
}

type PublishAssetParam struct {
	AppId        int64 `json:"app_id"`
	AssetId      int64 `json:"asset_id"`
	EvidenceType int   `json:"evidence_type"`
}

func (p *PublishAssetParam) IsValid() bool {
	if p.AppId < 1 || p.AssetId < 1 {
		return false
	}
	return true
}

type TransferShardParam struct {
	AppId      int64  `json:"app_id"`
	AssetId    int64  `json:"asset_id"`
	ShardId    int64  `json:"shard_id"`
	Price      int64  `json:"price,omitempty"`
	Addr       string `json:"addr"`
	Sign       string `json:"sign"`
	PKey       string `json:"pkey"`
	Nonce      int64  `json:"nonce"`
	ToAddr     string `json:"to_addr"`
	ToUserId   int64  `json:"to_userid,omitempty"`
	ShardParam string `json:"shard_param,omitempty"`
}

func (p *TransferShardParam) IsValid() bool {
	if p.AppId < 1 || p.AssetId < 1 || p.ShardId < 1 || p.Price < 0 {
		return false
	}
	if p.Addr == "" || p.Sign == "" || p.PKey == "" || p.Nonce < 1 {
		return false
	}
	if p.ToAddr == "" {
		return false
	}
	return true
}

type FreezeAssetParam struct {
	AppId   int64  `json:"app_id"`
	AssetId int64  `json:"asset_id"`
	Addr    string `json:"addr"`
	Sign    string `json:"sign"`
	PKey    string `json:"pkey"`
	Nonce   int64  `json:"nonce"`
}

type ConsumeShardParam struct {
	AppId   int64  `json:"app_id"`
	AssetId int64  `json:"asset_id"`
	ShardId int64  `json:"shard_id"`
	UAddr   string `json:"user_addr"`
	USign   string `json:"user_sign"`
	UPKey   string `json:"user_pkey"`
	Nonce   int64  `json:"nonce"`
}

type ListXShardParam struct {
	AppId      int64  `json:"app_id"`
	Addr       string `json:"addr"`
	Cursor     string `json:"cursor"`
	Limit      int    `json:"limit"`
	Type       int    `json:"type"`
	DaysFilter int64  `json:"time"`
	Title      string `json:"title"`
}

type SrdsInCirResp struct {
	BaseResp
	Amount int `json:"srdscir_amount"`
}

type IndexListShardResp struct {
	BaseResp
	HasMore int        `json:"has_more"`
	Cursor  string     `json:"cursor"`
	List    []ShowItem `json:"list"`
}

type ShowItem struct {
	AssetId   int64  `json:"asset_id"`
	ShardId   int64  `json:"shard_id"`
	OwnerAddr string `json:"owner_addr"`
	Title     string `json:"title"`
	Thumb     string `json:"thumb"`
	Ctime     int64  `json:"ctime"`
	Type      int    `json:"type"`
	Status    int    `json:"status"`
}

type AssetIndexTabNode struct {
	Id         int64  `json:"id"`
	Addr       string `json:"addr"`
	AssetId    int64  `json:"asset_id"`
	ShardId    int64  `json:"shard_id"`
	AppId      int64  `json:"app_id"`
	Title      string `json:"title"`
	Thumb      string `json:"thumb"`
	ShortDesc  string `json:"short_desc"`
	AssetUrl   string `json:"asset_url"`
	CreateAddr string `json:"create_addr"`
	ExtInfo    string `json:"ext_info"`
	Ctime      int64  `json:"ctime"`
	Mtime      int64  `json:"mtime"`
}

type HasAssetResp struct {
	BaseResp
	List []*AssetIndexTabNode `json:"list"`
}

type DiffListShardResp struct {
	BaseResp
	HasMore int        `json:"has_more"`
	Cursor  string     `json:"cursor"`
	List    []DiffItem `json:"list"`
}

type DiffItem struct {
	AssetId int64  `json:"asset_id"`
	ShardId int64  `json:"shard_id"`
	Operate int    `json:"operate"`
	Title   string `json:"title"`
	Thumb   string `json:"thumb"`
	Ctime   int64  `json:"ctime"`
}

const (
	MaxImgWidth      = "4096"
	DefaultImgHeight = "2048"
)

type Thumb struct {
	Bucket string `json:"bucket"`
	Object string `json:"object"`
	Width  string `json:"width"`
	Height string `json:"height"`
}

type ThumbObject struct {
	Url    string `json:"url"`
	Width  string `json:"width"`
	Height string `json:"height"`
}

type ThumbMap struct {
	Urls   map[string]string `json:"urls"`
	Width  string            `json:"width"`
	Height string            `json:"height"`
}

/*
*

	字符串转成多张不同尺寸的缩略图
*/
func StrToThumbMap(bc *bos.BceBosDlink, str string) ([]ThumbMap, error) {
	var err error
	thumbMaps := make([]ThumbMap, 0)
	if str == "" {
		return thumbMaps, nil
	}
	var thumbStrs []string
	err = json.Unmarshal([]byte(str), &thumbStrs)
	if err != nil {
		log.Printf("json.Unmarshal thumb %s failed %v", str, err)
	} else {
		for _, str := range thumbStrs {
			thumbObj := DecodeStrToThumb(str)
			if len(thumbObj.Object) < 1 || thumbObj.Object == "/" {
				continue
			}

			var thumbMap ThumbMap
			urls := make(map[string]string)
			for key, width := range NormalWidth {
				url, err := bc.GetThumbFileUrl(thumbObj.Object, -1, width)
				if err != nil {
					log.Printf("bc GetThumbFileUrl %v failed %v", thumbObj.Object, err)
				} else {
					urls[key] = url
				}
			}
			urls["origin"] = bc.GetThumbOriUrl(thumbObj.Object, BosDlineExpireSecond)
			thumbMap.Urls = urls
			thumbMap.Height = thumbObj.Height
			thumbMap.Width = thumbObj.Width
			thumbMaps = append(thumbMaps, thumbMap)
		}
	}
	return thumbMaps, nil
}

/*
资产详情长图字符串转成原图url
*/
func StrToImgDesc(bc *bos.BceBosDlink, str string) ([]string, error) {
	var err error
	thumbs := make([]string, 0)
	if str == "" {
		return thumbs, nil
	}
	var thumbStrs []string
	err = json.Unmarshal([]byte(str), &thumbStrs)
	if err != nil {
		log.Printf("json.Unmarshal thumb %s failed %v", str, err)
		return thumbs, err
	}
	for _, str := range thumbStrs {
		thumbObj := DecodeStrToThumb(str)
		if len(thumbObj.Object) < 1 || thumbObj.Object == "/" {
			continue
		}

		width, err := strconv.Atoi(thumbObj.Width)
		if err != nil {
			log.Printf("GetThumbFileUrl fail.width:%s %v", thumbObj.Width, err)
		}
		url, err := bc.GetThumbFileUrl(thumbObj.Object, BosDlineExpireSecond, width)
		if err != nil {
			log.Printf("GetThumbFileUrl fail.object:%s err:%v", thumbObj.Object, err)
		}
		thumbs = append(thumbs, url)
	}

	return thumbs, nil
}

/*
资产url字符串获取文件下载链接
*/
func StrToAssetUrls(bc *bos.BceBosDlink, str string) ([]string, error) {
	var err error
	assetUrls := make([]string, 0)
	if str == "" {
		return assetUrls, nil
	}
	var assetUrlStrs []string
	err = json.Unmarshal([]byte(str), &assetUrlStrs)
	if err != nil {
		log.Printf("json.Unmarshal thumb %s failed %v", str, err)
		return assetUrls, err
	}
	for _, str := range assetUrlStrs {
		obj := DecodeStrToThumb(str)
		if len(obj.Object) < 1 || obj.Object == "/" {
			return assetUrls, errors.New("asset url invalid")
		}
		url := bc.GetFileUrl(obj.Object, BosDlineExpireSecond)
		assetUrls = append(assetUrls, url)
	}

	return assetUrls, nil
}

/*
*

	格式: protocol_name://content
	示例: bos_v1://bucket/object/width_height
*/
func DecodeStrToThumb(thumbStr string) *Thumb {
	var thumbObj = &Thumb{
		Bucket: "",
		Object: "",
		Width:  "",
		Height: "",
	}
	if thumbStr == "" {
		return thumbObj
	}
	strs := strings.Split(thumbStr, "://")
	if len(strs) < 2 {
		return thumbObj
	}
	switch strs[0] {
	case "bos_v1":
		thumbObj = bosV1Decoder(strs[1])
	}

	return thumbObj
}

/*
*

	格式: bucket1/appId/object/width_height
	ObjectName:appId/object
	示例: xasset-trade/100839/horse.jpg/400_200
*/
func bosV1Decoder(str string) *Thumb {
	// 元信息没有宽高时，取默认最大的宽度，如最大宽度大于原图宽度，返回原图大小
	thumbObj := &Thumb{
		Bucket: "",
		Object: "",
		Width:  MaxImgWidth,
		Height: DefaultImgHeight,
	}
	if str == "" {
		return thumbObj
	}
	thumbInfos := strings.Split(str, "/")
	if len(thumbInfos) < 3 {
		return thumbObj
	}
	thumbObj.Bucket = thumbInfos[0]
	thumbObj.Object = fmt.Sprintf("%s/%s", thumbInfos[1], thumbInfos[2])
	if len(thumbInfos) < 4 {
		return thumbObj
	}
	widthHeight := strings.Split(thumbInfos[3], "_")
	if len(widthHeight) < 2 {
		return thumbObj
	}
	thumbObj.Width = widthHeight[0]
	thumbObj.Height = widthHeight[1]
	return thumbObj
}

type GetAssetTagResp struct {
	BaseResp
	List []TagInfo `json:"list"`
}

type TagInfo struct {
	TagId   int64  `json:"tag_id"`
	TagName string `json:"tag_name"`
}

type TagItem struct {
	AssetId int64  `json:"asset_id"`
	ShardId int64  `json:"shard_id"`
	Title   string `json:"title"`
	Thumb   string `json:"thumb"`
}

type IndexListShardbyTagResp struct {
	BaseResp
	HasMore int       `json:"has_more"`
	Cursor  string    `json:"cursor"`
	List    []TagItem `json:"list"`
}

type CreateCollectParam struct {
	Addr      string `json:"addr"`
	Title     string `json:"title"`
	Type      int    `json:"type"`
	CollectId int64  `json:"collect_id"`
	AppId     int64  `json:"app_id"`
}

func (p *CreateCollectParam) Valid() bool {
	return p.Addr != "" && p.Title != "" && p.CollectId > 0 && p.AppId > 0
}

type UpdateCollectParam struct {
	CollectId int64  `json:"collect_id"`
	AppId     int64  `json:"app_id"`
	Addr      string `json:"addr"`
	Title     string `json:"title"`
	AssetId   int64  `json:"asset_id"`
	ShardId   int64  `json:"shard_id"`
}

func (p *UpdateCollectParam) Valid() bool {
	return p.Addr != "" && p.CollectId > 0 && p.AppId > 0
}

type DelCollectParam struct {
	CollectId int64  `json:"collect_id"`
	AppId     int64  `json:"app_id"`
	Addr      string `json:"addr"`
}

func (p *DelCollectParam) Valid() bool {
	return p.Addr != "" && p.CollectId > 0 && p.AppId > 0
}

type QueryCollectParam struct {
	AppId     int64  `json:"app_id"`
	CollectId int64  `json:"collect_id"`
	Addr      string `json:"addr"`
}

func (p *QueryCollectParam) Valid() bool {
	return p.Addr != "" && p.CollectId > 0 && p.AppId > 0
}

type QueryCollectResp struct {
	BaseResp
	Meta *ShardsCollectNode `json:"meta"`
}

type ListCollectAssetParam struct {
	AppId     int64  `json:"app_id"`
	AssetId   int64  `json:"asset_id"`
	CollectId int64  `json:"collect_id"`
	Sort      int    `json:"sort"`
	Cursor    string `json:"cursor"`
	Limit     int    `json:"limit"`
}

func (p *ListCollectAssetParam) Valid() bool {
	return p.CollectId > 0 && p.AppId > 0
}

type ListCollectAssetResp struct {
	BaseResp
	List    []*CollectRelationNode `json:"list"`
	Cursor  string                 `json:"cursor"`
	HasMore int                    `json:"has_more"`
}

type CollectRelationNode struct {
	AppId     int64  `json:"app_id"`
	Addr      string `json:"addr"`
	CollectId int64  `json:"collect_id"`
	AssetId   int64  `json:"asset_id"`
	ShardId   int64  `json:"shard_id"`
	Title     string `json:"title"`
	Thumb     string `json:"thumb"`
	Ctime     int64  `json:"ctime"`
}

type ListCollectParam struct {
	Addr       string `json:"addr"`
	AppId      int64  `json:"app_id"`
	Cursor     string `json:"cursor"`
	Limit      int    `json:"limit"`
	Type       int    `json:"type"`
	Types      string `json:"types"`
	Sort       int    `json:"int"`
	DaysFilter int64  `json:"time"`
}

func (p *ListCollectParam) Valid() bool {
	return p.Addr != "" && p.AppId > 0
}

type ListCollectResp struct {
	BaseResp
	List    []*ShardsCollectNode `json:"list"`
	Cursor  string               `json:"cursor"`
	HasMore int                  `json:"has_more"`
}

type CountCollectParam struct {
	AppId int64  `json:"app_id"`
	Addr  string `json:"addr"`
	Types string `json:"types"`
}

func (p *CountCollectParam) Valid() bool {
	return p.Addr != "" && p.AppId > 0
}

type CountCollectResp struct {
	BaseResp
	Count int `json:"count"`
}

type ShardsCollectNode struct {
	AppId     int64  `json:"app_id"`
	CollectId int64  `json:"collect_id"`
	Type      int    `json:"type"`
	Addr      string `json:"addr"`
	Title     string `json:"title"`
	Cover     string `json:"cover"`
	Status    int    `json:"status"`
	Ctime     int64  `json:"ctime"`
	Mtime     int64  `json:"mtime"`
}

type UpdateCollectAssetParam struct {
	AppId     int64  `json:"app_id"`
	Addr      string `json:"addr"`
	CollectId int64  `json:"collect_id"`
	Assets    string `json:"assets"`
}

func (p *UpdateCollectAssetParam) Valid() bool {
	return p.Addr != "" && p.AppId > 0 && p.CollectId > 0 && p.Assets != ""
}

type ListCollectAggParam struct {
	AppId     int64  `json:"app_id"`
	CollectId int64  `json:"collect_id"`
	Sort      int    `json:"sort"`
	Cursor    string `json:"cursor"`
	Limit     int    `json:"limit"`
}

func (p *ListCollectAggParam) Valid() bool {
	return p.AppId > 0 && p.CollectId > 0
}

type CollectAggInfo struct {
	AssetId int64  `json:"asset_id"`
	Title   string `json:"title"`
	Thumb   string `json:"thumb"`
	Ctime   int64  `json:"ctime"`
}

type ListCollectAggResp struct {
	BaseResp
	List    []*CollectAggInfo `json:"list"`
	Cursor  string            `json:"cursor"`
	HasMore int               `json:"has_more"`
}

type SceneListShardsItem struct {
	AssetId   int64  `json:"asset_id"`
	ShardId   int64  `json:"shard_id"`
	Type      int    `json:"type"`
	Status    int    `json:"status"`
	OwnerAddr string `json:"owner_addr"`
	Title     string `json:"title"`
	Thumb     string `json:"thumb"`
	AssetUrl  string `json:"asset_url"`
	Ctime     int64  `json:"ctime"`
}

type SceneListShardsResp struct {
	BaseResp
	HasMore int                    `json:"has_more"`
	Cursor  string                 `json:"cursor"`
	List    []*SceneListShardsItem `json:"list"`
}

type BatchGrantParam struct {
	AppId    int64           `json:"app_id"`
	ToAddr   string          `json:"to_addr"`
	Addr     string          `json:"addr"`
	Pkey     string          `json:"pkey"`
	ToUserId int64           `json:"to_userid"`
	AstList  []*BatchOpAsset `json:"ast_list"`
}

type BatchOpAsset struct {
	AssetId int64  `json:"asset_id"`
	ShardId int64  `json:"shard_id"`
	Nonce   int64  `json:"nonce,omitempty"`
	Price   int64  `json:"price,omitempty"`
	Sign    string `json:"sign,omitempty"`
	ExtInfo string `json:"ext_info,omitempty"`
}

type BatchGrantResp struct {
	BaseResp
	List []*BatchOpAsset `json:"list"`
}

type SelectBoxAstParam struct {
	AppId   int64
	AssetId int64
	ShardId int64
	Address string
}

func (t *SelectBoxAstParam) Valid() bool {
	return t.AppId > 0 && t.AssetId > 0 && t.ShardId > 0 && t.Address != ""
}

type SelectBoxResp struct {
	BaseResp
	RealAstId int64      `json:"real_asset_id"`
	Token     string     `json:"token"`
	Gift      []*BoxGift `json:"gift"`
}

type BoxGift struct {
	AssetId int64 `json:"asset_id"`
	Amount  int   `json:"amount"`
}

type GrantBoxParam struct {
	AppId        int64
	Token        string
	ConsumeNonce int64
	UAddr        string
	USign        string
	UPkey        string
	CAddr        string
	CPkey        string
	CSign        string
	AssetId      int64
	GrantNonce   int64
	UserId       int64
}

func (t *GrantBoxParam) Valid() bool {
	return t.AppId > 0 && t.Token != "" && t.ConsumeNonce > 0 && t.UAddr != "" && t.USign != "" && t.UPkey != "" &&
		t.CAddr != "" && t.CSign != "" && t.CPkey != "" && t.AssetId > 0 && t.GrantNonce > 0
}

type GrantBoxResp struct {
	BaseResp
	AssetId int64 `json:"asset_id"`
	ShardId int64 `json:"shard_id"`
}

type SelectMaterialParam struct {
	AppId   int64
	AssetId int64
	StrgNo  int
	Addr    string
}

func (t *SelectMaterialParam) Valid() bool {
	return t.AppId > 0 && t.AssetId > 0 && t.StrgNo >= 0 && t.Addr != ""
}

type SelectMaterialResp struct {
	BaseResp
	List  []*BatchOpAsset `json:"list"`
	Token string          `json:"token"`
}

type ComposeParam struct {
	AppId   int64
	AssetId int64
	StrgNo  int
	Addr    string
	Pkey    string
	Nonce   int64
	Sign    string
	UAddr   string
	UPkey   string
	AstList string
	Token   string
	UserId  int64
}

func (t *ComposeParam) Valid() bool {
	return t.AppId > 0 && t.AssetId > 0 && t.StrgNo >= 0 && t.Addr != "" &&
		t.Pkey != "" && t.Nonce > 0 && t.Sign != "" && t.UAddr != "" && t.UPkey != "" && t.AstList != ""
}

type ComposeShardResp struct {
	BaseResp
	AssetId int64 `json:"asset_id"`
	ShardId int64 `json:"shard_id"`
}

type CountShardsResp struct {
	BaseResp
	List []*CountShardsNode `json:"list"`
}

type CountShardsNode struct {
	AssetId int64  `json:"asset_id"`
	Meta    string `json:"meta"`
	Cnt     int    `json:"cnt"`
}

type UpgradeAstParam struct {
	AppId      int64  `json:"app_id"`
	AssetId    int64  `json:"asset_id"`
	AssetParam string `json:"param"`
}

func (p *UpgradeAstParam) IsValid() bool {
	if p.AppId < 1 || p.AssetId < 1 || p.AssetParam == "" {
		return false
	}
	return true
}

type UpgradeSdsParam struct {
	AppId      int64  `json:"app_id"`
	AssetId    int64  `json:"asset_id"`
	ShardId    int64  `json:"shard_id"`
	ShardParam string `json:"param"`
}

func (p *UpgradeSdsParam) IsValid() bool {
	if p.AppId < 1 || p.AssetId < 1 || p.ShardId < 1 || p.ShardParam == "" {
		return false
	}
	return true
}

type LockOrFreezeShardParam struct {
	AppId   int64  `json:"app_id"`
	AssetId int64  `json:"asset_id"`
	ShardId int64  `json:"shard_id"`
	UAddr   string `json:"user_addr"`
	OpType  int    `json:"op_type"`
}

func (p *LockOrFreezeShardParam) Valid() bool {
	return p.AppId >= 1 && p.AssetId >= 1 && p.ShardId >= 1 && p.UAddr != ""
}

type BatchFreezeShardParam struct {
	Addr    string `json:"addr"`
	SdsList string `json:"sds_list"`
}

func (p *BatchFreezeShardParam) Valid() bool {
	return p.Addr != "" && p.SdsList != ""
}

type BatchCumfreShardParam struct {
	Addr    string `json:"addr"`
	Pkey    string `json:"pkey"`
	SdsList string `json:"sds_list"`
}

func (p *BatchCumfreShardParam) Valid() bool {
	return p.Addr != "" && p.Pkey != "" && p.SdsList != ""
}

type BatchConsumeParam struct {
	Addr    string `json:"addr"`
	Pkey    string `json:"pkey"`
	SdsList string `json:"sds_list"`
}

func (p *BatchConsumeParam) Valid() bool {
	return p.Addr != "" && p.Pkey != "" && p.SdsList != ""
}

type CountNearExpireParam struct {
	AppId      int64  `json:"app_id"`
	Addr       string `json:"addr"`
	AssetCate  int    `json:"asset_cate"`
	ExpireTime int64  `json:"expire_time"`
}

func (p *CountNearExpireParam) Valid() bool {
	return p.AppId > 0 && p.Addr != "" && p.ExpireTime > 0
}

type CountNearExpireResp struct {
	BaseResp
	Count int `json:"count"`
}

type ListShardsByCateParam struct {
	AppId     int64  `json:"app_id"`
	Addr      string `json:"addr"`
	AssetCate int    `json:"asset_cate"`
	SortKey   int    `json:"sort_key"`
	Cursor    string `json:"cursor"`
	Limit     int    `json:"limit"`
}

func (p *ListShardsByCateParam) Valid() bool {
	return p.AppId > 0 && p.Addr != "" && p.AssetCate > 0 && p.SortKey > 0
}

type ListShardsByCateResp struct {
	BaseResp
	List    []*AssetShardNode `json:"list"`
	HasMore int               `json:"has_more"`
	Cursor  string            `json:"cursor"`
}

type CountAssetsTypeParam struct {
	AppId int64  `json:"app_id"`
	Addr  string `json:"addr"`
	Type  int    `json:"type"`
}

type CountAssetsTypeResp struct {
	BaseResp
	AssetCnt int `json:"asset_cnt"`
	ShardCnt int `json:"shard_cnt"`
}

type AggrAstListParam struct {
	AppId    int64  `json:"app_id"`
	Title    string `json:"title"`
	Addr     string `json:"addr"`
	Type     int    `json:"type"`
	MonoIncr int    `json:"mono"`
	Limit    int    `json:"limit"`
	Cursor   string `json:"cursor"`
}

type AggrAstListNode struct {
	AssetId int64  `json:"asset_id"`
	Title   string `json:"title"`
	Thumb   string `json:"thumb"`
	SrdsCnt int    `json:"shard_cnt"`
	Mtime   int64  `json:"mtime"`
}

type AggrAstListResp struct {
	BaseResp
	List    []*AggrAstListNode `json:"list"`
	HasMore int                `json:"has_more"`
	Cursor  string             `json:"cursor"`
}

type ListSdsByAstV3Param struct {
	Addr     string `json:"addr"`
	MonoIncr int    `json:"mono"`
	AssetId  int64  `json:"asset_id"`
	Limit    int    `json:"limit"`
	Cursor   string `json:"cursor"`
}

type ListSdsByAstV3RNode struct {
	AssetId int64 `json:"asset_id"`
	ShardId int64 `json:"shard_id"`
	AppId   int64 `json:"app_id"`
}

type ListSdsByAstV3Resp struct {
	BaseResp
	List    []*ListSdsByAstV3RNode `json:"list"`
	HasMore int                    `json:"has_more"`
	Cursor  string                 `json:"cursor"`
}
