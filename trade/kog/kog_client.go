package kog

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type KogClient struct {
	addr *addr.Addr
	dev  bool // 是否沙盒环境
}

func NewKogClient(addr *addr.Addr, dev bool) *KogClient {
	return &KogClient{
		addr: addr,
		dev:  dev,
	}
}

func (t *KogClient) QueryStore(param *QueryStoreParam) (*QueryStoreResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId

	reqRes, err := t.doGet(KogApiQueryStore, "", header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for query store fail.err:%v", err)
	}
	var result QueryStoreResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) CreateStore(param *CreateStoreParam) (*CreateStoreResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	body, err := json.Marshal(param.CreateStoreBody)
	if err != nil {
		return nil, nil, err
	}

	reqRes, err := t.doPost(KogApiCreateStore, "", header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for create store fail.err:%v", err)
	}
	var result CreateStoreResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) UpdateStore(param *UpdateStoreParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	v := url.Values{}
	v.Set("store_id", strconv.Itoa(int(param.StoreId)))
	query := v.Encode()

	body, err := json.Marshal(param.CreateStoreBody)
	if err != nil {
		return nil, nil, err
	}

	reqRes, err := t.doPut(KogApiUpdateStore, query, header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for update store fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) PreviewStore(accountId string, storeId int64) (*PreviewResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if len(accountId) == 0 || storeId <= 0 {
		return nil, nil, errors.New("param error")
	}

	header := make(map[string]string)
	header[BCE_Account_Header] = accountId

	v := url.Values{}
	v.Set("store_id", strconv.FormatInt(storeId, 10))
	query := v.Encode()

	reqRes, err := t.doPut(KogApiPreviewStore, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for preview store failed.err:%v", err)
	}
	var result PreviewResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) ApplyStore(param *ApplyStoreParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	v := url.Values{}
	v.Set("store_id", strconv.Itoa(int(param.StoreId)))
	query := v.Encode()

	reqRes, err := t.doPost(KogApiApplyStore, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for apply store fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) RevokeStore(param *RevokeStoreParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	v := url.Values{}
	v.Set("store_id", strconv.Itoa(int(param.StoreId)))
	query := v.Encode()

	reqRes, err := t.doPost(KogApiRevokeStore, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for revoke store fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) ListAsset(param *ListAssetParam) (*ListAssetResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId

	v := url.Values{}
	v.Set("title", param.Title)
	v.Set("status", strconv.Itoa(param.Status))
	v.Set("asset_cate", strconv.Itoa(param.AssetCate))
	v.Set("page", strconv.Itoa(param.Page))
	v.Set("size", strconv.Itoa(param.Size))
	query := v.Encode()

	reqRes, err := t.doGet(KogApiListAsset, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for list asset fail.err:%v", err)
	}
	var result ListAssetResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) CreateAsset(param *CreateAssetParam) (*CreateAssetResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	body, err := json.Marshal(param.CreateAssetBody)
	if err != nil {
		return nil, nil, err
	}

	reqRes, err := t.doPost(KogApiCreateAsset, "", header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for create asset fail.err:%v", err)
	}
	var result CreateAssetResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) QueryAsset(param *QueryAssetParam) (*QueryAssetResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	v := url.Values{}
	v.Set("asset_id", strconv.Itoa(int(param.AssetId)))
	query := v.Encode()

	reqRes, err := t.doGet(KogApiQueryAsset, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for query asset fail.err:%v", err)
	}
	var result QueryAssetResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) UpdateAsset(param *UpdateAssetParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	v := url.Values{}
	v.Set("asset_id", strconv.Itoa(int(param.AssetId)))
	query := v.Encode()
	body, err := json.Marshal(param.UpdateAssetBody)
	if err != nil {
		return nil, nil, err
	}

	reqRes, err := t.doPut(KogApiUpdateAsset, query, header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for update asset fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) PreviewAsset(accountId string, assetId int64) (*PreviewResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if len(accountId) == 0 || assetId <= 0 {
		return nil, nil, errors.New("param error")
	}

	header := make(map[string]string)
	header[BCE_Account_Header] = accountId

	v := url.Values{}
	v.Set("asset_id", strconv.FormatInt(assetId, 10))
	query := v.Encode()

	reqRes, err := t.doPut(KogApiPreviewAsset, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for preview asset failed.err:%v", err)
	}
	var result PreviewResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) ApplyAsset(param *ApplyAssetParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	v := url.Values{}
	v.Set("asset_id", strconv.Itoa(int(param.AssetId)))
	query := v.Encode()

	reqRes, err := t.doPost(KogApiApplyAsset, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for apply asset fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) DeleteAsset(param *DeleteAssetParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	v := url.Values{}
	v.Set("asset_id", strconv.Itoa(int(param.AssetId)))
	query := v.Encode()

	reqRes, err := t.doDel(KogApiDeleteAsset, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for delete asset fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) ListAct(param *ListActParam) (*ListActResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	v := url.Values{}
	v.Set("act_name", param.ActName)
	v.Set("status", strconv.Itoa(param.Status))
	v.Set("online", strconv.Itoa(param.Online))
	v.Set("page", strconv.Itoa(param.Page))
	v.Set("size", strconv.Itoa(param.Size))
	query := v.Encode()

	reqRes, err := t.doGet(KogApiListAct, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for list act fail.err:%v", err)
	}
	var result ListActResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) CreateAct(param *CreateActParam) (*CreateActResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	v := url.Values{}
	v.Set("store_id", strconv.Itoa(int(param.StoreId)))
	query := v.Encode()

	body, err := json.Marshal(param.CreateActBody)
	if err != nil {
		return nil, nil, err
	}

	reqRes, err := t.doPost(KogApiCreateAct, query, header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for create act fail.err:%v", err)
	}
	var result CreateActResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) QueryAct(param *QueryActParam) (*QueryActResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId

	v := url.Values{}
	v.Set("act_id", strconv.Itoa(int(param.ActId)))
	query := v.Encode()

	reqRes, err := t.doGet(KogApiQueryAct, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for query act fail.err:%v", err)
	}
	var result QueryActResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) UpdateAct(param *UpdateActParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	v := url.Values{}
	v.Set("act_id", strconv.Itoa(int(param.ActId)))
	query := v.Encode()
	body, err := json.Marshal(param.UpdateActBody)
	if err != nil {
		return nil, nil, err
	}

	reqRes, err := t.doPut(KogApiUpdateAct, query, header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for update act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) DeleteAct(param *DelActParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	v := url.Values{}
	v.Set("act_id", strconv.Itoa(int(param.ActId)))
	query := v.Encode()

	reqRes, err := t.doDel(KogApiDelAct, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for delete act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) PreviewAct(accountId string, actId int64) (*PreviewResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if len(accountId) == 0 || actId <= 0 {
		return nil, nil, errors.New("param error")
	}

	header := make(map[string]string)
	header[BCE_Account_Header] = accountId

	v := url.Values{}
	v.Set("act_id", strconv.FormatInt(actId, 10))
	query := v.Encode()

	reqRes, err := t.doPut(KogApiPreviewAct, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for preview act failed.err:%v", err)
	}
	var result PreviewResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) ApplyAct(param *ApplyActParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	v := url.Values{}
	v.Set("act_id", strconv.Itoa(int(param.ActId)))
	query := v.Encode()

	reqRes, err := t.doPost(KogApiApplyAct, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for apply act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) ActOnline(param *ActOnlineParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	v := url.Values{}
	v.Set("act_id", strconv.Itoa(int(param.ActId)))
	v.Set("online", strconv.Itoa(param.Online))
	query := v.Encode()

	reqRes, err := t.doPost(KogApiyActOnline, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for act online fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) BindActAsset(param *BindActAstParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	v := url.Values{}
	v.Set("act_id", strconv.Itoa(int(param.ActId)))
	query := v.Encode()

	body, err := json.Marshal(param.BindActAstBody)
	if err != nil {
		return nil, nil, err
	}

	reqRes, err := t.doPost(KogApiBindActAst, query, header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for bind act ast fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) UnbindActAsset(param *UnbindActAstParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	v := url.Values{}
	v.Set("act_id", strconv.Itoa(int(param.ActId)))
	v.Set("asset_id", strconv.Itoa(int(param.AssetId)))
	query := v.Encode()

	reqRes, err := t.doPost(KogApiUnbindActAst, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for unbind act ast fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) ActStatistics(param *ActStatisticsParam) (*ActStatisticsResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId

	v := url.Values{}
	v.Set("act_id", strconv.Itoa(int(param.ActId)))
	v.Set("asset_title", param.AssetTitle)
	query := v.Encode()

	reqRes, err := t.doGet(KogApiActStatistics, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for query act statistics fail.err:%v", err)
	}
	var result ActStatisticsResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) AlterActTime(param *AlterActTimeParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	v := url.Values{}
	v.Set("act_id", strconv.Itoa(int(param.ActId)))
	v.Set("start", strconv.Itoa(int(param.Start)))
	v.Set("end", strconv.Itoa(int(param.End)))
	query := v.Encode()

	reqRes, err := t.doPut(KogApiAlterActTime, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for alter act time fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) AlterActAstTime(param *AlterActAstTimeParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	v := url.Values{}
	v.Set("act_id", strconv.Itoa(int(param.ActId)))
	v.Set("asset_id", strconv.Itoa(int(param.AssetId)))
	v.Set("start", strconv.Itoa(int(param.Start)))
	v.Set("end", strconv.Itoa(int(param.End)))
	query := v.Encode()

	reqRes, err := t.doPut(KogApiAlterActAstTime, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for alter act ast time fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) ListOrder(param *ListOrderParam) (*ListOrderResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId

	v := url.Values{}
	v.Set("status", strconv.Itoa(param.Status))
	v.Set("search", param.Search)
	v.Set("page", strconv.Itoa(param.Page))
	v.Set("size", strconv.Itoa(param.Size))
	v.Set("start", strconv.Itoa(int(param.Start)))
	v.Set("end", strconv.Itoa(int(param.End)))
	query := v.Encode()

	reqRes, err := t.doGet(KogApiListOrder, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for list order fail.err:%v", err)
	}
	var result ListOrderResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) QueryOrder(param *QueryOrderParam) (*QueryOrderResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId

	v := url.Values{}
	v.Set("order_id", strconv.Itoa(int(param.OrderId)))
	query := v.Encode()

	reqRes, err := t.doGet(KogApiQueryOrder, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for query order fail.err:%v", err)
	}
	var result QueryOrderResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) QueryCash(param *QueryCashParam) (*QueryCashResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId

	v := url.Values{}
	v.Set("start", strconv.Itoa(int(param.Start)))
	v.Set("end", strconv.Itoa(int(param.End)))
	v.Set("page", strconv.Itoa(param.Page))
	v.Set("size", strconv.Itoa(param.Size))
	query := v.Encode()

	reqRes, err := t.doGet(KogApiQueryCash, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for query cash fail.err:%v", err)
	}
	var result QueryCashResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) QueryRefund(param *QueryRefundParam) (
	*QueryRefundResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId

	v := url.Values{}
	v.Set("rid", strconv.Itoa(int(param.Rid)))
	query := v.Encode()

	reqRes, err := t.doGet(KogApiQueryRefund, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for query refund order fail.err:%v", err)
	}
	var result QueryRefundResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) ListRefund(param *ListRefundParam) (
	*ListRefundResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId

	v := url.Values{}
	v.Set("status", strconv.Itoa(param.Status))
	v.Set("search", param.Search)
	v.Set("page", strconv.Itoa(param.Page))
	v.Set("size", strconv.Itoa(param.Size))
	query := v.Encode()

	reqRes, err := t.doGet(KogApiListRefund, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for list refund order fail.err:%v", err)
	}
	var result ListRefundResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) QueryRefundApplyCnt(param *QueryRefundApplyCntParam) (
	*QueryRefundApplyCntResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId

	reqRes, err := t.doGet(KogApiQueryRefundApplyCnt, "", header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for query refund apply cnt fail.err:%v", err)
	}
	var result QueryRefundApplyCntResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) ApproveRefund(param *ApproveRefundParam) (
	*ApproveRefundResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)
	header[BCE_Account_Header] = param.AccountId
	header[User_Id_Header] = param.UserId

	body, err := json.Marshal(param.ApproveRefundBody)
	if err != nil {
		return nil, nil, err
	}

	reqRes, err := t.doPost(KogApiApproveRefund, "", header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for approve refund order fail.err:%v", err)
	}
	var result ApproveRefundResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil

}

func (t *KogClient) QueryStoreApplyList(param *QueryStoreApplyListParam) (
	*QueryStoreApplyListResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("page", strconv.FormatInt(param.Page, 10))
	v.Set("size", strconv.FormatInt(param.Size, 10))
	v.Set("status", strconv.FormatInt(param.Status, 10))
	v.Set("search", param.Search)
	query := v.Encode()

	reqRes, err := t.doGet(KogApiQueryStoreApplyList, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for list store apply info fail.err:%v", err)
	}
	var result QueryStoreApplyListResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) QueryAssetApplyList(param *QueryAssetApplyListParam) (
	*QueryAssetApplyListResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("page", strconv.FormatInt(param.Page, 10))
	v.Set("size", strconv.FormatInt(param.Size, 10))
	v.Set("status", strconv.FormatInt(param.Status, 10))
	v.Set("search", param.Search)
	query := v.Encode()

	reqRes, err := t.doGet(KogApiQueryAssetApplyList, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for list asset apply info fail.err:%v", err)
	}
	var result QueryAssetApplyListResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) QueryActApplyList(param *QueryActApplyListParam) (
	*QueryActApplyListResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("page", strconv.FormatInt(param.Page, 10))
	v.Set("size", strconv.FormatInt(param.Size, 10))
	v.Set("status", strconv.FormatInt(param.Status, 10))
	v.Set("search", param.Search)
	query := v.Encode()

	reqRes, err := t.doGet(KogApiQueryActApplyList, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for list act apply info fail.err:%v", err)
	}
	var result QueryActApplyListResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) ApproveStore(param *StoreApproveParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("id", strconv.FormatInt(param.FlowId, 10))
	v.Set("approve", strconv.FormatBool(param.Approve))
	v.Set("message", param.Message)
	query := v.Encode()

	reqRes, err := t.doPost(KogApiApproveStore, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for approve store fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) ApproveAsset(param *AssetApproveParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("id", strconv.FormatInt(param.FlowId, 10))
	v.Set("approve", strconv.FormatBool(param.Approve))
	v.Set("message", param.Message)
	query := v.Encode()

	reqRes, err := t.doPost(KogApiApproveAsset, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for approve asset fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) ApproveAct(param *ActApproveParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("id", strconv.FormatInt(param.FlowId, 10))
	v.Set("approve", strconv.FormatBool(param.Approve))
	v.Set("message", param.Message)
	query := v.Encode()

	reqRes, err := t.doPost(KogApiApproveAct, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for approve act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) BanStore(param *BanStoreParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("id", strconv.FormatInt(param.ObjectId, 10))
	v.Set("ban", strconv.FormatBool(param.Ban))
	v.Set("message", param.Message)
	query := v.Encode()

	reqRes, err := t.doPost(KogApiBanStore, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for ban store fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) BanAsset(param *BanAssetParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("id", strconv.FormatInt(param.ObjectId, 10))
	v.Set("message", param.Message)
	query := v.Encode()

	reqRes, err := t.doPost(KogApiBanAsset, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for ban asset fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) BanAct(param *BanActParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("id", strconv.FormatInt(param.ObjectId, 10))
	v.Set("ban", strconv.FormatBool(param.Ban))
	v.Set("message", param.Message)
	query := v.Encode()

	reqRes, err := t.doPost(KogApiBanAct, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for ban act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) QueryStoreAdminList(param *QueryStoreAdminListParam) (
	*QueryStoreAdminListResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("page", strconv.FormatInt(int64(param.Page), 10))
	v.Set("size", strconv.FormatInt(int64(param.Size), 10))
	v.Set("status", strconv.FormatInt(int64(param.Status), 10))
	v.Set("app_id", strconv.FormatInt(int64(param.AppId), 10))
	v.Set("store_id", strconv.FormatInt(int64(param.StoreId), 10))
	v.Set("wallet_status", strconv.FormatInt(int64(param.WalletStatus), 10))
	v.Set("store_name", param.StoreName)
	query := v.Encode()

	reqRes, err := t.doGet(KogApiQueryStoreAdminList, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for list store admin info fail.err:%v", err)
	}
	var result QueryStoreAdminListResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) QueryAssetAdminList(param *QueryAssetAdminListParam) (
	*QueryAssetAdminListResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("page", strconv.FormatInt(int64(param.Page), 10))
	v.Set("size", strconv.FormatInt(int64(param.Size), 10))
	v.Set("status", strconv.FormatInt(int64(param.Status), 10))
	v.Set("asset_cate", strconv.FormatInt(int64(param.AssetCate), 10))
	v.Set("app_id", strconv.FormatInt(param.AppId, 10))
	v.Set("asset_id", strconv.FormatInt(param.AssetId, 10))
	v.Set("deleted", strconv.FormatInt(int64(param.Deleted), 10))
	v.Set("file_type", param.FileType)
	v.Set("title", param.Title)
	query := v.Encode()

	reqRes, err := t.doGet(KogApiQueryAssetAdminList, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for list asset admin info fail.err:%v", err)
	}
	var result QueryAssetAdminListResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) QueryActAdminList(param *QueryActAdminListParam) (
	*QueryActAdminListResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("page", strconv.FormatInt(int64(param.Page), 10))
	v.Set("size", strconv.FormatInt(int64(param.Size), 10))
	v.Set("status", strconv.FormatInt(int64(param.Status), 10))
	v.Set("app_id", strconv.FormatInt(param.AppId, 10))
	v.Set("store_id", strconv.FormatInt(param.StoreId, 10))
	v.Set("act_id", strconv.FormatInt(param.ActId, 10))
	v.Set("online", strconv.FormatInt(int64(param.Online), 10))
	v.Set("deleted", strconv.FormatInt(int64(param.Deleted), 10))
	v.Set("wallet_status", strconv.FormatInt(int64(param.WalletStatus), 10))
	v.Set("act_name", param.ActName)

	query := v.Encode()

	reqRes, err := t.doGet(KogApiQueryActAdminList, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for list act admin info fail.err:%v", err)
	}
	var result QueryActAdminListResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) AdminPreviewAsset(assetId int64) (*PreviewResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if assetId <= 0 {
		return nil, nil, errors.New("param error")
	}

	header := make(map[string]string)
	v := url.Values{}
	v.Set("asset_id", strconv.FormatInt(assetId, 10))
	query := v.Encode()

	reqRes, err := t.doPut(KogApiAdminPreviewAsset, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for preview asset failed.err:%v", err)
	}
	var result PreviewResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) QueryStoreDetail(param *QueryStoreDetailParam) (
	*QueryStoreDetailResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("store_id", strconv.FormatInt(param.StoreId, 10))
	query := v.Encode()

	reqRes, err := t.doGet(KogApiQueryStoreDetail, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for query store detail fail.err:%v", err)
	}
	var result QueryStoreDetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) QueryAssetDetail(param *QueryAssetDetailParam) (
	*QueryAssetDetailResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("asset_id", strconv.FormatInt(param.AssetId, 10))
	query := v.Encode()

	reqRes, err := t.doGet(KogApiQueryAssetDetail, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for query asset detail fail.err:%v", err)
	}
	var result QueryAssetDetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) QueryActDetail(param *QueryActDetailParam) (
	*QueryActDetailResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	header := make(map[string]string)

	v := url.Values{}
	v.Set("act_id", strconv.FormatInt(param.ActId, 10))
	query := v.Encode()

	reqRes, err := t.doGet(KogApiQueryActDetail, query, header)
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for query act detail fail.err:%v", err)
	}
	var result QueryActDetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) PushStore(param *PushStoreParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	header := make(map[string]string)

	v := url.Values{}
	v.Set("store_id", strconv.FormatInt(param.StoreId, 10))
	v.Set("op_type", strconv.FormatInt(int64(param.OpType), 10))
	query := v.Encode()

	reqRes, err := t.doPost(KogApiPushStore, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for push store fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) PushAct(param *PushActParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	header := make(map[string]string)

	v := url.Values{}
	v.Set("act_id", strconv.FormatInt(param.ActId, 10))
	v.Set("op_type", strconv.FormatInt(int64(param.OpType), 10))
	query := v.Encode()

	reqRes, err := t.doPost(KogApiPushAct, query, header, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for push act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) AlterAssetExtInfo(param *AlterAssetExtInfoParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	header := make(map[string]string)

	v := url.Values{}
	v.Set("asset_id", strconv.FormatInt(param.AssetId, 10))
	query := v.Encode()

	body, _ := json.Marshal(map[string]string{"ext_info": param.ExtInfo})

	reqRes, err := t.doPut(KogApiAlterAssetExtInfo, query, header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for alter asset ext info fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) AlterActExtInfo(param *AlterActExtInfoParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	header := make(map[string]string)

	v := url.Values{}
	v.Set("act_id", strconv.FormatInt(param.ActId, 10))
	query := v.Encode()

	body, _ := json.Marshal(map[string]string{"ext_info": param.ExtInfo})

	reqRes, err := t.doPut(KogApiAlterActExtInfo, query, header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for alter act ext info fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) AlterBindAstExtInfo(param *AlterBindAstExtInfoParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	header := make(map[string]string)

	v := url.Values{}
	v.Set("act_id", strconv.FormatInt(param.ActId, 10))
	v.Set("asset_id", strconv.FormatInt(param.AssetId, 10))
	query := v.Encode()

	body, _ := json.Marshal(map[string]string{"ext_info": param.ExtInfo})

	reqRes, err := t.doPut(KogApiAlterBindAstExtInfo, query, header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for alter bind ast ext info fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) AlterAssetCompScript(param *AlterAssetCompScriptParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	header := make(map[string]string)

	v := url.Values{}
	v.Set("asset_id", strconv.FormatInt(param.AssetId, 10))
	query := v.Encode()

	body, _ := json.Marshal(map[string]string{"comp_script": param.CompScript})

	reqRes, err := t.doPut(KogApiAlterAssetCompScript, query, header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for alter asset comp script fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) AlterAssetDivScript(param *AlterAssetDivScriptParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	header := make(map[string]string)

	v := url.Values{}
	v.Set("asset_id", strconv.FormatInt(param.AssetId, 10))
	query := v.Encode()

	body, _ := json.Marshal(map[string]string{"div_script": param.DivScript})

	reqRes, err := t.doPut(KogApiAlterAssetDivScript, query, header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for alter asset div script fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) AlterAssetCouponScript(param *AlterAssetCouponScriptParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	header := make(map[string]string)

	v := url.Values{}
	v.Set("asset_id", strconv.FormatInt(param.AssetId, 10))
	query := v.Encode()

	body, _ := json.Marshal(map[string]string{"coupon_script": param.CouponScript})

	reqRes, err := t.doPut(KogApiAlterAssetCouponScript, query, header, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request kog for alter asset coupon script fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal kog response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *KogClient) getReqRWTimeout() time.Duration {
	if t.dev {
		return ReqRWTimeoutMsForDev
	}

	return ReqRWTimeoutMs
}

// post
func (t *KogClient) doPost(api string, query string, header map[string]string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl, err := url.Parse(fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api))
	if err != nil {
		return nil, fmt.Errorf("parse url fail, err: %v", err)
	}
	if len(query) != 0 {
		reqUrl.RawQuery = query
	}
	result := &RequestRes{
		ReqUrl: reqUrl.String(),
	}
	header["Content-Type"] = "application/json;charset=utf-8"
	resp, err := httpclient.Post(reqUrl.String(), header, ReqConnTimeoutMs, t.getReqRWTimeout(), data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// get
func (t *KogClient) doGet(api string, query string, header map[string]string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl, err := url.Parse(fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api))
	if err != nil {
		return nil, fmt.Errorf("parse url fail, err: %v", err)
	}
	if len(query) != 0 {
		reqUrl.RawQuery = query
	}
	result := &RequestRes{
		ReqUrl: reqUrl.String(),
	}
	resp, err := httpclient.Get(reqUrl.String(), header, ReqConnTimeoutMs, t.getReqRWTimeout(), nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// put
func (t *KogClient) doPut(api string, query string, header map[string]string,
	data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl, err := url.Parse(fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api))
	if err != nil {
		return nil, fmt.Errorf("parse url fail, err: %v", err)
	}
	if len(query) != 0 {
		reqUrl.RawQuery = query
	}
	result := &RequestRes{
		ReqUrl: reqUrl.String(),
	}
	header["Content-Type"] = "application/json;charset=utf-8"
	resp, err := httpclient.Put(reqUrl.String(), header, ReqConnTimeoutMs, t.getReqRWTimeout(), data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// del
func (t *KogClient) doDel(api string, query string, header map[string]string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl, err := url.Parse(fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api))
	if err != nil {
		return nil, fmt.Errorf("parse url fail, err: %v", err)
	}
	if len(query) != 0 {
		reqUrl.RawQuery = query
	}
	result := &RequestRes{
		ReqUrl: reqUrl.String(),
	}
	header["Content-Type"] = "application/json;charset=utf-8"
	resp, err := httpclient.Delete(reqUrl.String(), header, ReqConnTimeoutMs, t.getReqRWTimeout(), nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *KogClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
