package kog

import "net/http"

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000

	// pgc 模块沙盒环境 api 处理时间较长，3s 不够
	ReqRWTimeoutMsForDev = 6000
)

const (
	BCE_Account_Header = "X-Bce-Account"
	User_Id_Header     = "X-User-Id"
)

const (
	KogApiCreateStore  = "/internal/kog/v1/frontend/store"
	KogApiUpdateStore  = "/internal/kog/v1/frontend/store"
	KogApiQueryStore   = "/internal/kog/v1/frontend/store"
	KogApiPreviewStore = "/internal/kog/v1/frontend/store/preview"
	KogApiApplyStore   = "/internal/kog/v1/frontend/store/apply"
	KogApiRevokeStore  = "/internal/kog/v1/frontend/store/revoke"

	KogApiListAsset    = "/internal/kog/v1/frontend/asset/list"
	KogApiCreateAsset  = "/internal/kog/v1/frontend/asset"
	KogApiQueryAsset   = "/internal/kog/v1/frontend/asset"
	KogApiUpdateAsset  = "/internal/kog/v1/frontend/asset"
	KogApiDeleteAsset  = "/internal/kog/v1/frontend/asset"
	KogApiPreviewAsset = "/internal/kog/v1/frontend/asset/preview"
	KogApiApplyAsset   = "/internal/kog/v1/frontend/asset/apply"

	KogApiListAct         = "/internal/kog/v1/frontend/activity/list"
	KogApiCreateAct       = "/internal/kog/v1/frontend/activity"
	KogApiQueryAct        = "/internal/kog/v1/frontend/activity"
	KogApiUpdateAct       = "/internal/kog/v1/frontend/activity"
	KogApiDelAct          = "/internal/kog/v1/frontend/activity"
	KogApiPreviewAct      = "/internal/kog/v1/frontend/activity/preview"
	KogApiApplyAct        = "/internal/kog/v1/frontend/activity/apply"
	KogApiyActOnline      = "/internal/kog/v1/frontend/activity/online"
	KogApiBindActAst      = "/internal/kog/v1/frontend/activity/bind"
	KogApiUnbindActAst    = "/internal/kog/v1/frontend/activity/unbind"
	KogApiActStatistics   = "/internal/kog/v1/frontend/activity/statistics"
	KogApiAlterActTime    = "/internal/kog/v1/frontend/activity/time"
	KogApiAlterActAstTime = "/internal/kog/v1/frontend/activity/asttime"

	KogApiListOrder           = "/internal/kog/v1/frontend/order/list"
	KogApiQueryOrder          = "/internal/kog/v1/frontend/order"
	KogApiQueryCash           = "/internal/kog/v1/frontend/order/cash"
	KogApiQueryRefund         = "/internal/kog/v1/frontend/refundorder"
	KogApiListRefund          = "/internal/kog/v1/frontend/refundorder/list"
	KogApiApproveRefund       = "/internal/kog/v1/frontend/refundorder/approve"
	KogApiQueryRefundApplyCnt = "/internal/kog/v1/frontend/refundorder/applycnt"

	KogApiQueryStoreApplyList    = "/internal/kog/v1/admin/store/list"
	KogApiQueryStoreAdminList    = "/internal/kog/v1/admin/store/adminlist"
	KogApiApproveStore           = "/internal/kog/v1/admin/store/approve"
	KogApiBanStore               = "/internal/kog/v1/admin/store/ban"
	KogApiPushStore              = "/internal/kog/v1/admin/store/pushwallet"
	KogApiAdminPreviewAsset      = "/internal/kog/v1/admin/asset/preview"
	KogApiQueryStoreDetail       = "/internal/kog/v1/admin/store/detail"
	KogApiQueryAssetApplyList    = "/internal/kog/v1/admin/asset/list"
	KogApiQueryAssetAdminList    = "/internal/kog/v1/admin/asset/adminlist"
	KogApiApproveAsset           = "/internal/kog/v1/admin/asset/approve"
	KogApiBanAsset               = "/internal/kog/v1/admin/asset/ban"
	KogApiQueryAssetDetail       = "/internal/kog/v1/admin/asset/detail"
	KogApiAlterAssetExtInfo      = "/internal/kog/v1/admin/asset/extinfo"
	KogApiAlterAssetCompScript   = "/internal/kog/v1/admin/asset/compscript"
	KogApiAlterAssetDivScript    = "/internal/kog/v1/admin/asset/divscript"
	KogApiAlterAssetCouponScript = "/internal/kog/v1/admin/asset/couponscript"
	KogApiQueryActApplyList      = "/internal/kog/v1/admin/activity/list"
	KogApiQueryActAdminList      = "/internal/kog/v1/admin/activity/adminlist"
	KogApiApproveAct             = "/internal/kog/v1/admin/activity/approve"
	KogApiBanAct                 = "/internal/kog/v1/admin/activity/ban"
	KogApiQueryActDetail         = "/internal/kog/v1/admin/activity/detail"
	KogApiPushAct                = "/internal/kog/v1/admin/activity/pushwallet"
	KogApiAlterActExtInfo        = "/internal/kog/v1/admin/activity/extinfo"
	KogApiAlterBindAstExtInfo    = "/internal/kog/v1/admin/activity/bindextinfo"
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

type QueryStoreParam struct {
	AccountId string `json:"account_id"`
}

type StoreOverview struct {
	AppId         int64  `json:"app_id"`
	StoreId       int64  `json:"store_id"`
	StoreName     string `json:"store_name"`
	Logo          string `json:"logo"`
	LogoView      string `json:"logo_view"`
	Cover         string `json:"cover"`
	CoverView     string `json:"cover_view"`
	ShortDesc     string `json:"short_desc"`
	Phone         string `json:"phone"`
	Wechat        string `json:"wechat"`
	Status        int    `json:"status"`
	Message       string `json:"message"`
	MaxUpdate     int    `json:"max_update"`
	CurrentUpdate int    `json:"current_update"`
	Business      string `json:"business,omitempty"`
	WalletStatus  int    `json:"wallet_status,omitempty"`
	ExtInfo       string `json:"ext_info,omitempty"`
	Ctime         int64  `json:"ctime"`
	Mtime         int64  `json:"mtime"`
}

type QueryStoreResp struct {
	BaseResp
	Store StoreOverview `json:"store"`
}

type CreateStoreBody struct {
	StoreName string `json:"store_name"`
	Logo      string `json:"logo"`
	Cover     string `json:"cover"`
	ShortDesc string `json:"short_desc"`
	Phone     string `json:"phone"`
	Wechat    string `json:"wechat"`
}

type CreateStoreParam struct {
	AccountId string `json:"account_id"`
	UserId    string `json:"user_id"`
	CreateStoreBody
}

type CreateStoreResp struct {
	BaseResp
	StoreId int64 `json:"store_id"`
}

type UpdateStoreParam struct {
	AccountId string `json:"account_id"`
	StoreId   int64  `json:"store_id"`
	UserId    string `json:"user_id"`
	CreateStoreBody
}

type PreviewResp struct {
	BaseResp
	PreviewId string `json:"preview_id"`
}

type ApplyStoreParam struct {
	AccountId string `json:"account_id"`
	UserId    string `json:"user_id"`
	StoreId   int64  `json:"store_id"`
}

type RevokeStoreParam struct {
	AccountId string `json:"account_id"`
	StoreId   int64  `json:"store_id"`
	UserId    string `json:"user_id"`
}

type ListAssetParam struct {
	AccountId      string
	NeedTotalCount bool
	Title          string
	AssetCate      int
	Status         int
	Page           int
	Size           int
}

type AssetOverview struct {
	Id            int64           `json:"id"`    // 自增id
	Title         string          `json:"title"` // 藏品名称
	AssetCate     int             `json:"asset_cate"`
	ShortDesc     string          `json:"short_desc"` // 藏品简介
	LongDesc      string          `json:"long_desc"`  // 藏品长描述
	Thumb         []string        `json:"thumb"`      // 藏品缩略图
	ThumbView     []string        `json:"thumb_view"`
	ImgDesc       []string        `json:"img_desc"` // 藏品长图描述
	ImgDescView   []string        `json:"img_desc_view"`
	AssetUrl      []string        `json:"asset_url"` // 藏品原图
	AssetUrlView  []string        `json:"asset_url_view"`
	FileType      string          `json:"file_type"` // 藏品原图类型
	Amount        int             `json:"amount"`    // 藏品数量
	Price         int64           `json:"price"`     // 藏品价格
	AssetId       string          `json:"asset_id"`  // 藏品id
	Status        int             `json:"status"`    // 藏品状态
	RealAst       []RealAstDetail `json:"real_ast,omitempty"`
	CompStrg      []ComposeStrg   `json:"comp_strg,omitempty"`
	DivStrg       []DivideStrg    `json:"div_strg,omitempty"`
	BindAct       []BindAct       `json:"bind_act,omitempty"`
	Message       string          `json:"message"` // 最新消息，审批不通过或封禁时更新
	AppId         int64           `json:"app_id,omitempty"`
	Business      string          `json:"business,omitempty"`
	Copyright     []string        `json:"copyright,omitempty"`
	CopyrightView []string        `json:"copyright_view,omitempty"`
	Deleted       int             `json:"deleted,omitempty"`
	ExtInfo       string          `json:"ext_info,omitempty"` // 扩展信息
	ProcScript    string          `json:"proc_script,omitempty"`
	ExpireTime    int64           `json:"expire_time,omitempty"`
	Ctime         int64           `json:"ctime"`
	Mtime         int64           `json:"mtime,omitempty"`
}

type RealAstDetail struct {
	Seq       int      `json:"seq,omitempty"`
	Title     string   `json:"title,omitempty"`
	Thumb     []string `json:"thumb,omitempty"` // 藏品缩略图
	ThumbView []string `json:"thumb_view,omitempty"`
	AssetId   string   `json:"asset_id"`        // 藏品id
	Price     int64    `json:"price,omitempty"` // 藏品价格
	Amount    int      `json:"amount"`          // 藏品数量
}

type ComposeStrg struct {
	StrgNo int    `json:"strg_no"`
	Strgs  []Strg `json:"strg"`
}

type Strg struct {
	Id        int64    `json:"id"`
	Need      int64    `json:"need"`
	Title     string   `json:"title,omitempty"`
	Thumb     []string `json:"thumb,omitempty"`
	ThumbView []string `json:"thumb_view,omitempty"`
}

type DivideStrg struct {
	StrgNo int    `json:"strg_no"`
	Strgs  []Strg `json:"strg"`
}

type BindAct struct {
	ActId  int64 `json:"act_id"`
	Amount int64 `json:"amount"`
}

type ListAssetResp struct {
	BaseResp
	AssetList []AssetOverview `json:"list"`
	TotalCnt  int64           `json:"total_cnt"`
}

type CreateAssetBody struct {
	Title      string          `json:"title"`
	AssetCate  int             `json:"asset_cate"`
	ShortDesc  string          `json:"short_desc"`
	LongDesc   string          `json:"long_desc"`
	Price      int64           `json:"price"`
	Thumb      []string        `json:"thumb"`
	FileType   string          `json:"file_type"`
	AssetUrl   []string        `json:"asset_url"`
	ImgDesc    []string        `json:"img_desc"`
	Copyright  []string        `json:"copyright"`
	ExtInfo    string          `json:"ext_info"`
	RealAst    []BlindBoxAsset `json:"real_ast"`
	ExpireTime int64           `json:"expire_time"`
}

type BlindBoxAsset struct {
	AssetId string `json:"asset_id"`
	Amount  int    `json:"amount"`
}

type CreateAssetParam struct {
	AccountId string `json:"account_id"`
	UserId    string `json:"user_id"`
	CreateAssetBody
}

type CreateAssetResp struct {
	BaseResp
	AssetId int64 `json:"asset_id"`
}

type QueryAssetParam struct {
	AccountId string `json:"account_id"`
	AssetId   int64  `json:"asset_id"`
}

type QueryAssetResp struct {
	BaseResp
	Asset AssetOverview `json:"asset"`
}

type UpdateAssetBody = CreateAssetBody

type UpdateAssetParam struct {
	AccountId string `json:"account_id"`
	UserId    string `json:"user_id"`
	AssetId   int64  `json:"asset_id"`
	UpdateAssetBody
}

type ApplyAssetParam struct {
	AccountId string `json:"account_id"`
	UserId    string `json:"user_id"`
	AssetId   int64  `json:"asset_id"`
}

type DeleteAssetParam struct {
	AccountId string `json:"account_id"`
	UserId    string `json:"user_id"`
	AssetId   int64  `json:"asset_id"`
}

type ListActParam struct {
	AccountId      string
	ActName        string
	Status         int
	Online         int
	Page           int
	Size           int
	NeedTotalCount bool
}

type ListActResp struct {
	BaseResp
	ActList  []ActOverview `json:"list"`
	TotalCnt int64         `json:"total_cnt"`
}

type BindAssetInfo struct {
	AssetId   string   `json:"asset_id"`
	SplitId   string   `json:"split_id"`
	Thumb     []string `json:"thumb"`
	ThumbView []string `json:"thumb_view"`
	Title     string   `json:"title"`
	Amount    int64    `json:"amount"`
	Price     int64    `json:"price,omitempty"`
	Start     int64    `json:"start"`
	End       int64    `json:"end"`
	ApplyForm int      `json:"apply_form,omitempty"`
	ExtInfo   string   `json:"ext_info,omitempty"`
}

type ActOverview struct {
	Id           int64           `json:"id"`
	ActId        int64           `json:"act_id"`     // 活动id
	ActName      string          `json:"act_name"`   // 活动名称
	Issuer       string          `json:"issuer"`     // 活动发行方名称
	ShortDesc    string          `json:"short_desc"` // 活动短文本描述
	Weight       int             `json:"weight"`     // 活动权重
	Thumb        []string        `json:"thumb"`      // 活动缩略图信息
	ThumbView    []string        `json:"thumb_view"`
	ImgDesc      []string        `json:"img_desc"` // 活动图片描述
	ImgDescView  []string        `json:"img_desc_view"`
	ShareImg     []string        `json:"share_img"`
	ShareImgView []string        `json:"share_img_view"`
	Start        int64           `json:"start"`  // 活动开始时间
	End          int64           `json:"end"`    // 活动结束时间
	Status       int             `json:"status"` // 活动状态
	Online       int             `json:"online"`
	Message      string          `json:"message"` // 最新消息，审批不通过或封禁时更新
	AssetList    []BindAssetInfo `json:"asset_list"`
	AppId        int64           `json:"app_id,omitempty"`
	StoreId      int64           `json:"store_id,omitempty"`
	Business     string          `json:"business,omitempty"`
	Deleted      int             `json:"deleted,omitempty"`
	WalletStatus int             `json:"wallet_status,omitempty"` // 活动推送钱包状态
	AssetBinds   int             `json:"asset_binds,omitempty"`
	ExtInfo      string          `json:"ext_info,omitempty"`
	Ctime        int64           `json:"ctime,omitempty"`
	Mtime        int64           `json:"mtime,omitempty"`
}

type CreateActBody struct {
	ActName   string   `json:"act_name"`
	Issuer    string   `json:"issuer"`     // 活动发行方名称
	ShortDesc string   `json:"short_desc"` // 活动短文本描述
	Weight    int      `json:"weight"`     // 活动权重
	Thumb     []string `json:"thumb"`      // 活动缩略图信息
	ImgDesc   []string `json:"img_desc"`   // 活动图片描述
	ShareImg  []string `json:"share_img"`  // 活动分享图
	Start     int64    `json:"start"`      // 活动开始时间
	End       int64    `json:"end"`        // 活动结束时间
	ExtInfo   string   `json:"ext_info"`
}

type CreateActParam struct {
	AccountId string `json:"account_id"` // 账户id
	UserId    string `json:"user_id"`
	StoreId   int64  `json:"store_id"`
	CreateActBody
}

type CreateActResp struct {
	BaseResp
	ActId int64 `json:"act_id"`
}

type QueryActParam struct {
	AccountId string
	ActId     int64
}

type QueryActResp struct {
	BaseResp
	Activity ActOverview `json:"activity"`
}

type UpdateActBody = CreateActBody

type UpdateActParam struct {
	AccountId string `json:"account_id"` // 账户id
	UserId    string `json:"user_id"`
	ActId     int64  `json:"act_id"`
	UpdateActBody
}

type DelActParam struct {
	AccountId string `json:"account_id"`
	UserId    string `json:"user_id"`
	ActId     int64  `json:"act_id"` // 活动id
}

type ApplyActParam struct {
	AccountId string `json:"account_id"`
	UserId    string `json:"user_id"`
	ActId     int64  `json:"act_id"` // 活动id
}

type BindActAstBody struct {
	AssetId   string `json:"asset_id"` // 藏品id
	SplitId   string `json:"split_id"`
	ApplyForm int    `json:"apply_form"` // 售卖方式 0:空投 1:购买 2:兑换
	Amount    int64  `json:"amount"`     // 数量
	Start     int64  `json:"start"`      // 开始时间
	End       int64  `json:"end"`        // 结束时间
	ExtInfo   string `json:"ext_info"`
}

type BindActAstParam struct {
	AccountId string `json:"account_id"` // 账户id
	UserId    string `json:"user_id"`
	ActId     int64  `json:"act_id"` // 活动id
	BindActAstBody
}

type UnbindActAstParam struct {
	AccountId string `json:"account_id"` // 账户id
	UserId    string `json:"user_id"`
	ActId     int64  `json:"act_id"`   // 活动id
	AssetId   int64  `json:"asset_id"` // 藏品id
}

type ActOnlineParam struct {
	AccountId string `json:"account_id"`
	UserId    string `json:"user_id"`
	ActId     int64  `json:"act_id"`
	Online    int    `json:"online"`
}

type ActStatisticsParam struct {
	AccountId  string
	ActId      int64
	AssetTitle string
}

type AlterActTimeParam struct {
	AccountId string
	UserId    string
	ActId     int64
	Start     int64
	End       int64
}

type AlterActAstTimeParam struct {
	AccountId string
	UserId    string
	ActId     int64
	AssetId   int64
	Start     int64
	End       int64
}

type AssetInfo struct {
	AssetId   string   `json:"asset_id"`
	Thumb     []string `json:"thumb"`
	ThumbView []string `json:"thumb_view"`
	Title     string   `json:"title"`
	Price     int64    `json:"price"`
	Sold      int64    `json:"sold"`
	Income    int64    `json:"income"`
}

type AssetInfoList struct {
	List     []AssetInfo `json:"list"`
	TotalCnt int         `json:"total_cnt"`
}

type ActStatisticsResp struct {
	BaseResp
	TotalSold     int64         `json:"total_sold"`
	TotalIncome   int64         `json:"total_income"`
	AssetInfoList AssetInfoList `json:"asset"`
}

type QueryOrderParam struct {
	AccountId string
	OrderId   int64
}

type OrderOverview struct {
	OrderId   string   `json:"order_id"`
	Thumb     []string `json:"thumb"`
	ThumbView []string `json:"thumb_view"`
	Title     string   `json:"title"`
	PayPrice  int      `json:"pay_price"`
	UnitPrice int      `json:"unit_price"`
	BuyCount  int      `json:"buy_count"`
	AssetId   string   `json:"asset_id"`
	ShardIds  []string `json:"shard_ids"`
	Status    int      `json:"status"`
	BuyerAddr string   `json:"buyer_addr"`
	Ctime     int64    `json:"ctime"`
	PayTime   int64    `json:"pay_time"`
}

type QueryOrderResp struct {
	BaseResp
	Order OrderOverview `json:"order"`
}

type ListOrderParam struct {
	AccountId string
	Search    string
	Status    int
	Page      int
	Size      int
	Start     int64
	End       int64
}

func (p *ListOrderParam) Valid() bool {
	return len(p.AccountId) > 0 && p.Page > 0 && p.Size > 0
}

type ListOrderResp struct {
	BaseResp
	OrderList   []OrderOverview `json:"list"`
	TotalCnt    int64           `json:"total_cnt"`
	TotalAmount int64           `json:"total_amount"`
	TotalPrice  int64           `json:"total_price"`
}

type QueryCashParam struct {
	AccountId string `json:"account_id"`
	Start     int64  `json:"start"`
	End       int64  `json:"end"`
	Page      int    `json:"page"`
	Size      int    `json:"size"`
}

type QueryCashResp struct {
	BaseResp
	Income          int64     `json:"income"`
	Amount          int64     `json:"amount"`
	TotalCommission int64     `json:"total_commission"`
	StoreCommission int64     `json:"store_commission"`
	ActCashList     []ActCash `json:"list"`
	TotalCnt        int       `json:"total_cnt"`
}

type ActCash struct {
	ActId              int64
	ActName            string
	ActIncome          int64
	ActAmount          int64
	ActTotalCommission int64
	ActStoreCommission int64
}

type RefundOverview struct {
	Rid           string   `json:"rid"`
	Oid           string   `json:"oid"`
	Thumb         []string `json:"thumb"`
	ThumbView     []string `json:"thumb_view"`
	Title         string   `json:"title"`
	PayPrice      int      `json:"pay_price"`
	UnitPrice     int      `json:"unit_price"`
	BuyCount      int      `json:"buy_count"`
	AssetId       string   `json:"asset_id"`
	ShardIds      []string `json:"shard_ids"`
	Status        int      `json:"status"`
	BuyerAddr     string   `json:"buyer_addr"`
	Ctime         int64    `json:"ctime"`
	PayTime       int64    `json:"pay_time"`
	RefundReason  string   `json:"refund_reason"`
	RefundMessage string   `json:"refund_message"`
	RefundCtime   int64    `json:"refund_ctime"`
	RefundRtime   int64    `json:"refund_rtime"`
}

type QueryRefundParam struct {
	AccountId string
	Rid       int64
}

type QueryRefundResp struct {
	BaseResp
	RefundOrder RefundOverview `json:"refund_order"`
}

type ListRefundParam struct {
	AccountId string
	Search    string
	Status    int
	Page      int
	Size      int
}

type ListRefundResp struct {
	BaseResp
	RefundList  []RefundOverview `json:"list"`
	TotalCnt    int64            `json:"total_cnt"`
	TotalAmount int64            `json:"total_amount"`
	TotalPrice  int64            `json:"total_price"`
}

type QueryRefundApplyCntParam struct {
	AccountId string
}

type QueryRefundApplyCntResp struct {
	BaseResp
	TotalCnt int64 `json:"total_cnt"`
}

type ApproveRefundBody struct {
	Rid     []string `json:"rid"`
	Approve bool     `json:"approve"`
	Message string   `json:"message"`
}

type ApproveRefundParam struct {
	AccountId string `json:"account_id"`
	UserId    string `json:"user_id"`
	ApproveRefundBody
}

type ApproveRefundResp struct {
	BaseResp
	FailList []string `json:"fail_list"`
	FailCnt  int64    `json:"fail_cnt"`
}

type QueryStoreApplyListParam struct {
	Page   int64
	Size   int64
	Status int64 // 流程状态

	Search string
}

// StoreApplyInfo 店铺审核列表项
type StoreApplyInfo struct {
	Id        int64  `json:"id"`
	AppId     int64  `json:"app_id"`
	Business  string `json:"business"`
	StoreId   int64  `json:"store_id"`
	StoreName string `json:"store_name"`
	Logo      string `json:"logo"`
	LogoView  string `json:"logo_view"`
	Cover     string `json:"cover"`
	CoverView string `json:"cover_view"`
	Phone     string `json:"phone"`
	Wechat    string `json:"wechat"`
	ShortDesc string `json:"short_desc"`
	Operator  string `json:"operator"`
	Message   string `json:"message"`
	Status    int64  `json:"status"`
	Ctime     int64  `json:"ctime"`
	Mtime     int64  `json:"mtime"`
}

type QueryStoreApplyListResp struct {
	BaseResp
	TotalCnt int64            `json:"total_cnt"`
	List     []StoreApplyInfo `json:"list"`
}

type QueryAssetApplyListParam struct {
	Page   int64
	Size   int64
	Status int64 // 流程状态

	Search string
}

// AssetApplyInfo 藏品审核列表项
type AssetApplyInfo struct {
	Id            int64    `json:"id"`
	AppId         int64    `json:"app_id"`
	Business      string   `json:"business"`
	AssetId       string   `json:"asset_id"`
	Title         string   `json:"title"`
	AssetCate     int      `json:"asset_cate"`
	ShortDesc     string   `json:"short_desc"`
	LongDesc      string   `json:"long_desc"`
	Thumb         []string `json:"thumb"`
	ThumbView     []string `json:"thumb_view"`
	ImgDesc       []string `json:"img_desc"`
	ImgDescView   []string `json:"img_desc_view"`
	AssetUrl      []string `json:"asset_url"`
	AssetUrlView  []string `json:"asset_url_view"`
	Amount        int      `json:"amount"`
	Price         int64    `json:"price"`
	Operator      string   `json:"operator"`
	Message       string   `json:"message"`
	Status        int64    `json:"status"`
	Copyright     []string `json:"copyright"`
	CopyrightView []string `json:"copyright_view"`
	Ctime         int64    `json:"ctime"`
	Mtime         int64    `json:"mtime"`
}

type QueryAssetApplyListResp struct {
	BaseResp
	TotalCnt int64            `json:"total_cnt"`
	List     []AssetApplyInfo `json:"list"`
}

type QueryActApplyListParam struct {
	Page   int64
	Size   int64
	Status int64 // 流程状态

	Search string
}

// ActivityApplyInfo 活动审核列表项
type ActApplyInfo struct {
	Id           int64    `json:"id"`
	AppId        int64    `json:"app_id"`
	Business     string   `json:"business"`
	ActId        int64    `json:"act_id"`
	ActName      string   `json:"act_name"`
	Issuer       string   `json:"issuer"`
	ShortDesc    string   `json:"short_desc"`
	Weight       int64    `json:"weight"`
	Thumb        []string `json:"thumb"`
	ThumbView    []string `json:"thumb_view"`
	ImgDesc      []string `json:"img_desc"`
	ImgDescView  []string `json:"img_desc_view"`
	ShareImg     []string `json:"share_img"`
	ShareImgView []string `json:"share_img_view"`
	Start        int64    `json:"start"`
	End          int64    `json:"end"`
	Operator     string   `json:"operator"`
	Message      string   `json:"message"`
	Status       int64    `json:"status"`
	Ctime        int64    `json:"ctime"`
	Mtime        int64    `json:"mtime"`
}

type QueryActApplyListResp struct {
	BaseResp
	TotalCnt int64          `json:"total_cnt"`
	List     []ActApplyInfo `json:"list"`
}

type StoreApproveParam struct {
	FlowId  int64 // 流程Id
	Approve bool
	Message string
}

type AssetApproveParam = StoreApproveParam

type ActApproveParam = StoreApproveParam

type BanStoreParam struct {
	ObjectId int64 // 店铺Id|藏品Id|活动Id
	Ban      bool  // true:封禁 false:解封
	Message  string
}

type BanAssetParam = BanStoreParam

type BanActParam = BanStoreParam

type QueryStoreAdminListParam struct {
	AppId        int64
	StoreId      int64
	StoreName    string
	Status       int
	WalletStatus int
	Page         int
	Size         int
}

type QueryStoreAdminListResp struct {
	BaseResp
	TotalCnt int64           `json:"total_cnt"`
	List     []StoreOverview `json:"list"`
}

type QueryAssetAdminListParam struct {
	AppId     int64
	AssetId   int64
	Title     string
	FileType  string
	Status    int
	AssetCate int
	Deleted   int
	Page      int
	Size      int
}

type QueryAssetAdminListResp struct {
	BaseResp
	TotalCnt int64           `json:"total_cnt"`
	List     []AssetOverview `json:"list"`
}

type QueryActAdminListParam struct {
	AppId        int64
	ActId        int64
	StoreId      int64
	ActName      string
	Status       int
	Online       int
	Deleted      int
	WalletStatus int
	Page         int
	Size         int
}

type QueryActAdminListResp struct {
	BaseResp
	TotalCnt int64         `json:"total_cnt"`
	List     []ActOverview `json:"list"`
}

type QueryStoreDetailParam struct {
	StoreId int64 `json:"store_id"`
}

type StoreDetail struct {
	StoreOverview
	XStoreStatus int   `json:"xstore_status"`
	Likes        int64 `json:"likes"`
}

type QueryStoreDetailResp struct {
	BaseResp
	StoreDetail `json:"store"`
}

type QueryAssetDetailParam struct {
	AssetId int64 `json:"asset_id"`
}

type AssetDetail struct {
	AssetOverview
	Deleted int    `json:"deleted"`
	Addr    string `json:"addr"`
	TxId    string `json:"tx_id"`
}

type QueryAssetDetailResp struct {
	BaseResp
	AssetDetail `json:"asset"`
}

type QueryActDetailParam struct {
	ActId int64 `json:"act_id"`
}

type ActDetail struct {
	ActOverview
	XStorePublishStatus int `json:"xstore_publish_status"`
	XStoreStatus        int `json:"xstore_status"`
}

type QueryActDetailResp struct {
	BaseResp
	ActDetail `json:"activity"`
}

type PushStoreParam struct {
	StoreId int64
	OpType  int
}

type PushActParam struct {
	ActId  int64
	OpType int
}

type AlterAssetExtInfoParam struct {
	AssetId int64  `json:"asset_id"`
	ExtInfo string `json:"ext_info"`
}

type AlterActExtInfoParam struct {
	ActId   int64  `json:"act_id"`
	ExtInfo string `json:"ext_info"`
}

type AlterBindAstExtInfoParam struct {
	ActId   int64  `json:"act_id"`
	AssetId int64  `json:"asset_id"`
	ExtInfo string `json:"ext_info"`
}

type AlterAssetCompScriptParam struct {
	AssetId    int64  `json:"asset_id"`
	CompScript string `json:"comp_script"`
}

type AlterAssetDivScriptParam struct {
	AssetId   int64  `json:"asset_id"`
	DivScript string `json:"div_script"`
}

type AlterAssetCouponScriptParam struct {
	AssetId      int64  `json:"asset_id"`
	CouponScript string `json:"coupon_script"`
}
