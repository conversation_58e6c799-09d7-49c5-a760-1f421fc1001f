package lab

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type LabClient struct {
	addr *addr.Addr
}

func NewLabClient(addr *addr.Addr) *LabClient {
	return &LabClient{addr}
}

func (t *LabClient) IntraRecycleAssetOperate(param *RecycleAssetParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("shard_id", fmt.Sprintf("%d", param.ShardId))
	v.Set("task_id", fmt.Sprintf("%d", param.TaskId))
	v.Set("address", param.Address)
	v.Set("asset_info", param.AssetInfo)
	v.Set("ext_info", param.ExtInfo)

	body := v.Encode()
	reqRes, err := t.doRequest(LabApiRecycleAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab to recycle asset fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) SellItem(param *SellItemParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("addr", param.Addr)
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("shard_id", fmt.Sprintf("%d", param.ShardId))
	v.Set("type", fmt.Sprintf("%d", param.Type))
	v.Set("sale_points", fmt.Sprintf("%d", param.SalePoints))
	v.Set("asset_info", param.AssetInfo)
	v.Set("ext_info", param.ExtInfo)

	body := v.Encode()
	reqRes, err := t.doRequest(LabApiSellItem, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab to sell item fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) AlterItem(param *AlterItemParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("sale_id", fmt.Sprintf("%d", param.SaleId))
	v.Set("sale_points", fmt.Sprintf("%d", param.SalePoints))

	body := v.Encode()
	reqRes, err := t.doRequest(LabApiAlterItem, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab to alter item fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) WithdrawItem(param *WithdrawItemParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("sale_id", fmt.Sprintf("%d", param.SaleId))

	body := v.Encode()
	reqRes, err := t.doRequest(LabApiWithdrawItem, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab to withdraw item fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) ExchangeItem(param *ExchangeItemParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("addr", param.Addr)
	v.Set("sale_id", fmt.Sprintf("%d", param.SaleId))
	v.Set("sale_points", fmt.Sprintf("%d", param.SalePoints))

	body := v.Encode()
	reqRes, err := t.doRequest(LabApiExchangeItem, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab to exchange item fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) QueryItem(param *QueryItemParam) (*QueryItemResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("sale_id", fmt.Sprintf("%d", param.SaleId))

	body := v.Encode()
	reqRes, err := t.doRequest(LabApiQryItem, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab to query item fail.err:%v", err)
	}
	var result QueryItemResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) ListItem(param *ListItemParam) (*ListItemResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	v.Set("cursor", param.Cursor)

	body := v.Encode()
	reqRes, err := t.doRequest(LabApiListItem, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab to list item fail.err:%v", err)
	}
	var result ListItemResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) ListOnSaleItem(param *ListOnSaleParam) (*ListItemResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("type", fmt.Sprintf("%d", param.Type))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("sort_key", fmt.Sprintf("%d", param.SortKey))
	v.Set("order", fmt.Sprintf("%d", param.Order))
	v.Set("max_price", fmt.Sprintf("%d", param.MaxPrice))
	v.Set("min_price", fmt.Sprintf("%d", param.MinPrice))
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	v.Set("cursor", param.Cursor)

	body := v.Encode()
	reqRes, err := t.doRequest(LabApiListOnSaleItem, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab to list on sale item fail.err:%v", err)
	}
	var result ListItemResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) SearchOnSale(param *SearchOnSaleParam) (*SearchOnSaleResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("src", fmt.Sprintf("%d", param.Src))
	v.Set("key_word", param.KeyWord)
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	v.Set("cursor", param.Cursor)

	body := v.Encode()
	reqRes, err := t.doRequest(LabApiSearchOnSale, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab to search on sale item fail.err:%v", err)
	}
	var result SearchOnSaleResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) ComposeShard(param *ComposeShardParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("addr", param.Addr)
	v.Set("task_id", fmt.Sprintf("%d", param.TaskId))
	v.Set("asset_info", param.AssetInfo)
	v.Set("consume_list", param.ConsumeList)

	body := v.Encode()
	reqRes, err := t.doRequest(LabApiComposeShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab to compose shard fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) ListSellByAssets(uid int64, assetIds []int64) (*ListSellByAssetsResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if uid < 1 || len(assetIds) < 1 {
		return nil, nil, errors.New("param error")
	}
	assetIdsStr, err := json.Marshal(assetIds)
	if err != nil {
		return nil, nil, fmt.Errorf("param error, asset_ids json encode failed")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", uid))
	v.Set("asset_ids", string(assetIdsStr))

	body := v.Encode()
	reqRes, err := t.doRequest(LabApiListSellByAsts, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab to list sell by assets fail.err:%v", err)
	}
	var result ListSellByAssetsResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) CreateTransferTask(param *CreateTransParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("to_uid", fmt.Sprintf("%d", param.ToUid))
	v.Set("task_id", fmt.Sprintf("%d", param.TaskId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("shard_id", fmt.Sprintf("%d", param.ShardId))
	v.Set("use_coupon", fmt.Sprintf("%d", param.UseCoupon))
	v.Set("from_addr", param.FromAddr)
	v.Set("to_addr", param.ToAddr)
	v.Set("coupon_addr", param.CouponAddr)
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiCreateTrans, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec trans fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) ListTransRecord(uid int64, typ, limit int, cursor string) (*ListTransRecordResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if uid < 1 || typ < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", uid))
	v.Set("type", fmt.Sprintf("%d", typ))
	v.Set("limit", fmt.Sprintf("%d", limit))
	v.Set("cursor", cursor)
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiListTransRec, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for list trans record fail.err:%v", err)
	}
	var result ListTransRecordResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) OpenBlindBox(param *OpenBoxParam) (*OpenBoxResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("order_id", fmt.Sprintf("%d", param.Oid))
	v.Set("box_id", fmt.Sprintf("%d", param.BoxId))
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("series", fmt.Sprintf("%d", param.Series))
	v.Set("payprice", fmt.Sprintf("%d", param.Price))
	v.Set("buyer_addr", param.Addr)
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiOpenBox, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec open blind box fail.err:%v", err)
	}
	var result OpenBoxResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) QueryBlindBox(param *QueryBoxParam) (*QueryBoxResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("task_id", fmt.Sprintf("%d", param.TaskId))
	v.Set("start", fmt.Sprintf("%d", param.Start))
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiQueryBox, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec query blind box fail.err:%v", err)
	}
	var result QueryBoxResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) MallHasNew(src int) (*MallHasNewResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if src <= 0 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("src", fmt.Sprintf("%d", src))
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiMallHasNew, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec mall has new fail.err:%v", err)
	}
	var result MallHasNewResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) ListGoods(param *ListGoodsParam) (*ListGoodsResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("src", fmt.Sprintf("%d", param.Src))
	v.Set("status", fmt.Sprintf("%d", param.Status))
	v.Set("sort_key", fmt.Sprintf("%d", param.SortKey))
	v.Set("order", fmt.Sprintf("%d", param.Order))
	v.Set("min_price", fmt.Sprintf("%d", param.MinPrice))
	v.Set("max_price", fmt.Sprintf("%d", param.MaxPrice))
	v.Set("cursor", param.Cursor)
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	body := v.Encode()
	reqRes, err := t.doRequest(LapApiListGoods, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec list mall goods fail.err:%v", err)
	}
	var result ListGoodsResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) QueryGoods(assetId int64) (*QueryGoodsResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if assetId <= 0 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiQueryGoods, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec query mall goods fail.err:%v", err)
	}
	var result QueryGoodsResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) BuyGoods(param *BuyGoodsParam) (*BuyGoodsResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("addr", param.Addr)
	v.Set("oid", param.Oid)
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiBuyGoods, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec buy mall goods fail.err:%v", err)
	}
	var result BuyGoodsResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) PlayGashapon(param *PlayGashaponParam) (*GashaponResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("price", fmt.Sprintf("%d", param.Price))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	v.Set("addr", param.Addr)
	v.Set("user_name", param.UserName)
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiPlayGashapon, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec play gashapon fail.err:%v", err)
	}
	var result GashaponResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) RecentRaffle() (*RecentRaffleResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	reqRes, err := t.doRequest(LabApiRecentRaffle, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec get gashapon winners fail.err:%v", err)
	}
	var result RecentRaffleResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) ListAllGoods(param *ListAllGoodsParam) (*ListAllGoodsResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("page", fmt.Sprintf("%d", param.Page))
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	body := v.Encode()
	reqRes, err := t.doRequest(LapApiListAllGoods, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec list all goods fail.err:%v", err)
	}
	var result ListAllGoodsResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) CreateGoods(param *CreateGoodsParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("asset_type", fmt.Sprintf("%d", param.AssetType))
	v.Set("asset_cate", fmt.Sprintf("%d", param.AssetCate))
	v.Set("price", fmt.Sprintf("%d", param.Price))
	v.Set("amount", fmt.Sprintf("%d", param.Amount))
	v.Set("title", param.Title)
	v.Set("thumb", param.Thumb)
	v.Set("short_desc", param.ShortDesc)
	v.Set("asset_url", param.AssetUrl)
	v.Set("img_desc", param.ImgDesc)
	v.Set("long_desc", param.LongDesc)
	v.Set("asset_ext", param.AssetExt)
	v.Set("etime", fmt.Sprintf("%d", param.Etime))
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiCreateGoods, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec create goods fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) AlterGoods(param *AlterGoodsParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("price", fmt.Sprintf("%d", param.Price))
	v.Set("amount", fmt.Sprintf("%d", param.Amount))
	v.Set("etime", fmt.Sprintf("%d", param.Etime))
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiAlterGoods, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec alter goods fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) PublishGoods(assetId int64) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if assetId <= 0 {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiPublishGoods, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec publish goods fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) OffShelfGoods(assetId int64) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if assetId <= 0 {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiOffShelfGoods, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec offshelf goods fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) CreateCollectGroup(param *InsertGroupParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.CreateValid() {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("group_id", fmt.Sprintf("%d", param.GroupId))
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("group_type", fmt.Sprintf("%d", param.GroupType))
	v.Set("title", param.Title)
	v.Set("name", param.Name)
	v.Set("thumb", param.Thumb)
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiCreateGroup, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec create collect group fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) AddGroupAsset(param *InsertGroupParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.AddValid() {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("group_id", fmt.Sprintf("%d", param.GroupId))
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("title", param.Title)
	v.Set("thumb", param.Thumb)
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiAddGroupAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec add group asset fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) DelGroupAsset(param *DelGroupParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("group_id", fmt.Sprintf("%d", param.GroupId))
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiDelGroupAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec del group asset fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) UpdateGroupStatus(param *UpdateGroupStatusParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || param.GroupId <= 0 {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("status", fmt.Sprintf("%d", param.Status))
	v.Set("group_id", fmt.Sprintf("%d", param.GroupId))
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiUpdateGroup, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec update group status fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) SearchGroupByName(param *SearchGroupParam) (*ListGroupResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || param.Name == "" {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("group_type", fmt.Sprintf("%d", param.GroupType))
	v.Set("name", param.Name)
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiSearchGroup, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec search group by name fail.err:%v", err)
	}
	var result ListGroupResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) ListGroup(param *ListGroupParam) (*ListGroupResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("group_type", fmt.Sprintf("%d", param.GroupType))
	v.Set("page", fmt.Sprintf("%d", param.Page))
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiListGroup, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec page list group fail.err:%v", err)
	}
	var result ListGroupResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *LabClient) ListGroupAsset(groupId int64) (*ListGroupAssetResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if groupId <= 0 {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("group_id", fmt.Sprintf("%d", groupId))
	body := v.Encode()
	reqRes, err := t.doRequest(LabApiListGroupAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request lab for exec list group asset fail.err:%v", err)
	}
	var result ListGroupAssetResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal lab response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// post
func (t *LabClient) doRequest(api string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *LabClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
