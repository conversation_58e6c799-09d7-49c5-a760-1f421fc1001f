package lab

import (
	"fmt"
	"net/http"
)

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000
)

const (
	ListDefaultLimit = 20 // 默认拉取20条
	ListMaxLimit     = 30
)

const (
	SaleStatusOn       = 0 // 上架
	SaleStatusOff      = 7 // 下架
	SaleStatusExchange = 9 // 已兑换
	SaleTypeAsset      = 1 // 数字藏品
	SaleTypeCoupon     = 2 // 卡券
	SaleTypeObject     = 3 // 实物
)

const (
	// 积分商城API接口
	LabApiRecycleAsset   = "/internal/tasks/v1/recycleasset"
	LabApiSellItem       = "/internal/lab/v1/sell"
	LabApiListItem       = "/internal/lab/v1/listitem"
	LabApiAlterItem      = "/internal/lab/v1/alteritem"
	LabApiWithdrawItem   = "/internal/lab/v1/withdraw"
	LabApiListOnSaleItem = "/internal/lab/v1/listonsale"
	LabApiQryItem        = "/internal/lab/v1/qryitem"
	LabApiExchangeItem   = "/internal/lab/v1/spend"
	LabApiComposeShard   = "/internal/lab/v1/compose"
	LabApiListSellByAsts = "/internal/lab/v1/listsellbyasts"
	LabApiCreateTrans    = "/internal/trans/v1/exectrans"
	LabApiListTransRec   = "/internal/trans/v1/listrecord"
	LabApiSearchOnSale   = "/internal/lab/v1/searchonsale"
	LabApiOpenBox        = "/internal/lab/v1/openbox"
	LabApiQueryBox       = "/internal/lab/v1/querybox"
	LabApiMallHasNew     = "/internal/lab/v1/hasnew"
	LapApiListGoods      = "/internal/lab/v1/listgoods"
	LabApiQueryGoods     = "/internal/lab/v1/qrygoods"
	LabApiBuyGoods       = "/internal/lab/v1/buygoods"
	LabApiPlayGashapon   = "/internal/lab/v1/playgashapon"
	LabApiRecentRaffle   = "/internal/lab/v1/getraffle"
	LapApiListAllGoods   = "/internal/lab/v1/listallgoods"
	LabApiCreateGoods    = "/internal/lab/v1/creategoods"
	LabApiAlterGoods     = "/internal/lab/v1/altergoods"
	LabApiPublishGoods   = "/internal/lab/v1/publishgoods"
	LabApiOffShelfGoods  = "/internal/lab/v1/offshelfgoods"

	// 藏品集模板后台管理API
	LabApiCreateGroup    = "/internal/lab/v1/creategroup"
	LabApiAddGroupAsset  = "/internal/lab/v1/addgroupast"
	LabApiDelGroupAsset  = "/internal/lab/v1/delgroupast"
	LabApiUpdateGroup    = "/internal/lab/v1/updategroup"
	LabApiSearchGroup    = "/internal/lab/v1/searchgroup"
	LabApiListGroup      = "/internal/lab/v1/listgroup"
	LabApiListGroupAsset = "/internal/lab/v1/listgroupast"
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

// -------- 实验室玩法参数，包括回收、上架、下架、兑换、合成、转赠 --------
type RecycleAssetParam struct {
	Uid       int64  `json:"uid"`
	AssetId   int64  `json:"asset_id"`
	ShardId   int64  `json:"shard_id"`
	TaskId    int64  `json:"task_id"`
	Address   string `json:"address"`
	AssetInfo string `json:"asset_info"`
	ExtInfo   string `json:"ext_info"`
}

func (p *RecycleAssetParam) Valid() bool {
	return p.Uid > 0 && p.AssetId > 0 && p.ShardId > 0 && p.TaskId > 0 && p.Address != ""
}

type QueryItemParam struct {
	SaleId int64 `json:"sale_id"`
}

func (p *QueryItemParam) Valid() bool {
	return p.SaleId > 0
}

type QueryItemResp struct {
	BaseResp
	SaleItem
}

type SellItemParam struct {
	Uid        int64  `json:"uid"`
	Addr       string `json:"addr"`
	AssetId    int64  `json:"asset_id"`
	ShardId    int64  `json:"shard_id"`
	Type       int    `json:"type"`
	SalePoints int64  `json:"sale_points"`
	AssetInfo  string `json:"asset_info"`
	ExtInfo    string `json:"ext_info"`
}

func (p *SellItemParam) Valid() bool {
	if p.Addr == "" || p.AssetId < 1 || p.ShardId < 1 {
		return false
	}
	return true
}

type WithdrawItemParam struct {
	Uid    int64 `json:"uid"`
	SaleId int64 `json:"sale_id"`
}

func (p *WithdrawItemParam) Valid() bool {
	if p.SaleId < 1 {
		return false
	}
	return true
}

type AlterItemParam struct {
	Uid        int64 `json:"uid"`
	SaleId     int64 `json:"sale_id"`
	SalePoints int64 `json:"sale_points"`
}

func (p *AlterItemParam) Valid() bool {
	if p.SaleId < 1 {
		return false
	}
	return true
}

type ExchangeItemParam struct {
	Uid        int64  `json:"uid"`
	Addr       string `json:"addr"`
	SaleId     int64  `json:"sale_id"`
	SalePoints int64  `json:"sale_points"`
}

func (p *ExchangeItemParam) Valid() bool {
	if p.SaleId < 1 {
		return false
	}
	return true
}

type SearchOnSaleParam struct {
	Src     int    `json:"src"`
	KeyWord string `json:"key_word"`
	Limit   int    `json:"limit"`
	Cursor  string `json:"cursor"`
}

type SearchOnSaleResp struct {
	BaseResp
	List    []*SaleAssetItem
	HasMore int    `json:"has_more"`
	Cursor  string `json:"cursor"`
}

type SaleAssetItem struct {
	AssetId int64    `json:"asset_id"`
	Status  int      `json:"status"`
	Title   string   `json:"title"`
	Thumb   []string `json:"thumb"`
	Price   int64    `json:"price"`
	Mtime   int64    `json:"mtime"`
	Amount  int64    `json:"amount"`
}

type ListOnSaleParam struct {
	Type     int    `json:"type"`
	AssetId  int64  `json:"asset_id"`
	SortKey  int    `json:"sort_key"`
	Order    int    `json:"order"`
	MaxPrice int64  `json:"max_price"`
	MinPrice int64  `json:"min_price"`
	Limit    int    `json:"limit"`
	Cursor   string `json:"cursor"`
}

type ListItemParam struct {
	Uid    int64  `json:"uid"`
	Limit  int    `json:"limit"`
	Cursor string `json:"cursor"`
}

type SaleItem struct {
	SaleId     int64  `json:"sale_id"`
	Type       int    `json:"type"`
	AssetInfo  string `json:"asset_info"`
	AssetId    int64  `json:"asset_id"`
	ShardId    int64  `json:"shard_id"`
	SalePoints int64  `json:"sale_points"`
	Status     int    `json:"status"`
}

type ListItemResp struct {
	BaseResp
	List    []*SaleItem
	HasMore int    `json:"has_more"`
	Cursor  string `json:"cursor"`
}

type ComposeShardParam struct {
	Uid         int64  `json:"uid"`
	Addr        string `json:"addr"`
	TaskId      int64  `json:"task_id"`
	AssetInfo   string `json:"asset_info"`
	ConsumeList string `json:"consume_list"`
}

func (p *ComposeShardParam) Valid() bool {
	if p.Uid < 1 || p.Addr == "" || len(p.ConsumeList) < 1 {
		return false
	}
	return true
}

type ListSellByAssetsResp struct {
	BaseResp
	List []*AssetShards `json:"list"`
}

type AssetShards struct {
	AssetId int64 `json:"asset_id"`
	ShardId int64 `json:"shard_id"`
}

type CreateTransParam struct {
	Uid        int64
	ToUid      int64
	FromAddr   string
	ToAddr     string
	AssetId    int64
	ShardId    int64
	TaskId     int64
	UseCoupon  int
	CouponAddr string
}

func (p *CreateTransParam) Valid() error {
	if p.Uid < 1 || p.ToUid < 1 || p.FromAddr == "" || p.ToAddr == "" || p.AssetId < 1 || p.ShardId < 1 || p.TaskId < 1 {
		return fmt.Errorf("create trans param invalid")
	}
	return nil
}

type ListTransRecordResp struct {
	BaseResp
	List    []*TransRecordNode `json:"list"`
	HasMore int                `json:"has_more"`
	Cursor  string             `json:"cursor"`
}

type TransRecordNode struct {
	Uid      int64    `json:"uid"`
	FromAddr string   `json:"from_addr"`
	ToAddr   string   `json:"to_addr"`
	OpType   int      `json:"op_type"`
	AssetId  int64    `json:"asset_id"`
	ShardId  int64    `json:"shard_id"`
	Title    string   `json:"title"`
	Thumb    []string `json:"thumb"`
	Status   int      `json:"status"`
	CTime    int64    `json:"ctime"`
	MTime    int64    `json:"mtime"`
}

// -------- 盲盒单抽、连抽旧返回 --------
type OpenBoxParam struct {
	Uid    int64  `json:"uid"`
	Addr   string `json:"addr"`
	ActId  int64  `json:"act_id"`
	BoxId  int64  `json:"asset_id"`
	Oid    int64  `json:"order_id"`
	Series int    `json:"series"`
	Price  int    `json:"price"`
}

func (p *OpenBoxParam) Valid() bool {
	return p.Uid > 0 && p.Addr != "" && p.BoxId > 0 && p.Oid > 0 && p.Series > 0
}

type OpenBoxResp struct {
	BaseResp
	TaskId  int64    `json:"task_id"`
	AssetId int64    `json:"asset_id"`
	ShardId int64    `json:"shard_id"`
	Thumb   []string `json:"thumb"`
	Title   string   `json:"title"`
	Opened  int      `json:"opened"`
}

type QueryBoxParam struct {
	Uid    int64 `json:"uid"`
	TaskId int64 `json:"task_id"`
	Start  int   `json:"start"`
	Limit  int   `json:"limit"`
}

func (p *QueryBoxParam) Valid() bool {
	return p.Uid > 0 && p.TaskId > 0 && p.Start > 0 && p.Limit > 0
}

type QueryBoxResp struct {
	BaseResp
	List []*BlindBoxItem
	Number     int `json:"number"`
	Amount     int `json:"amount"`
	TaskStatus int `json:"task_status"`
}

type BlindBoxItem struct {
	AssetId int64    `json:"asset_id"`
	ShardId int64    `json:"shard_id"`
	Thumb   []string `json:"thumb"`
	Title   string   `json:"title"`
}

// -------- 自营积分商城相关参数，用户侧逻辑 --------
type MallHasNewResp struct {
	BaseResp
	HasNew   int   `json:"has_new"`
	LastTime int64 `json:"last_time"`
}

type ListGoodsParam struct {
	Src      int    `json:"src"`
	Status   int    `json:"status"`
	SortKey  int    `json:"sort_key"`
	Order    int    `json:"order"`
	MinPrice int64  `json:"min_price"`
	MaxPrice int64  `json:"max_price"`
	Cursor   string `json:"cursor"`
	Limit    int    `json:"limit"`
}

func (p *ListGoodsParam) Valid() bool {
	return p.Src > 0 && p.Status > 0 && p.SortKey > 0 && p.Order > 0 && p.Limit > 0
}

type ListGoodsResp struct {
	BaseResp
	List    []*SaleAssetItem
	HasMore int    `json:"has_more"`
	Cursor  string `json:"cursor"`
}

type ListAllGoodsParam struct {
	Page  int `json:"page"`
	Limit int `json:"limit"`
}

func (p *ListAllGoodsParam) Valid() bool {
	return p.Page > 0 && p.Limit > 0
}

type ListAllGoodsResp struct {
	BaseResp
	List     []*SaleAssetItem
	TotalCnt int `json:"total_cnt"`
}

type QueryGoodsResp struct {
	BaseResp
	Oid       string   `json:"oid"`
	AssetId   int64    `json:"asset_id"`
	AssetType int      `json:"asset_type"`
	Title     string   `json:"title"`
	Thumb     []string `json:"thumb"`
	ShortDesc string   `json:"short_desc"`
	AssetUrl  []string `json:"asset_url"`
	ImgDesc   []string `json:"img_desc"`
	Amount    int64    `json:"amount"`
	Price     int64    `json:"price"`
	ExtInfo   string   `json:"ext_info"`
	Mtime     int64    `json:"mtime"`
	HasStock  int      `json:"has_stock"`
}

type BuyGoodsParam struct {
	Uid     int64  `json:"uid"`
	AssetId int64  `json:"asset_id"`
	Addr    string `json:"addr"`
	Oid     string `json:"oid"`
}

func (p *BuyGoodsParam) Valid() bool {
	return p.Uid > 0 && p.AssetId > 0 && p.Addr != "" && p.Oid != ""
}

type BuyGoodsResp struct {
	BaseResp
	NextOid string `json:"next_oid"`
}

// -------- 扭蛋机相关接口 --------
type PlayGashaponParam struct {
	Uid      int64  `json:"uid"`
	Addr     string `json:"addr"`
	UserName string `json:"user_name"`
	Price    int64  `json:"price"`
	Oid      int64  `json:"oid"`
}

func (p *PlayGashaponParam) Valid() bool {
	return p.Uid > 0 && p.Price > 0 && p.Oid > 0 && p.Addr != ""
}

type GashaponResp struct {
	BaseResp
	TypeList   []int   `json:"type"`
	AssetList  []int64 `json:"asset_id"`
}

type GashaponWinner struct {
	UserName  string `json:"user_name"`
	AwardName string `json:"award_name"`
}

type RecentRaffleResp struct {
	BaseResp
	Winners int               `json:"winners"`
	List    []*GashaponWinner `json:"list"`
}

// -------- 自营积分商城相关参数，后台管理逻辑 --------
type CreateGoodsParam struct {
	AssetId   int64  `json:"asset_id"`
	AssetType int    `json:"asset_type"`
	AssetCate int    `json:"asset_cate"`
	Title     string `json:"title"`
	Thumb     string `json:"thumb"`
	ShortDesc string `json:"short_desc"`
	AssetUrl  string `json:"asset_url"`
	ImgDesc   string `json:"img_desc"`
	LongDesc  string `json:"long_desc"`
	AssetExt  string `json:"asset_ext"`
	Amount    int64  `json:"amount"`
	Price     int64  `json:"price"`
	Etime     int64  `json:"etime"`
}

func (p *CreateGoodsParam) Valid() bool {
	if p.Title == "" || p.Thumb == "" || p.ShortDesc == "" || p.ImgDesc == "" || p.AssetUrl == ""{
		return false
	}
 	return p.AssetId > 0 && p.AssetType > 0 && p.AssetCate > 0 && p.Amount > 0 && p.Price > 0
}

type AlterGoodsParam struct {
	AssetId int64 `json:"asset_id"`
	Amount  int64 `json:"amount"`
	Price   int64 `json:"price"`
	Etime   int64 `json:"etime"`
}

func (p *AlterGoodsParam) Valid() bool {
	return p.AssetId > 0 && p.Amount > 0 && p.Price > 0
}

// -------- 藏品集模板，后台管理逻辑 --------
// InsertGroupParam 藏品集模板创建时需要关联一个藏品, 添加时也需要关联藏品
type InsertGroupParam struct {
	GroupId   int64  `json:"group_id"`
	GroupType int    `json:"group_type"`
	Name      string `json:"name"`
	AppId     int64  `json:"app_id"`
	AssetId   int64  `json:"asset_id"`
	Title     string `json:"title"`
	Thumb     string `json:"thumb"`
}

func (p *InsertGroupParam) CreateValid() bool {
	return p.AppId > 0 && p.AssetId > 0 && p.Title != "" && p.Thumb != "" && p.GroupType > 0 && p.Name != ""
}

func (p *InsertGroupParam) AddValid() bool {
	return p.AppId > 0 && p.AssetId > 0 && p.Title != "" && p.Thumb != "" && p.GroupId > 0
}

type DelGroupParam struct {
	GroupId int64 `json:"group_id"`
	AssetId int64 `json:"asset_id"`
}

func (p *DelGroupParam) Valid() bool {
	return p.GroupId > 0 && p.AssetId > 0
}

type SearchGroupParam struct {
	GroupType int    `json:"group_type"`
	Name      string `json:"name"`
}

func (p *SearchGroupParam) Valid() bool {
	return p.Name != ""
}

type UpdateGroupStatusParam struct {
	GroupId int64 `json:"group_id"`
	Status  int   `json:"status"`
}

type ListGroupParam struct {
	GroupType int `json:"group_type"`
	Page      int `json:"page"`
	Limit     int `json:"limit"`
}

func (p *ListGroupParam) Valid() bool {
	return p.Page > 0 && p.Limit > 0
}

type GroupInfo struct {
	GroupId   int64  `json:"group_id"`
	GroupType int    `json:"group_type"`
	Status    int    `json:"status"`
	Name      string `json:"name"`
	Ctime     int64  `json:"ctime"`
	Mtime     int64  `json:"mtime"`
}

type ListGroupResp struct {
	BaseResp
	List  []*GroupInfo `json:"list"`
	Total int          `json:"total"`
}

type GroupAssetInfo struct {
	AppId   int64    `json:"app_id"`
	AssetId int64    `json:"asset_id"`
	Title   string   `json:"title"`
	Thumb   []string `json:"thumb"`
	Ctime   int64    `json:"ctime"`
	Mtime   int64    `json:"mtime"`
}

type ListGroupAssetResp struct {
	BaseResp
	List  []*GroupAssetInfo `json:"list"`
	Total int               `json:"total"`
}
