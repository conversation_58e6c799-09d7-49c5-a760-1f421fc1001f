package market

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type MarketClient struct {
	addr *addr.Addr
}

func NewMarketClient(addr *addr.Addr) *MarketClient {
	return &MarketClient{addr}
}

func (t *MarketClient) CreateItem(param *CreateItemParam) (*CreateItemResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("sale_cnt", fmt.Sprintf("%d", param.SaleCnt))
	v.Set("price", fmt.Sprintf("%d", param.Price))
	v.Set("sale_item", param.SaleItem)
	v.Set("nonce", fmt.Sprintf("%d", param.Nonce))
	v.Set("create_addr", param.CreateAddr)
	v.Set("create_sign", param.OwnerSign)
	v.Set("create_pkey", param.OwnerPkey)
	body := v.Encode()
	reqRes, err := t.doRequest(MarketApiCreateItem, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset market for create item fail.err:%v", err)
	}
	var result CreateItemResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal market response for create item fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *MarketClient) OffShelveItem(param *OffShelveItemParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("sale_id", fmt.Sprintf("%d", param.SaleId))
	body := v.Encode()
	reqRes, err := t.doRequest(MarketApiOffShelve, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset market for offshelve item fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal market response for offshelve item fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *MarketClient) FilterSaleItem(param *FilterItemParam) (*FilterResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("filter_id", fmt.Sprintf("%d", param.FilterId))
	v.Set("filter_cond", param.FilterCond)
	v.Set("cursor", param.Cursor)
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(MarketApiListByFilter, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset market for filter sale item fail.err:%v", err)
	}
	var result FilterResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal market response for filter item fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *MarketClient) GetSaleItem(param *GetItemParam) (*GetResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("sale_id", fmt.Sprintf("%d", param.SaleId))
	body := v.Encode()
	reqRes, err := t.doRequest(MarketApiGetItem, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset market for get sale item fail.err:%v", err)
	}
	var result GetResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal market response for get sale item fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *MarketClient) QuerySaleItem(param *QueryItemParam) (*QueryResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	body := v.Encode()
	reqRes, err := t.doRequest(MarketApiQueryItem, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset market for query sale item fail.err:%v", err)
	}
	var result QueryResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal market response for query sale item fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *MarketClient) ListItems(param *ListItemsParam) (*ListItemsResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("addr", param.Addr)
	v.Set("status", fmt.Sprintf("%d", param.Status))
	v.Set("page", fmt.Sprintf("%d", param.Page))
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(MarketApiListItems, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset market for list sale item fail.err:%v", err)
	}
	var result ListItemsResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal market response for list sale item fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *MarketClient) UpdateStock(param *UpdateStockParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("sale_id", fmt.Sprintf("%d", param.SaleId))
	v.Set("op_type", fmt.Sprintf("%d", param.OpType))
	v.Set("op_num", fmt.Sprintf("%d", param.OpNum))
	v.Set("op_addr", param.OpAddr)
	body := v.Encode()
	reqRes, err := t.doRequest(MarketApiUpdateStock, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset market for update stock fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal market response for update stock fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// post
func (t *MarketClient) doRequest(api string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request product fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request product http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *MarketClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
