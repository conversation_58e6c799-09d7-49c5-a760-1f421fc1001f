package market

import (
	"fmt"
	"testing"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
)

var hosts string = "*************:8370"
var appId int64 = 100839
var assetId int64 = 954890473069207
var saleId int64 = 76023429400201703

func TestCreateItem(t *testing.T) {
	saleItem := `{"sale_num":10,"title":"haha","thumb":"gaga","short_desc":"qiqi",
					"ori_price":40000,"sale_chan":1,"view_type":0,"asset_cate":1,"sale_type":1, "tx_id":"sdfsfsad12313221"}`
	createItemParam := &CreateItemParam{
		AppId:      appId,
		AssetId:    1631863758,
		SaleCnt:    100,
		Price:      100,
		Nonce:      time.Now().Unix(),
		SaleItem:   saleItem,
		CreateAddr: "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY",
		OwnerSign:  "3045022049bf3aed2d15f1034dddb56634a4739d13fb1b6ea3c48fd82e16f8c5274942490221008a2cd0a300f60f20324cd34675ca01d110a9b2f074f19bf369d40540789c7cc0",
		OwnerPkey:  "{\"Curvname\":\"P-256\",\"X\":36505150171354363400464126431978257855318414556425194490762274938603757905292,\"Y\":79656876957602994269528255245092635964473154458596947290316223079846501380076}",
	}
	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewMarketClient(a).CreateItem(createItemParam)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestOffShelveItem(t *testing.T) {
	offShelveParam := &OffShelveItemParam{
		AppId:  appId,
		SaleId: 65666399233739239,
	}
	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewMarketClient(a).OffShelveItem(offShelveParam)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestFilterItem(t *testing.T) {
	filterParam := &FilterItemParam{
		AppId:    appId,
		FilterId: 100100,
	}
	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewMarketClient(a).FilterSaleItem(filterParam)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)

	filterParam = &FilterItemParam{
		AppId:      appId,
		FilterId:   100200,
		FilterCond: "{\"sale_typ\":1}",
	}

	resp, reqInfo, err = NewMarketClient(a).FilterSaleItem(filterParam)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestGetItem(t *testing.T) {
	getParam := &GetItemParam{
		AppId:  appId,
		SaleId: saleId,
	}

	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewMarketClient(a).GetSaleItem(getParam)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestQueryItem(t *testing.T) {
	queryParam := &QueryItemParam{
		AppId:   appId,
		AssetId: assetId,
	}

	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewMarketClient(a).QuerySaleItem(queryParam)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestListItems(t *testing.T) {
	listParam := &ListItemsParam{
		AppId:  appId,
		Addr:   "eSkqeb3kwaRk6r6DSca6cA3VwdzrbatWZ",
		Status: 1,
	}

	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewMarketClient(a).ListItems(listParam)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestUpdateStock(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	param := &UpdateStockParam{
		AppId:   appId,
		AssetId: assetId,
		SaleId:  saleId,
		OpType:  1, //加
		OpNum:   3,
		OpAddr:  "dpzuVdosQrF2kmzumhVeFQZa1aYcdgFpZ",
	}
	resp, reqInfo, err := NewMarketClient(a).UpdateStock(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
	param.OpType = 2 //减
	param.OpNum = 2
	resp, reqInfo, err = NewMarketClient(a).UpdateStock(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}
