package market

import (
	"net/http"
)

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000

	MaxLimit = 50
)

const (
	MarketApiCreateItem   = "/internal/market/v1/createitem"
	MarketApiOffShelve    = "/internal/market/v1/offshelve"
	MarketApiListItems    = "/internal/market/v1/listitems"
	MarketApiGetItem      = "/internal/market/v1/queryiteminfo"
	MarketApiQueryItem    = "/internal/market/v1/listbyasset"
	MarketApiListByFilter = "/internal/market/v1/listbyfilter"
	MarketApiUpdateStock  = "/internal/market/v1/updatestock"
)

type AssetInfo struct {
	Id         int64  `json:"id"`
	AssetId    int64  `json:"asset_id"`
	AssetCate  int    `json:"asset_cate"`
	AssetFrom  int    `json:"asset_from"`
	Title      string `json:"title"`
	Thumb      string `json:"thumb"`
	ShortDesc  string `json:"short_desc"`
	CreateAddr string `json:"create_addr"`
	GroupId    int64  `json:"group_id"`
	TxId       string `json:"tx_id"`
}

type AssetSaleFullInfo struct {
	AssetId    int64  `json:"asset_id"`
	ShardsId   int64  `json:"shards_id"`
	SaleId     int64  `json:"sale_id"`
	SaleNum    int64  `json:"sale_num"`
	SaleChan   int    `json:"sale_chan"`
	SaleInfo   string `json:"sale_info"`
	SaleType   int    `json:"sale_type"`
	SaleCnt    int    `json:"sale_cnt"`
	AssetCate  int    `json:"asset_cate"`
	AssetFrom  int    `json:"asset_from"`
	Title      string `json:"title"`
	Thumb      string `json:"thumb"`
	ShortDesc  string `json:"short_desc"`
	Status     int    `json:"status"`
	Price      int64  `json:"price"`
	OriPrice   int64  `json:"ori_price"`
	CreateAddr string `json:"create_addr"`
	OwnerAddr  string `json:"owner_addr"`
	OwnerPkey  string `json:"owner_pkey"`
	GroupId    int64  `json:"group_id"`
	TxId       string `json:"tx_id"`
}

type SaleItemInfo struct {
	Oid       int64  `json:"oid"`
	SaleId    int64  `json:"sale_id"`
	SaleNum   int64  `json:"sale_num"`
	SaleCnt   int    `json:"sale_cnt"`
	SaleChan  int    `json:"sale_chan"`
	SaleType  int    `json:"sale_type"`
	SaleInfo  string `json:"sale_info"`
	Status    int    `json:"status"`
	Price     int64  `json:"price"`
	OriPrice  int64  `json:"ori_price"`
	OwnerAddr string `json:"owner_addr"`
}

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

type CreateItemParam struct {
	AppId      int64  `json:"app_id"`
	AssetId    int64  `json:"asset_id"`
	SaleCnt    int    `json:"sale_cnt"`
	Price      int64  `json:"price"`
	Nonce      int64  `json:"nonce"`
	SaleItem   string `json:"sale_item"`
	CreateAddr string `json:"create_addr"`
	OwnerSign  string `json:"create_sign"`
	OwnerPkey  string `json:"create_pkey"`
}

func (t *CreateItemParam) IsValid() bool {
	if t.AppId < 1 || t.AssetId < 1 || t.SaleCnt < 1 || t.Nonce < 1 || t.Price < 0 ||
		t.SaleItem == "" || t.CreateAddr == "" || t.OwnerSign == "" || t.OwnerPkey == "" {
		return false
	}
	return true
}

type CreateItemResp struct {
	BaseResp
	SaleId int64 `json:"sale_id"`
}

type OffShelveItemParam struct {
	AppId  int64 `json:"app_id"`
	SaleId int64 `json:"sale_id"`
}

func (t *OffShelveItemParam) IsValid() bool {
	if t.AppId < 1 || t.SaleId < 1 {
		return false
	}
	return true
}

type FilterItemParam struct {
	AppId      int64  `json:"app_id"`
	FilterId   int    `json:"filter_id"`
	FilterCond string `json:"filter_cond"`
	Limit      int    `json:"limit"`
	Cursor     string `json:"cursor"`
}

func (t *FilterItemParam) IsValid() bool {
	if t.AppId < 1 || t.Limit < 0 || t.Limit > MaxLimit {
		return false
	}
	return true
}

type FilterResp struct {
	BaseResp
	List    []*AssetSaleFullInfo `json:"list"`
	HasMore int                  `json:"has_more"`
	Cursor  string               `json:"cursor"`
}

type GetItemParam struct {
	AppId  int64 `json:"app_id"`
	SaleId int64 `json:"sale_id"`
}

func (t *GetItemParam) IsValid() bool {
	if t.AppId < 1 || t.SaleId < 1 {
		return false
	}
	return true
}

type GetResp struct {
	BaseResp
	Info *AssetSaleFullInfo `json:"info"`
}

type QueryItemParam struct {
	AppId   int64 `json:"app_id"`
	AssetId int64 `json:"asset_id"`
}

func (t *QueryItemParam) IsValid() bool {
	if t.AppId < 1 || t.AssetId < 1 {
		return false
	}
	return true
}

type QueryResp struct {
	BaseResp
	Meta *AssetInfo      `json:"meta"`
	List []*SaleItemInfo `json:"sale_list"`
}

type ListItemsParam struct {
	AppId  int64  `json:"app_id"`
	Addr   string `json:"addr"`
	Status int    `json:"status"`
	Page   int    `json:"page"`
	Limit  int    `json:"limit"`
}

func (t *ListItemsParam) IsValid() bool {
	if t.AppId < 1 || t.Addr == "" || t.Status < 0 || t.Limit < 0 || t.Limit > MaxLimit {
		return false
	}
	return true
}

type ListItemsResp struct {
	BaseResp
	List     []*AssetSaleFullInfo `json:"list"`
	TotalCnt int                  `json:"total_cnt"`
}

type UpdateStockParam struct {
	AppId   int64  `json:"app_id"`
	Oid     int64  `json:"oid"`
	AssetId int64  `json:"asset_id"`
	SaleId  int64  `json:"sale_id"`
	OpType  int    `json:"op_type"`
	OpNum   int    `json:"op_num"`
	OpAddr  string `json:"op_addr"`
}

func (t *UpdateStockParam) IsValid() bool {
	if t.AppId < 1 || t.AssetId < 1 || t.SaleId < 1 || t.OpNum < 1 {
		return false
	}
	return true
}
