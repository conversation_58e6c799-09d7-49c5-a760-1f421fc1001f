package nft

import (
	"encoding/json"
	"errors"
	"fmt"
	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
	"strconv"
)

type NftClient struct {
	addr *addr.Addr
}

func NewNftClient(addr *addr.Addr) *NftClient {
	return &NftClient{addr}
}

func (t *NftClient) CreateNFT(appId int64, param *NFTCreateRequest) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	body, err := json.Marshal(param)
	reqRes, err := t.doRequest(NftCreateNFT, appId, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset nft for create nft fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal nft response for create nft fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *NftClient) DeleteNFT(appId int64, param *NFTDeleteRequest) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	body, err := json.Marshal(param)
	reqRes, err := t.doRequest(NftDeleteNFT, appId, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset nft for delete nft fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal nft response for delete nft fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *NftClient) BanNFT(appId int64, param *NFTBanRequest) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	body, err := json.Marshal(param)
	reqRes, err := t.doRequest(NftBanNFT, appId, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset nft for ban nft fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal nft response for ban nft fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *NftClient) ListNFTs(appId int64, param *NFTQueryRequest) (*NFTQueryResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	body, err := json.Marshal(param)
	reqRes, err := t.doRequest(NftListNFT, appId, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset nft for list nft fail. err:%v", err)
	}
	var result NFTQueryResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal nft response for list nft fail. url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *NftClient) TransferShards(appId int64, param *ShardTransferRequest) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	body, err := json.Marshal(param)
	reqRes, err := t.doRequest(NftTransferShards, appId, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset nft shards for transfer nft fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal nft shards response for transfer nft fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *NftClient) DestroyShards(appId int64, param *ShardWriteOffRequest) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	body, err := json.Marshal(param)
	reqRes, err := t.doRequest(NftWriteOffShards, appId, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset nft shards for destroy nft fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal nft shards response for destroy nft fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *NftClient) ListShards(appId int64, param *ShardsQueryRequest) (*ShardsQueryResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	body, err := json.Marshal(param)
	reqRes, err := t.doRequest(NftListShards, appId, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset nft shards for list nft fail. err:%v", err)
	}
	var result ShardsQueryResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal nft shards response for list nft fail. url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *NftClient) ListShardsByAddr(appId int64, param *ListShardsByAddrReq) (*ListShardsByAddrResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	body, err := json.Marshal(param)
	reqRes, err := t.doRequest(NftListShardsByAddr, appId, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset nft shards for list nft fail. err:%v", err)
	}
	var result ListShardsByAddrResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal nft shards response for list nft fail. url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *NftClient) QueryShards(appId int64, param *QueryShardsReq) (*QueryShardResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	body, err := json.Marshal(param)
	reqRes, err := t.doRequest(NftQueryShard, appId, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset nft shards for query shard fail. err:%v", err)
	}
	var result QueryShardResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal nft shards response for query shrad fail. url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *NftClient) QueryShardsByOid(appId int64, param *QueryShardsByOidReq) (*QueryShardResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	body, err := json.Marshal(param)
	reqRes, err := t.doRequest(NftQueryShardByOid, appId, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset nft shards for query shard by oid fail. err:%v", err)
	}
	var result QueryShardResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal nft shards response for query shard by oid fail. url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// -------------------- 浏览器 ----------------------
func (t *NftClient) QueryAssetInfoList(param *QueryAssetInfoListRequest) (*QueryAssetResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	body, err := json.Marshal(param)
	reqRes, err := t.doRequest(NftQueryAssetInfoList, 100000, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset nft query asset fail. err:%v", err)
	}
	var result QueryAssetResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal nft query asset response fail. url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *NftClient) QueryTxBlock(param *QueryTxBlockRequest) (*QueryTxBlockResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	body, err := json.Marshal(param)
	reqRes, err := t.doRequest(NftQueryTxBlock, 100000, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset nft query tx_block fail. err:%v", err)
	}
	var result QueryTxBlockResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal nft query tx_block response fail. url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *NftClient) QueryFullAssetList(param *QueryFullAssetListRequest) (*QueryFullAssetListResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	body, err := json.Marshal(param)
	reqRes, err := t.doRequest(NftQueryFullAssetList, 100000, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset nft query full asset list fail. err:%v", err)
	}
	var result QueryFullAssetListResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal nft query full asset list response fail. url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// post
func (t *NftClient) doRequest(api string, appId int64, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	appIdHeader := strconv.FormatInt(appId, 10)
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;"
	header["app_id"] = appIdHeader
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request product fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request nft http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *NftClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
