package nft

import (
	"encoding/hex"
	"fmt"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/utils"
	"testing"
	"time"
)

var (
	hosts      string = "*************:8340"
	appId      int64  = 1000000
	privateKey string = "{\"Curvname\":\"P-256\",\"X\":36505150171354363400464126431978257855318414556425194490762274938603757905292,\"Y\":79656876957602994269528255245092635964473154458596947290316223079846501380076,\"D\":111497060296999106528800133634901141644446751975433315540300236500052690483486}"
	publicKey  string = "{\"Curvname\":\"P-256\",\"X\":36505150171354363400464126431978257855318414556425194490762274938603757905292,\"Y\":79656876957602994269528255245092635964473154458596947290316223079846501380076}"
)

func TestNftClient_CreateNFT(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	param := &NFTCreateRequest{
		AssetId:     20000,
		Address:     "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY",
		Amount:      10,
		Meta:        "{}",
		Nonce:       1,
		CallbackUrl: "",
		Pkey:        publicKey,
		Sign:        getSign("200001"),
	}
	resp, _, err := NewNftClient(a).CreateNFT(appId, param)
	assert.Nil(t, err)
	assert.Equal(t, resp.Errno, 0)
	assert.Equal(t, resp.Errmsg, "succ")
}

func TestNftClient_DeleteNFT(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	param := &NFTDeleteRequest{
		Address:     "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY",
		AssetId:     20000,
		Nonce:       1,
		CallbackUrl: "",
		Pkey:        publicKey,
		Sign:        getSign("200001"),
	}
	resp, _, err := NewNftClient(a).DeleteNFT(appId, param)
	assert.Nil(t, err)
	assert.Equal(t, resp.Errno, 0)
	assert.Equal(t, resp.Errmsg, "succ")
}

func TestNftClient_BanNFT(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	param := &NFTBanRequest{
		AssetIds:    "20000",
		CallbackUrl: "",
	}
	resp, _, err := NewNftClient(a).BanNFT(appId, param)
	assert.Nil(t, err)
	assert.Equal(t, resp.Errno, 0)
	assert.Equal(t, resp.Errmsg, "succ")
}

func TestNftClient_ListNFTs(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	timestamp := time.Now().UnixNano()
	param := &NFTQueryRequest{
		Address: "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY",
		Status:  "all",
		Cursor:  "",
		Limit:   10,
		Nonce:   int64(timestamp),
		Pkey:    publicKey,
		Sign:    getSign(fmt.Sprintf("TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY%d", timestamp)),
	}
	resp, _, err := NewNftClient(a).ListNFTs(appId, param)
	assert.Nil(t, err)
	assert.Equal(t, resp.Errno, 0)
	assert.Equal(t, resp.Errmsg, "succ")
}

func TestNftClient_TransferShards(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	param := &ShardTransferRequest{
		AssetId:     20000,
		ShardIds:    "200001;200002",
		Oid:         10000,
		From:        "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY",
		To:          "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY",
		Metas:       "",
		CallbackUrl: "",
		Pkey:        publicKey,
		Sign:        getSign("2000010000"),
	}
	resp, _, err := NewNftClient(a).TransferShards(appId, param)
	assert.Nil(t, err)
	assert.Equal(t, resp.Errno, 40005)
	assert.Equal(t, resp.Errmsg, "prohibit transfer")
}

func TestNftClient_DestroyShards(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	param := &ShardWriteOffRequest{
		Address:     "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY",
		AssetId:     20000,
		ShardIds:    "200001;200002",
		Nonce:       1,
		CallbackUrl: "",
		Pkey:        publicKey,
		Sign:        getSign("200001"),
	}
	resp, _, err := NewNftClient(a).DestroyShards(appId, param)
	assert.Nil(t, err)
	assert.Equal(t, resp.Errno, 40001)
	assert.Equal(t, resp.Errmsg, "param error")
}

func TestNftClient_ListShards(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	timestamp := time.Now().UnixNano()
	param := &ShardsQueryRequest{
		Address: "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY",
		Status:  "all",
		Cursor:  "",
		Limit:   10,
		Nonce:   int64(timestamp),
		Pkey:    publicKey,
		Sign:    getSign(fmt.Sprintf("TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY%d", timestamp)),
	}
	resp, _, err := NewNftClient(a).ListShards(appId, param)
	assert.Nil(t, err)
	assert.Equal(t, resp.Errno, 0)
	assert.Equal(t, resp.Errmsg, "succ")
}

func TestNftClient_QueryAssetInfoList(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	param := &QueryAssetInfoListRequest{AssetId: 35476653203559492}
	resp, _, err := NewNftClient(a).QueryAssetInfoList(param)
	assert.Nil(t, err)
	fmt.Println(resp.List)
}

func TestNftClient_QueryTxBlock(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	param := &QueryTxBlockRequest{TxId: "8091d6e7599328b2fc5e3e14d73977f26809858c74a86fe6ea08ba8db36a4d88"}
	resp, _, err := NewNftClient(a).QueryTxBlock(param)
	assert.Nil(t, err)
	fmt.Println(resp)
}

func TestNftClient_QueryFullAssetList(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	param := &QueryFullAssetListRequest{Addr: "hCJweg4UpVbCjw8sDbpqMXCmB49qctaNq"}
	resp, _, err := NewNftClient(a).QueryFullAssetList(param)
	assert.Nil(t, err)
	fmt.Println(resp)
}

func getSign(msg string) string {
	msgHash := utils.HashUsingSha256([]byte(msg))
	sign, _ := utils.SignECDSA(privateKey, string(msgHash))
	return hex.EncodeToString(sign)
}
