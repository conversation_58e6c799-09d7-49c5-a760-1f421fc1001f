package nft

import (
	"net/http"
)

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000
)

const (
	NftCreateNFT          = "/internal/nft/v1/asset/create"
	NftDeleteNFT          = "/internal/nft/v1/asset/delete"
	NftBanNFT             = "/internal/nft/v1/asset/ban"
	NftListNFT            = "/internal/nft/v1/asset/list"
	NftTransferShards     = "/internal/nft/v1/shard/transfer"
	NftWriteOffShards     = "/internal/nft/v1/shard/writeOff"
	NftListShards         = "/internal/nft/v1/shard/list"
	NftListShardsByAddr   = "/internal/nft/v1/shard/listbyaddr"
	NftQueryShard         = "/internal/nft/v1/shard/query"
	NftQueryShardByOid    = "/internal/nft/v1/shard/querybyoid"
	NftQueryAssetInfoList = "/internal/nft/v1/query/assetinfolist"
	NftQueryTxBlock       = "/internal/nft/v1/query/txblock"
	NftQueryFullAssetList = "/internal/nft/v1/query/fullassetlist"
)

const (
	ERROR_DUPLICATE_INSERT = 40002 // 重复向资产表插入NFT
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

type NFTCreateRequest struct {
	AssetId     int64  `json:"assetId"`     // 资产唯一id
	Address     string `json:"address"`     // 商户账户地址
	Amount      int64  `json:"amount"`      // 发行数量
	Meta        string `json:"meta"`        // 资产Meta信息json串
	Nonce       int64  `json:"nonce"`       // 随机数
	CallbackUrl string `json:"callbackUrl"` // 回调通知地址
	Pkey        string `json:"pkey"`        // 商户公钥
	Sign        string `json:"sign"`        // 买家使用私钥对msg签名 `msg=hash(assetId+nonce)`
}

type NFTDeleteRequest struct {
	Address     string `json:"address"`     // 商户地址
	AssetId     int64  `json:"assetId"`     // 资产唯一id
	Nonce       int64  `json:"nonce"`       // 随机数
	CallbackUrl string `json:"callbackUrl"` // 回调通知地址
	Pkey        string `json:"pkey"`        // 买家公钥
	Sign        string `json:"sign"`        // 买家对nftid的签名
}

type NFTBanRequest struct {
	AssetIds    string `json:"assetIds"`    // 资产唯一id,使用英文分号;拼接
	CallbackUrl string `json:"callbackUrl"` // 回调通知地址
}

type NFTQueryRequest struct {
	Address string `json:"address"` // 资产拥有者地址
	Status  string `json:"status"`  // 资产状态，选值包括all（全部）、onchain（上链中+已上链）、destroy（销毁中+已销毁）、ban（封禁中+已封禁）
	Cursor  string `json:"cursor"`  // 分页游标
	Limit   int    `json:"limit"`   // 分页数量,默认20, 最多50
	Nonce   int64  `json:"nonce"`   // 带有时间戳的随机数
	Pkey    string `json:"pkey"`    // 买家公钥
	Sign    string `json:"sign"`    // 买家对nftid的签名
}

type NFTQueryResp struct {
	BaseResp
	HasMore int             `json:"has_more"`
	Cursor  string          `json:"cursor"`
	List    []*NFTAssetInfo `json:"list"`
}

type NFTAssetInfo struct {
	Id        int64  `json:"id"`
	AppId     int64  `json:"app_id"`     // 平台id
	JobId     int64  `json:"job_id"`     // 任务表id
	OwnerAddr string `json:"owner_addr"` // 拥有账户地址
	AssetId   int64  `json:"asset_id"`   // nft id
	Amount    int64  `json:"amount"`     // 拥有数量
	Status    int8   `json:"status"`     // 资产状态. 0:已上链 1:发行中 2:销毁中 3:已销毁 4:封禁中 5已封禁 10异常
	TxId      string `json:"tx_id"`      // 链上交易hash
	Meta      string `json:"meta"`       // 资产元数据，json串
	ExtInfo   string `json:"ext_info"`   // 扩展字段
	Ctime     int64  `json:"ctime"`
	Mtime     int64  `json:"mtime"`
}

type ShardTransferRequest struct {
	AssetId     int64  `json:"assetId"`     // 资产唯一id
	ShardIds    string `json:"shardIds"`    // 资产碎片编号，使用英文分号;拼接
	Oid         int64  `json:"oid"`         // 订单id
	From        string `json:"from"`        // 起始地址
	To          string `json:"to"`          // 目的地址
	Metas       string `json:"metas"`       // 碎片Meta信息json串，使用英文分号;拼接，与shardIds对应
	CallbackUrl string `json:"callbackUrl"` // 回调通知地址
	Pkey        string `json:"pkey"`        // 资产拥有者公钥
	Sign        string `json:"sign"`        // 资产拥有者私钥对msg的签名 msg=hash(assetId+oid)
}

type ShardWriteOffRequest struct {
	Address     string `json:"address" valid:"Required"`  // 用户（碎片所有者）地址
	AssetId     int64  `json:"assetId" valid:"Required"`  // 资产唯一id
	ShardIds    string `json:"shardIds" valid:"Required"` // 资产碎片编号，使用英文分号;拼接
	Nonce       int64  `json:"nonce" valid:"Required"`    // 随机数
	CallbackUrl string `json:"callbackUrl"`               // 回调通知地址
	Pkey        string `json:"pkey" valid:"Required"`     // 资产拥有者公钥
	Sign        string `json:"sign" valid:"Required"`     // 资产拥有者私钥对msg的签名 msg=hash(assetId+nonce)
}

type ShardsQueryRequest struct {
	Address string `json:"address" valid:"Required"` // 拥有者账户地址
	Status  string `json:"status"`                   // 资产状态，默认all，可选 0:正常(包括发行上链中和已发行) 1:转售中 2:已核销（包含核销上链中和已核销）
	Cursor  string `json:"cursor"`                   // 分页游标
	Limit   int    `json:"limit"`                    // 分页数量,默认20, 最多50
	Nonce   int64  `json:"nonce"`                    // 带有时间戳的随机数
	Pkey    string `json:"pkey"`                     // 买家公钥
	Sign    string `json:"sign"`                     // 买家对nftid的签名
}

type ListShardsByAddrReq struct {
	Addr  string `json:"addr"`
	Page  int    `json:"page"`
	Limit int    `json:"limit"`
}

type QueryShardsReq struct {
	AssetId int64 `json:"asset_id"`
	ShardId int64 `json:"shard_id"`
}

type QueryShardsByOidReq struct {
	Oid int64 `json:"oid"`
}

type ShardsQueryResp struct {
	BaseResp
	HasMore int                       `json:"has_more"`
	Cursor  string                    `json:"cursor"`
	List    map[int64][]*NFTShardInfo `json:"list"`
}

type NFTShardInfo struct {
	Id        int64  `json:"id"`
	AppId     int64  `json:"app_id"`
	AssetId   int64  `json:"asset_id"`   // nft id
	ShardId   int64  `json:"shard_id"`   // nft碎片id
	Meta      string `json:"meta"`       // 资产元数据，json串
	JobId     int64  `json:"job_id"`     // 任务表id
	Oid       int64  `json:"oid"`        // 订单号
	OwnerAddr string `json:"owner_addr"` // 拥有账户地址
	Status    int8   `json:"status"`     // 资产状态.  上链状态. 0:已上链 1:发行中 2:核销中 3:已核销 4:转移中 10:异常
	TxId      string `json:"tx_id"`      // 链上交易hash
	ExtInfo   string `json:"ext_info"`
	Ctime     int64  `json:"ctime"`
	Mtime     int64  `json:"mtime"`
}

type QueryAssetInfoListRequest struct {
	AssetId int64 `json:"asset_id"`
}

type QueryAssetResp struct {
	BaseResp
	Meta *NFTAssetInfo   `json:"meta"`
	List []*NFTShardInfo `json:"list"`
}

type QueryTxBlockRequest struct {
	TxId string `json:"tx_id"`
}

type QueryTxBlockResp struct {
	BaseResp
	Tx    interface{} `json:"tx"`
	Block interface{} `json:"block"`
}

type QueryFullAssetListRequest struct {
	Addr string `json:"addr"`
}

type QueryFullAssetListResp struct {
	BaseResp
	List []*SimpleAsset `json:"list"`
}

type SimpleAsset struct {
	AssetId    int64  `json:"asset_id"`
	ShardId    int64  `json:"shard_id"`
	CreateAddr string `json:"create_addr"`
}

type ListShardsByAddrResp struct {
	BaseResp
	TotalCnt int             `json:"total_cnt"`
	List     []*NFTShardInfo `json:"list"`
}

type QueryShardResp struct {
	BaseResp
	Meta *NFTShardInfo `json:"meta"`
}
