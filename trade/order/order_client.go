package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type OrderClient struct {
	addr *addr.Addr
}

func NewOrderClient(addr *addr.Addr) *OrderClient {
	return &OrderClient{addr}
}

func (t *OrderClient) QueryOrderInfo(param *QueryOrderInfoParam) (*QueryOrderInfoResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.<PERSON>("order_user_type", fmt.Sprintf("%d", param.OrderType))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	v.Set("addr", param.Addr)
	body := v.Encode()
	reqRes, err := t.doRequest(OrderApiQueryOrderInfo, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset order for query order info fail.err:%v", err)
	}
	var result QueryOrderInfoResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal order response for query order info fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *OrderClient) QueryOrderInfoSign(param *QueryOrderInfoParam) (*QueryOrderInfoSignResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	v.Set("addr", param.Addr)
	body := v.Encode()
	reqRes, err := t.doRequest(OrderApiQueryOrderInfoSign, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset order for query order info sign fail.err:%v", err)
	}
	var result QueryOrderInfoSignResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal order response for query order info sign fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *OrderClient) QueryOrderList(param *QueryOrderListParam) (*QueryOrderListResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("order_user_type", fmt.Sprintf("%d", param.OrderType))
	v.Set("display_status", fmt.Sprintf("%d", param.DisplayStatus))
	v.Set("cursor", param.Cursor)
	v.Set("addr", param.Addr)
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(OrderApiQueryOrderList, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset order for query order list fail.err:%v", err)
	}
	var result QueryOrderListResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal order response for query order list fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *OrderClient) QueryOrderListByStatus(param *QueryOrderListByStatusParam) (*QueryOrderListByStatusResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("display_status", fmt.Sprintf("%d", param.DisplayStatus))
	v.Set("cursor", param.Cursor)
	v.Set("mtime_start", fmt.Sprintf("%d", param.MtimeStart))
	v.Set("mtime_end", fmt.Sprintf("%d", param.MtimeEnd))
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(OrderApiQueryOrderListByStatus, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset order for query order list by status fail.err:%v", err)
	}
	var result QueryOrderListByStatusResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal order response for query order list by status fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *OrderClient) CreateOrder(param *CreateOrderParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("sale_id", fmt.Sprintf("%d", param.SaleId))
	v.Set("sale_num", fmt.Sprintf("%d", param.SaleNum))
	v.Set("buyer_addr", param.BuyerAddr)
	v.Set("seller_addr", param.SellerAddr)
	v.Set("plat_addr", param.PlatAddr)
	v.Set("fee_addr", param.FeeAddr)
	v.Set("create_addr", param.CreateAddr)
	v.Set("buy_cnt", fmt.Sprintf("%d", param.BuyCnt))
	v.Set("asset_price", fmt.Sprintf("%d", param.AssetPrice))
	v.Set("pay_amount", fmt.Sprintf("%d", param.PayAmount))
	v.Set("seller_amount", fmt.Sprintf("%d", param.SellerAmount))
	v.Set("plat_amount", fmt.Sprintf("%d", param.PlatAmount))
	v.Set("fee_amount", fmt.Sprintf("%d", param.FeeAmount))
	v.Set("pay_type", fmt.Sprintf("%d", param.PayType))
	v.Set("view_type", fmt.Sprintf("%d", param.ViewType))
	v.Set("asset_cate", fmt.Sprintf("%d", param.AssetCate))
	v.Set("title", param.Title)
	v.Set("thumb", param.Thumb)
	v.Set("short_desc", param.ShortDesc)
	v.Set("ext_info", param.ExtInfo)
	v.Set("seller_sign", param.SellerSign)
	v.Set("seller_pkey", param.SellerPkey)
	v.Set("pay_related_info", param.PayRelatedInfo)
	v.Set("profit_list", param.ProfitList)
	v.Set("sale_from", fmt.Sprintf("%d", param.SaleFrom))
	body := v.Encode()
	reqRes, err := t.doRequest(OrderApiCreateOrder, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset order for create order fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal order response for create order fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *OrderClient) CancelOrder(param *CancelOrderParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	v.Set("cancel_type", fmt.Sprintf("%d", param.CancelType))
	v.Set("buyer_addr", param.BuyerAddr)
	v.Set("plat_addr", param.PlatAddr)
	body := v.Encode()
	reqRes, err := t.doRequest(OrderApiCancelOrder, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset order for cancel order fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal order response for cancel order fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *OrderClient) DeleteOrder(param *DeleteOrderParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	v.Set("buyer_addr", param.BuyerAddr)
	body := v.Encode()
	reqRes, err := t.doRequest(OrderApiDeleteOrder, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset order for delete order fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal order response for delete order fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *OrderClient) CountOrder(param *CountOrderParam) (*CountOrderResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("order_user_type", fmt.Sprintf("%d", param.OrderType))
	v.Set("display_status", fmt.Sprintf("%d", param.DisplayStatus))
	v.Set("addr", param.Addr)
	body := v.Encode()
	reqRes, err := t.doRequest(OrderApiCountOrder, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset order for count order fail.err:%v", err)
	}
	var result CountOrderResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal order response for count order fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *OrderClient) GetPayInfo(param *GetPayInfoParam) (*GetPayInfoResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("pay_type", fmt.Sprintf("%d", param.PayType))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	v.Set("buyer_addr", param.BuyerAddr)
	body := v.Encode()
	reqRes, err := t.doRequest(OrderApiGetPayInfo, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset order for get pay info fail.err:%v", err)
	}
	var result GetPayInfoResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal order response for get pay info fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *OrderClient) NotifyPaySucc(param *NotifyPaySuccParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	v.Set("buyer_addr", param.BuyerAddr)
	v.Set("plat_addr", param.PlatAddr)
	v.Set("pay_num", param.PayNum)
	v.Set("shard_id", fmt.Sprintf("%d", param.ShardId))
	body := v.Encode()
	reqRes, err := t.doRequest(OrderApiPayNotify, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset order for notify pay succ fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal order response for noticy pay succ fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *OrderClient) NotifyNFTIssue(param *NotifyNftIssueParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	v.Set("buyer_addr", param.BuyerAddr)
	v.Set("plat_addr", param.PlatAddr)
	body := v.Encode()
	reqRes, err := t.doRequest(OrderApiPayNotifyNft, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset order for notify nft issue fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal order response for noticy nft issue fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *OrderClient) PaySendGoods(param *PaySendGoodsParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	v.Set("buyer_addr", param.BuyerAddr)
	v.Set("seller_addr", param.SellerAddr)
	v.Set("txid", param.Txid)
	body := v.Encode()
	reqRes, err := t.doRequest(OrderApiPaySendGoods, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset order for pay send goods fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal order response for pay send goods fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// post
func (t *OrderClient) doRequest(api string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request order fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request order http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *OrderClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
