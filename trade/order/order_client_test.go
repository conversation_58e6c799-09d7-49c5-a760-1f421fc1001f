package order

import (
	"encoding/json"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
)

var hosts string = "*************:8350"
var appId int64 = 1325413256743134
var oid int64 = 24
var buyerAddr string = "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY"

func TestCreateOrder(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	profitShare := []*ProfitShareInfo{
		&ProfitShareInfo{
			ProfitShareAddr: "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydN",
			ReciverType:     "mchid",
			ProfitId:        "wx123457",
			ProfitPercent:   50,
			ProfitAmount:    50,
		},
		&ProfitShareInfo{
			ProfitShareAddr: "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydX",
			ReciverType:     "mchid",
			ProfitId:        "wx1234568",
			ProfitPercent:   20,
			ProfitAmount:    20,
		},
	}
	_, _ = json.Marshal(profitShare)

	param := &CreateOrderParam{
		AppId:        300100,
		Oid:          143672772084929604,
		AssetId:      1,
		SaleId:       111,
		SaleNum:      123,
		BuyerAddr:    buyerAddr,
		SellerAddr:   "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydM",
		PlatAddr:     "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydP",
		FeeAddr:      "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydP",
		CreateAddr:   "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydY",
		BuyCnt:       10,
		AssetPrice:   200,
		PayAmount:    100,
		SellerAmount: 50,
		AssetCate:    1,
		PayType:      9,
		Title:        "昆明七彩云南温德姆至尊豪廷大酒店",
		Thumb:        "{\"bucket\":\"xasset\", \"object\":\"sample.jpg\"}",
		ShortDesc:    "昆明七彩云南温德姆至尊豪廷大酒店位于滇池路。",
		BuyerSign:    "3045022049bf3aed2d15f1034dddb56634a4739d13fb1b6ea3c48fd82e16f8c5274942490221008a2cd0a300f60f20324cd34675ca01d110a9b2f074f19bf369d40540789c7cc0",
		BuyerPkey:    "{\"Curvname\":\"P-256\",\"X\":36505150171354363400464126431978257855318414556425194490762274938603757905292,\"Y\":79656876957602994269528255245092635964473154458596947290316223079846501380076}",
		ExtInfo:      "",
		ProfitList:   "[]",
	}
	resp, reqInfo, err := NewOrderClient(a).CreateOrder(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestCancelOrder(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	param := &CancelOrderParam{
		AppId:      appId,
		Oid:        oid,
		CancelType: 0,
		BuyerAddr:  buyerAddr,
		PlatAddr:   "TeyyPLpp9L7QAcxHangtcHTu7HUZ6iydP",
	}
	resp, reqInfo, err := NewOrderClient(a).CancelOrder(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestDeleteOrder(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	param := &DeleteOrderParam{
		AppId:     appId,
		Oid:       oid,
		BuyerAddr: buyerAddr,
	}
	resp, reqInfo, err := NewOrderClient(a).DeleteOrder(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestCountOrder(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	param := &CountOrderParam{
		AppId:     appId,
		OrderType: 0,
		Addr:      buyerAddr,
	}
	resp, reqInfo, err := NewOrderClient(a).CountOrder(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestGetPayInfo(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	param := &GetPayInfoParam{
		AppId:     300100,
		Oid:       143672772084929604,
		PayType:   9,
		BuyerAddr: buyerAddr,
	}
	resp, reqInfo, err := NewOrderClient(a).GetPayInfo(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestNotifyPaySucc(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	param := &NotifyPaySuccParam{
		AppId:     300100,
		Oid:       143672772084929604,
		BuyerAddr: buyerAddr,
		PlatAddr:  "dsfdsfsesnkfdse",
	}
	resp, reqInfo, err := NewOrderClient(a).NotifyPaySucc(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestNotifyNftIssue(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	param := &NotifyNftIssueParam{
		AppId:     appId,
		Oid:       oid + 1,
		BuyerAddr: buyerAddr,
		PlatAddr:  "dsfdsfsesnkfdse",
	}
	resp, reqInfo, err := NewOrderClient(a).NotifyNFTIssue(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestPaySendGoods(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	param := &PaySendGoodsParam{
		AppId:      appId,
		Oid:        oid + 1,
		BuyerAddr:  buyerAddr,
		SellerAddr: "234r3rfesdsvwerfew",
		Txid:       "wx123131",
	}
	resp, reqInfo, err := NewOrderClient(a).PaySendGoods(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestQueryOrderInfo(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	param := &QueryOrderInfoParam{
		AppId:     appId,
		Oid:       oid,
		OrderType: 0,
		Addr:      buyerAddr,
	}
	resp, reqInfo, err := NewOrderClient(a).QueryOrderInfo(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestQueryOrderInfoSign(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	param := &QueryOrderInfoParam{
		AppId: appId,
		Oid:   oid,
		Addr:  buyerAddr,
	}
	resp, reqInfo, err := NewOrderClient(a).QueryOrderInfoSign(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestQueryOrderList(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	param := &QueryOrderListParam{
		AppId:     appId,
		OrderType: 0,
		Addr:      buyerAddr,
	}
	resp, reqInfo, err := NewOrderClient(a).QueryOrderList(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestQueryOrderListByStatus(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	param := &QueryOrderListByStatusParam{
		AppId:      appId,
		MtimeStart: 0,
		MtimeEnd:   100000000,
	}
	resp, reqInfo, err := NewOrderClient(a).QueryOrderListByStatus(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}
