package order

import (
	"net/http"
)

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000

	MaxLimit = 50
)

const (
	OrderApiCreateOrder            = "/internal/order/v1/create"
	OrderApiCancelOrder            = "/internal/order/v1/cancel"
	OrderApiDeleteOrder            = "/internal/order/v1/delete"
	OrderApiGetPayInfo             = "/internal/order/v1/buy"
	OrderApiQueryOrderInfo         = "/internal/order/v1/query"
	OrderApiQueryOrderInfoSign     = "/internal/order/v1/querysign"
	OrderApiQueryOrderList         = "/internal/order/v1/list"
	OrderApiQueryOrderListByStatus = "/internal/order/v1/listbystatus"
	OrderApiCountOrder             = "/internal/order/v1/count"
	OrderApiPayNotify              = "/internal/order/v1/pay/notify"
	OrderApiPayNotifyNft           = "/internal/order/v1/pay/nft"
	OrderApiPaySendGoods           = "/internal/order/v1/pay/sendgoods"
)

//------TradeOrderCancelType------
const (
	Canceled_By_Buyer   = iota // 买家取消订单
	Canceled_By_Timeout        // 超时自动关闭订单
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

type QueryOrderInfoResp struct {
	BaseResp
	OrderInfo *OrderInfo `json:"info"`
}

type QueryOrderInfoSignResp struct {
	BaseResp
	OrderInfoSign *OrderInfoSign `json:"info"`
}

type QueryOrderListResp struct {
	BaseResp
	OrderList []*OrderInfo `json:"list"`
	HasMore   int          `json:"has_more"`
	Cursor    string       `json:"cursor"`
}

type QueryOrderListByStatusResp struct {
	BaseResp
	OrderListByStatus []*TradeOrderTabNode `json:"list"`
	HasMore           int                  `json:"has_more"`
	Cursor            string               `json:"cursor"`
}

type CountOrderResp struct {
	BaseResp
	Cnt int64 `json:"cnt"`
}

type GetPayInfoResp struct {
	BaseResp
	PayInfo interface{} `json:"pay_info"`
}

type TradeOrderTabNode struct {
	Id             int64              `json:"id"`
	AppId          int64              `json:"app_id"`
	Oid            int64              `json:"oid"`
	AssetId        int64              `json:"asset_id"`
	SaleId         int64              `json:"sale_id"`
	SaleNum        int64              `json:"sale_num"`
	BuyerAddr      string             `json:"buyer_addr"`
	SellerAddr     string             `json:"seller_addr"`
	PlatAddr       string             `json:"plat_addr"`
	FeeAddr        string             `json:"fee_addr"`
	CreateAddr     string             `json:"create_addr"`
	BuyCnt         int                `json:"buy_cnt"`
	AssetPrice     int                `json:"asset_price"`
	PayAmount      int                `json:"pay_amount"`
	SellerAmount   int                `json:"seller_amount"`
	FeeAmount      int                `json:"fee_amount"`
	PlatAmount     int                `json:"play_amount"`
	OrderStatus    int                `json:"order_status"`
	PayType        int                `json:"pay_type"`
	PayNum         string             `json:"pay_num"`
	ViewType       int                `json:"view_type"`
	AssetCate      int                `json:"asset_cate"`
	Title          string             `json:"title"`
	Thumb          string             `json:"thumb"`
	ShortDesc      string             `json:"short_desc"`
	SellerSign     string             `json:"seller_sign"`
	SellerPkey     string             `json:"seller_pkey"`
	ExtInfo        string             `json:"ext_info"`
	ProfitList     []*ProfitShareInfo `json:"profit_list"`
	PayRelatedInfo interface{}        `json:"pay_related_info"`
	SaleFrom       int                `json:"sale_from"`
	TradeTxid      string             `json:"trade_txid"`
	Ctime          int64              `json:"ctime"`
	Mtime          int64              `json:"mtime"`

	// 分账信息
	ProfitShareId        int64  `json:"profitshare_id"`
	ProfitShareNum       string `json:"profitshare_num"`
	ProfitShareReturnId  int64  `json:"profitshare_return_id"`
	ProfitShareReturnNum string `json:"profitshare_return_num"`
}

type ProfitShareInfo struct {
	ProfitShareAddr string `json:"profit_share_addr"`
	ReciverType     string `json:"reciver_type"`
	ProfitId        string `json:"profit_id"`
	ProfitPercent   int    `json:"profit_percent"`
	ProfitAmount    int    `json:"profit_amount"`
	ExtInfo         string `json:"profit_ext_info"`
}

type OrderInfo struct {
	Oid         int64  `json:"oid"`
	AssetId     int64  `json:"asset_id"`
	SaleId      int64  `json:"sale_id"`
	SaleNum     int64  `json:"sale_num"`
	BuyCnt      int    `json:"buy_cnt"`
	AssetPrice  int    `json:"asset_price"`
	PayAmount   int    `json:"pay_amount"`
	OrderStatus int    `json:"order_status"`
	AssetCate   int    `json:"asset_cate"`
	Title       string `json:"title"`
	Thumb       string `json:"thumb"`
	ShortDesc   string `json:"short_desc"`
	ExtInfo     string `json:"ext_info"`
	Ctime       int64  `json:"ctime"`
	Mtime       int64  `json:"mtime"`
}

type OrderInfoSign struct {
	Oid        int64              `json:"oid"`
	AssetId    int64              `json:"asset_id"`
	SaleId     int64              `json:"sale_id"`
	SaleNum    int64              `json:"sale_num"`
	BuyCnt     int                `json:"buy_cnt"`
	AssetPrice int                `json:"asset_price"`
	PayAmount  int                `json:"pay_amount"`
	ProfitList []*ProfitShareInfo `json:"profit_list"`
	SellerSign string             `json:"seller_sign"`
	SellerPkey string             `json:"seller_pkey"`
}

type CreateOrderParam struct {
	AppId          int64  `json:"app_id"`
	Oid            int64  `json:"oid"`
	AssetId        int64  `json:"asset_id"`
	SaleId         int64  `json:"sale_id"`
	SaleNum        int64  `json:"sale_num"`
	BuyerAddr      string `json:"buyer_addr"`
	SellerAddr     string `json:"seller_addr"`
	PlatAddr       string `json:"plat_addr"`
	FeeAddr        string `json:"fee_addr"`
	CreateAddr     string `json:"create_addr"`
	BuyCnt         int    `json:"buy_cnt"`
	AssetPrice     int    `json:"asset_price"`
	PayAmount      int    `json:"pay_amount"`
	SellerAmount   int    `json:"seller_amount"`
	FeeAmount      int    `json:"fee_amount"`
	PlatAmount     int    `json:"plat_amount"`
	PayType        int    `json:"pay_type"`
	ViewType       int    `json:"view_type"`
	AssetCate      int    `json:"asset_cate"`
	Title          string `json:"title"`
	Thumb          string `json:"thumb"`
	ShortDesc      string `json:"short_desc"`
	SellerSign     string `json:"seller_sign"`
	SellerPkey     string `json:"seller_pkey"`
	ExtInfo        string `json:"ext_info"`
	PayRelatedInfo string `json:"pay_related_info"`
	ProfitList     string `json:"profit_info"`
	SaleFrom       int    `json:"sale_from"`
}

func (t *CreateOrderParam) IsValid() bool {
	if t.AppId < 1 || t.Oid < 1 || t.AssetId < 1 || t.SaleId < 1 || t.BuyerAddr == "" || t.SellerAddr == "" ||
		t.PlatAddr == "" || t.FeeAddr == "" || t.BuyCnt < 1 || t.AssetPrice < 0 || t.PayAmount < 0 || t.SellerAmount < 0 ||
		t.PlatAmount < 0 || t.FeeAmount < 0 || t.PayType < 1 || t.ViewType < 0 || t.AssetCate < 1 ||
		t.Title == "" || t.Thumb == "" || t.ShortDesc == "" || t.SellerSign == "" || t.SellerPkey == "" {
		return false
	}
	return true
}

type CancelOrderParam struct {
	AppId      int64  `json:"app_id"`
	Oid        int64  `json:"oid"`
	CancelType int    `json:"cancel_type"`
	BuyerAddr  string `json:"buyer_addr"`
	PlatAddr   string `json:"plat_addr"`
}

func (t *CancelOrderParam) IsValid() bool {
	if t.AppId < 1 || t.Oid < 1 || t.BuyerAddr == "" || (t.CancelType == Canceled_By_Timeout && t.PlatAddr == "") {
		return false
	}
	return true
}

type DeleteOrderParam struct {
	AppId     int64  `json:"app_id"`
	Oid       int64  `json:"oid"`
	BuyerAddr string `json:"buyer_addr"`
}

func (t *DeleteOrderParam) IsValid() bool {
	if t.AppId < 1 || t.Oid < 1 || t.BuyerAddr == "" {
		return false
	}
	return true
}

type GetPayInfoParam struct {
	AppId     int64  `json:"app_id"`
	Oid       int64  `json:"oid"`
	PayType   int    `json:"pay_type"`
	BuyerAddr string `json:"buyer_addr"`
}

func (t *GetPayInfoParam) IsValid() bool {
	if t.AppId < 1 || t.Oid < 1 || t.PayType < 1 || t.BuyerAddr == "" {
		return false
	}
	return true
}

type QueryOrderInfoParam struct {
	AppId     int64  `json:"app_id"`
	OrderType int    `json:"order_user_type"`
	Oid       int64  `json:"oid"`
	Addr      string `json:"addr"`
}

func (t *QueryOrderInfoParam) IsValid() bool {
	if t.AppId < 1 || t.Oid < 1 || t.Addr == "" || t.OrderType < 0 {
		return false
	}
	return true
}

type QueryOrderListParam struct {
	AppId         int64  `json:"app_id"`
	OrderType     int    `json:"order_user_type"`
	DisplayStatus int    `json:"display_status"`
	Addr          string `json:"addr"`
	Cursor        string `json:"cursor"`
	Limit         int    `json:"limit"`
}

func (t *QueryOrderListParam) IsValid() bool {
	if t.AppId < 1 || t.OrderType < 0 || t.Addr == "" || t.Limit < 0 || t.Limit > MaxLimit {
		return false
	}
	return true
}

type QueryOrderListByStatusParam struct {
	AppId         int64  `json:"app_id"`
	DisplayStatus int    `json:"display_status"`
	Cursor        string `json:"cursor"`
	Limit         int    `json:"limit"`
	MtimeStart    int64  `json:"mtime_start"`
	MtimeEnd      int64  `json:"mtime_end"`
}

func (t *QueryOrderListByStatusParam) IsValid() bool {
	if t.AppId < 1 || t.Limit < 0 || t.Limit > MaxLimit || t.MtimeStart < 0 || t.MtimeEnd < 0 || t.MtimeEnd < t.MtimeStart {
		return false
	}
	return true
}

type NotifyPaySuccParam struct {
	AppId     int64  `json:"app_id"`
	Oid       int64  `json:"oid"`
	BuyerAddr string `json:"buyer_addr"`
	PlatAddr  string `json:"plat_addr"`
	PayNum    string `json:"pay_num"`
	ShardId   int64  `json:"shard_id"`
}

func (t *NotifyPaySuccParam) IsValid() bool {
	if t.AppId < 1 || t.Oid < 1 || t.BuyerAddr == "" || t.PlatAddr == "" || t.ShardId < 1 {
		return false
	}
	return true
}

type NotifyNftIssueParam struct {
	AppId     int64  `json:"app_id"`
	Oid       int64  `json:"oid"`
	BuyerAddr string `json:"buyer_addr"`
	PlatAddr  string `json:"plat_addr"`
}

func (t *NotifyNftIssueParam) IsValid() bool {
	if t.AppId < 1 || t.Oid < 1 || t.BuyerAddr == "" || t.PlatAddr == "" {
		return false
	}
	return true
}

type PaySendGoodsParam struct {
	AppId      int64  `json:"app_id"`
	Oid        int64  `json:"oid"`
	BuyerAddr  string `json:"buyer_addr"`
	SellerAddr string `json:"seller_addr"`
	Txid       string `json:"txid"`
}

func (t *PaySendGoodsParam) IsValid() bool {
	if t.AppId < 1 || t.Oid < 1 || t.BuyerAddr == "" || t.SellerAddr == "" {
		return false
	}
	return true
}

type CountOrderParam struct {
	AppId         int64  `json:"app_id"`
	OrderType     int    `json:"order_user_type"`
	DisplayStatus int    `json:"display_status"`
	Addr          string `json:"addr"`
}

func (t *CountOrderParam) IsValid() bool {
	if t.AppId < 1 || t.OrderType < 0 || t.Addr == "" {
		return false
	}
	return true
}
