package points

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000
)

type PointsFunc func(hc *PointsClient) (interface{}, *RequestRes, error)

func RetryPoints(getAddr func() (*addr.Addr, error), f PointsFunc, tryTime int) (resp interface{}, reqResp *RequestRes, err error) {
	if f == nil {
		return nil, nil, fmt.Errorf("need trade function")
	}

	var pointsAddr *addr.Addr
	for i := 0; i < tryTime; i++ {
		pointsAddr, err = getAddr()
		if err != nil {
			continue
		}
		client := NewPointsClient(pointsAddr)
		resp, reqResp, err = f(client)
		if err == nil {
			break
		}
	}
	if err != nil {
		return nil, nil, err
	}
	return resp, reqResp, nil
}

type PointsClient struct {
	addr *addr.Addr
}

func NewPointsClient(addr *addr.Addr) *PointsClient {
	return &PointsClient{addr}
}

func (t *PointsClient) IncrPoints(param *OpPointsParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("op_id", fmt.Sprintf("%d", param.OpId))
	v.Set("points", fmt.Sprintf("%d", param.Points))

	v.Set("address", param.Address)
	v.Set("describe", param.Describe)
	v.Set("ext_info", param.ExtInfo)
	body := v.Encode()
	reqRes, err := t.doRequest(PointsApiIncrPoints, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request points to incr points fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal points response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *PointsClient) ReducePoints(param *OpPointsParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("op_id", fmt.Sprintf("%d", param.OpId))
	v.Set("points", fmt.Sprintf("%d", param.Points))
	v.Set("op_cate", fmt.Sprintf("%d", param.OpCate))

	v.Set("address", param.Address)
	v.Set("describe", param.Describe)
	v.Set("ext_info", param.ExtInfo)
	body := v.Encode()
	reqRes, err := t.doRequest(PointsApiReducePoints, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request points to incr points fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal points response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *PointsClient) GetBalance(param *GetBalanceParam) (*GetBalanceResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))

	v.Set("address", param.Address)
	body := v.Encode()
	reqRes, err := t.doRequest(PointsApiGetBalance, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request points to get balance fail.err:%v", err)
	}
	var result GetBalanceResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal points response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *PointsClient) QryOperate(param *QryOperateParam) (*QryOperateResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("op_id", fmt.Sprintf("%d", param.OpId))

	body := v.Encode()
	reqRes, err := t.doRequest(PointsApiQryOperate, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request points to query operate fail.err:%v", err)
	}
	var result QryOperateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal points response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *PointsClient) ListOperate(param *ListOperateParam) (*ListOperateResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("address", param.Address)
	v.Set("cursor", param.Cursor)
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}

	body := v.Encode()
	reqRes, err := t.doRequest(PointsApiListOperate, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request points to list operate fail.err:%v", err)
	}
	var result ListOperateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal points response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// post
func (t *PointsClient) doRequest(api string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *PointsClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
