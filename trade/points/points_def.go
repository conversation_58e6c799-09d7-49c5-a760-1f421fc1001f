package points

import (
	"net/http"
)

const (
	PointsApiIncrPoints   = "/internal/point/v1/increase"
	PointsApiReducePoints = "/internal/point/v1/reduce"
	PointsApiGetBalance   = "/internal/point/v1/balance"
	PointsApiListOperate  = "/internal/point/v1/listoperate"
	PointsApiQryOperate   = "/internal/point/v1/qryoperate"
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

type OpPointsParam struct {
	AppId    int64  `json:"app_id"`
	Address  string `json:"address"`
	OpId     int64  `json:"op_id"`
	Points   int64  `json:"points"`
	OpCate   int    `json:"op_cate"`
	Describe string `json:"describe"`
	ExtInfo  string `json:"ext_info"`
}

func (p *OpPointsParam) Valid() bool {
	if p.Address == "" || p.AppId < 1 || p.Points < 1 || p.OpId < 1 {
		return false
	}
	return true
}

type GetBalanceParam struct {
	AppId   int64  `json:"app_id"`
	Address string `json:"address"`
}

func (p *GetBalanceParam) Valid() bool {
	return p.AppId > 0 && p.Address != ""
}

type GetBalanceResp struct {
	BaseResp
	Balance int64 `json:"balance"`
}

type QryOperateParam struct {
	AppId int64 `json:"app_id"`
	OpId  int64 `json:"op_id"`
}

func (p *QryOperateParam) Valid() bool {
	return p.AppId > 0 && p.OpId > 0
}

type QryOperateResp struct {
	BaseResp
	OperateNode
}

type ListOperateParam struct {
	AppId   int64  `json:"app_id"`
	Address string `json:"address"`
	Limit   int    `json:"limit"`
	Cursor  string `json:"cursor"`
}

func (p *ListOperateParam) Valid() bool {
	return p.AppId > 0 && p.Address != ""
}

type ListOperateResp struct {
	BaseResp
	List    []*OperateNode `json:"list"`
	HasMore int            `json:"has_more"`
	Cursor  string         `json:"cursor"`
}

type OperateNode struct {
	Address  string `json:"address"`
	OpType   int    `json:"op_type"`
	OpId     int64  `json:"op_id"`
	Points   int64  `json:"points"`
	Describe string `json:"describe"`
	ExtInfo  string `json:"ext_info"`
	Ctime    int64  `json:"ctime"`
}
