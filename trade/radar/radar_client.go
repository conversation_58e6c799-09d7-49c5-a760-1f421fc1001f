package radar

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type RadarClient struct {
	addr             *addr.Addr
	reqRWTimeoutMs   time.Duration
	reqConnTimeoutMs time.Duration
}

func NewRadarClient(addr *addr.Addr) *RadarClient {
	return &RadarClient{
		addr:             addr,
		reqRWTimeoutMs:   ReqRWTimeoutMs * time.Millisecond,
		reqConnTimeoutMs: ReqConnTimeoutMs * time.Millisecond,
	}
}

func (t *RadarClient) SetRWTimeout(timeout time.Duration) error {
	if timeout > 0 {
		t.reqRWTimeoutMs = timeout
	}
	return nil
}

func (t *RadarClient) SetConnTimeout(timeout time.Duration) error {
	if timeout > 0 {
		t.reqConnTimeoutMs = timeout
	}
	return nil
}

func (t *RadarClient) CreateTask(param *CreateTaskParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.IsValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("task_type", fmt.Sprintf("%d", param.TaskType))
	v.Set("task_id", fmt.Sprintf("%d", param.TaskId))
	v.Set("task_info", param.TaskInfo)
	v.Set("task_prog", param.TaskProg)
	v.Set("ext_info1", param.ExtInfo1)
	v.Set("callback_url", param.CallbackURL)
	body := v.Encode()
	reqRes, err := t.doRequest(RadarApiCreateTask, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset radar for create task fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal radar response for create task fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *RadarClient) DetailTask(taskId, appId int64) (*DetailTaskResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if taskId < 1 || appId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("task_id", fmt.Sprintf("%d", taskId))
	v.Set("app_id", fmt.Sprintf("%d", appId))
	body := v.Encode()
	reqRes, err := t.doRequest(RadarApiDetailTask, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset radar for detail task fail.err:%v", err)
	}
	var result DetailTaskResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal radar response for detail task fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *RadarClient) QueryContract(method, args string) (*QueryResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if method == "" || args == "" {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("method", method)
	v.Set("args", args)
	body := v.Encode()
	reqRes, err := t.doRequestForBrowser(RadarApiQueryContract, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset radar for query contract fail.err:%v", err)
	}
	var result QueryResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal radar response for query contract fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *RadarClient) QueryTx(txId string) (*QueryResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if txId == "" {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("tx_id", txId)
	body := v.Encode()
	reqRes, err := t.doRequest(RadarApiQueryTx, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset radar for query tx fail.err:%v", err)
	}
	var result QueryResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal radar response for query tx fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *RadarClient) QueryBlock(blockId string) (*QueryResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if blockId == "" {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("block_id", blockId)
	body := v.Encode()
	reqRes, err := t.doRequest(RadarApiQueryBlock, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset radar for query block fail.err:%v", err)
	}
	var result QueryResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal radar response for query block fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *RadarClient) QueryChainStatus() (*QueryResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	body := v.Encode()
	reqRes, err := t.doRequest(RadarApiQueryChainStatus, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset radar for query chain status fail.err:%v", err)
	}
	var result QueryResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal radar response for query chain status fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// post
func (t *RadarClient) doRequest(api string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	resp, err := httpclient.Post(reqUrl, header, t.reqConnTimeoutMs, t.reqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request product fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request product http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// post request for browser
// TODO: delete later
func (t *RadarClient) doRequestForBrowser(api string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	resp, err := httpclient.Post(reqUrl, header, 1000, 10000, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request product fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request product http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *RadarClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
