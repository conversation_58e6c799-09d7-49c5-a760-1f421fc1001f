package radar

import (
	"fmt"
	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"testing"
)

var hosts string = "*************:8370"
var appId int64 = 100839

func TestCreateTask(t *testing.T) {
	param := &CreateTaskParam{
		AppId:    appId,
		TaskType: 0,
		TaskId:   1234567,
		TaskInfo: "fdsfsfs",
	}
	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewRadarClient(a).CreateTask(param)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestDetailTask(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewRadarClient(a).DetailTask(1234567, 100839)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestQueryContract(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewRadarClient(a).QueryContract("getAssetList", "{\"address\":\"eSkqeb3kwaRk6r6DSca6cA3VwdzrbatWd\"}")
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestQueryTx(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewRadarClient(a).QueryTx("8775fd1da3824e261540a01b05d3728ba7b8ad365f9f83b7ffa9adb2765e70f6")
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestQueryBlock(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewRadarClient(a).QueryBlock("8775fd1da3824e261540a01b05d3728ba7b8ad365f9f83b7ffa9adb2765e70f6")
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}
