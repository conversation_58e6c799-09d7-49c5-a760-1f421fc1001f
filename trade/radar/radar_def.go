package radar

import (
	"net/http"
)

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000

	MaxLimit = 50
)

const (
	RadarApiCreateTask       = "/internal/radar/v1/asset/create"
	RadarApiDetailTask       = "/internal/radar/v1/asset/detail"
	RadarApiQueryContract    = "/internal/radar/v1/asset/querycontract"
	RadarApiQueryTx          = "/internal/radar/v1/asset/querytx"
	RadarApiQueryBlock       = "/internal/radar/v1/asset/queryblock"
	RadarApiQueryChainStatus = "/internal/radar/v1/asset/querychainstatus"
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

type CreateTaskParam struct {
	AppId       int64  `json:"app_id"`
	TaskType    int    `json:"task_type"`
	TaskId      int64  `json:"task_id"`
	TaskInfo    string `json:"task_info"`
	TaskProg    string `json:"task_prog"`
	ExtInfo1    string `json:"ext_info1"`
	CallbackURL string `json:"callback_url"`
}

func (t *CreateTaskParam) IsValid() bool {
	if t.AppId < 1 || t.TaskType < 0 || t.TaskId < 1 || t.TaskInfo == "" {
		return false
	}
	return true
}

type TaskInfo struct {
	Id          int64  `json:"id"`
	TaskId      int64  `json:"task_id"`
	TaskType    int    `json:"task_type"`
	AppId       int64  `json:"app_id"`
	Status      int    `json:"status"`
	ChainStatus int    `json:"chain_status"`
	TaskInfo    string `json:"task_info"`
	TaskProg    string `json:"task_prog"`
	TxId        string `json:"tx_id"`
	ExtInfo1    string `json:"ext_info1"`
	CallbackUrl string `json:"callback_url"`
	Ctime       int64  `json:"ctime"`
	Mtime       int64  `json:"mtime"`
}

type DetailTaskResp struct {
	BaseResp
	Task *TaskInfo `json:"task"`
}

type QueryResp struct {
	BaseResp
	Res interface{} `json:"res"`
}
