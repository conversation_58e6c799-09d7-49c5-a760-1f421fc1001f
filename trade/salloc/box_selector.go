package salloc

import (
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	rcli "icode.baidu.com/baidu/blockchain/xasset-golib/rediscli"
	"icode.baidu.com/baidu/blockchain/xasset-golib/utils"
)

const (
	// 计数器个数，采用多个不同计数器，让结果不可预测
	BlindBoxCounterNum = 3
)

const (
	// 毫秒
	BoxRedisTimeoutMs = 100
	// 序号记录key
	BlindBoxCounterKeyFmt = "%s:blindbox_select_counter:%d:%d"
	// redis前缀
	BoxRedisKeyPrefix = "xasset:salloc"
	// 计算概率时精度，考虑到小数点后4位
	ProbPrecision = 10000
)

type SelectTabNode struct {
	AssetId  int64
	Prob     int64
	HasStock bool
}

type SelectLog struct {
	StartNo   int64          `json:"start_no"`
	StartIdx  int64          `json:"start_idx"`
	AstStock  map[int64]bool `json:"asset_stock"`
	ProbValue map[int]int64  `json:"proc_value"`
}

type BoxSelector struct {
	// 盲盒资产本身的asset_id
	BoxAstId int64
	// 盲盒绑定的资产列表，k:asset_id v:绑定份数
	BlindAstList map[int64]int64

	// redis endpoint
	rdsAddr *addr.Addr

	// 根据算法生成的藏品选择顺序表
	// 算法说明:https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/jdlvt4koSq/-ATG2PVXbMe2-V
	selectSequeTab []*SelectTabNode
}

// @boxAstId 盲盒藏品本身的asset_id
// @bdAstList 盲盒绑定的真实藏品列表，k:asset_id v:绑定份数
func NewBoxSelector(rdsAddr *addr.Addr, boxAstId int64, bdAstList map[int64]int64) (*BoxSelector, error) {
	if rdsAddr == nil || boxAstId < 1 || len(bdAstList) < 1 {
		return nil, errors.New("param set error")
	}

	selectTab, err := GenBoxSelectTab(bdAstList)
	if err != nil {
		return nil, err
	}

	obj := &BoxSelector{
		BoxAstId:       boxAstId,
		BlindAstList:   bdAstList,
		rdsAddr:        rdsAddr,
		selectSequeTab: selectTab,
	}

	return obj, nil
}

// 注意:没选中时asset_id响应0，err响应nil
func (t *BoxSelector) SelectAst() (int64, *SelectLog, error) {
	// 1.随机获取桶标号
	bucketId := utils.GenNonce() % int64(BlindBoxCounterNum)

	// 2.获取指定桶累加序号
	key := fmt.Sprintf(BlindBoxCounterKeyFmt, BoxRedisKeyPrefix, t.BoxAstId, bucketId)
	// 尝试初始化桶中开始位置
	rInitPos := utils.GenNonce() % int64(len(t.selectSequeTab))
	_, err := t.setNxRetry(key, rInitPos)
	if err != nil {
		return 0, nil, err
	}
	// 计数+1
	incrNo, err := t.incrRetry(key, 1)
	if err != nil {
		return 0, nil, err
	}

	// 3.在标尺上顺时针选择满足条件的格子，并记录下选择记录
	var selectAstId, selectCnt int64
	sIdx := incrNo % int64(len(t.selectSequeTab))
	selectHis := &SelectLog{
		StartNo:   incrNo,
		StartIdx:  sIdx,
		AstStock:  make(map[int64]bool),
		ProbValue: make(map[int]int64),
	}
	for i := 0; i < len(t.selectSequeTab); i++ {
		// 记录跳过了多少个格子
		selectCnt++

		// 让能够循环选择
		idx := (int(sIdx) + i) % len(t.selectSequeTab)

		// 无库存继续选择
		if !t.selectSequeTab[idx].HasStock {
			selectHis.AstStock[t.selectSequeTab[idx].AssetId] = false
			continue
		}

		// 如果不是100%概率的，需要按概率做下随机选择
		if t.selectSequeTab[idx].Prob < int64(ProbPrecision) {
			rNo := utils.GenNonce() % int64(ProbPrecision)
			selectHis.ProbValue[idx] = rNo
			if rNo >= t.selectSequeTab[idx].Prob {
				continue
			}
		}

		// 选中了，结束循环
		selectAstId = t.selectSequeTab[idx].AssetId
		break
	}

	// 4.更新桶累加序号，响应选择结果
	if selectCnt > 1 {
		t.incrRetry(key, selectCnt-1)
	}

	return selectAstId, selectHis, nil
}

func (t *BoxSelector) SetNoStock(astId int64) {
	for k, v := range t.selectSequeTab {
		if v.AssetId == astId {
			t.selectSequeTab[k].HasStock = false
		}
	}
}

func (t *BoxSelector) setNx(key string, value interface{}) (string, error) {
	rHandle, err := rcli.NewRedis(t.rdsAddr, BoxRedisTimeoutMs*time.Millisecond)
	if err != nil {
		return "", err
	}
	defer rHandle.Close()

	return rHandle.SetNx(key, value)
}

func (t *BoxSelector) setNxRetry(key string, value interface{}) (string, error) {
	var res string
	var err error
	for i := 0; i < 3; i++ {
		res, err = t.setNx(key, value)
		if err == nil {
			break
		}
	}
	if err != nil {
		return "", err
	}

	return res, nil
}

func (t *BoxSelector) incrBy(key string, count int64) (int64, error) {
	rHandle, err := rcli.NewRedis(t.rdsAddr, BoxRedisTimeoutMs*time.Millisecond)
	if err != nil {
		return 0, err
	}
	defer rHandle.Close()

	num, err := rHandle.Incrby(key, count)
	if err != nil {
		return 0, err
	}

	// 防止过大溢出
	if num > 0xFFFFFFFFFFFF {
		rHandle.DelKeys(key)
	}

	return num, nil
}

func (t *BoxSelector) incrRetry(key string, count int64) (int64, error) {
	var num int64
	var err error
	for i := 0; i < 3; i++ {
		num, err = t.incrBy(key, count)
		if err == nil {
			break
		}
	}
	if err != nil {
		return 0, err
	}

	return num, nil
}

func GenBoxSelectTab(bdAstList map[int64]int64) ([]*SelectTabNode, error) {
	if len(bdAstList) < 1 {
		return nil, errors.New("param set error")
	}

	// 1.计算配比
	var sumAmount, minAstId, minAmount int64
	tmpAstList := make(map[int64]int64)
	for astId, amount := range bdAstList {
		tmpAstList[astId] = amount
		sumAmount += amount
		if minAmount < 1 || amount < minAmount {
			minAmount = amount
			minAstId = astId
		}
	}
	// 最小的只有1份，不需要参与计算
	delete(tmpAstList, minAstId)
	computeRes := make(map[int]int64)
	astRelate := make(map[int]int64)
	for i := 0; i < len(bdAstList)-1; i++ {
		maxAstId, maxAmount := getMaxAmount(tmpAstList)
		delete(tmpAstList, maxAstId)
		// 取小数点后n位取整
		computeRes[i] = int64(float64(maxAmount) / float64(minAmount) * float64(ProbPrecision))
		astRelate[i] = maxAstId
	}
	// 把最小的补到最后
	computeRes[len(computeRes)] = int64(ProbPrecision)
	astRelate[len(astRelate)] = minAstId

	// 2.生成选择参照表
	selectTab := computeSelectTab(computeRes, astRelate)
	return selectTab, nil
}

// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/jdlvt4koSq/-ATG2PVXbMe2-V
func computeSelectTab(computeRes, astRelate map[int]int64) []*SelectTabNode {
	var selectTab []*SelectTabNode
	for i := 0; i < len(computeRes); i++ {
		// 向下取整
		cnt := computeRes[i] / int64(ProbPrecision)
		// 回去小数点后面的部分
		mantissa := computeRes[i] % int64(ProbPrecision)
		// 记录总共站几个格子
		total := int(cnt)
		if mantissa > 0 {
			// 如果有小数点后面，就要多占一个格子
			total = total + 1
		}
		// 计算本批插入表中的间隔
		interval := int(len(selectTab)/total) + 1

		oldTabIdx := 0
		tmpTabIdx := 0
		insertCnt := 0
		tmpTab := make([]*SelectTabNode, len(selectTab)+total)
		for tmpTabIdx < len(selectTab)+total {
			// 先从旧表中复制interval-1个进去
			for j := 0; j < interval-1 && len(selectTab) > 0 && oldTabIdx < len(selectTab); j++ {
				tmpTab[tmpTabIdx] = selectTab[oldTabIdx]
				oldTabIdx = oldTabIdx + 1
				tmpTabIdx = tmpTabIdx + 1
			}

			// 如果需要，就补一个进取
			if insertCnt < total {
				// 第一个概率值设置为不能整除的部分
				prod := int64(ProbPrecision)
				if insertCnt == 0 && mantissa > 0 {
					prod = mantissa
				}
				// 补一个新增的进去
				tmpTab[tmpTabIdx] = &SelectTabNode{
					AssetId:  astRelate[i],
					Prob:     prod,
					HasStock: true,
				}
				tmpTabIdx = tmpTabIdx + 1
				insertCnt = insertCnt + 1
			}
		}

		// 替换成最新的
		selectTab = tmpTab
	}

	return selectTab
}

func getMaxAmount(bdAstList map[int64]int64) (int64, int64) {
	var tmpAstId, tmpAmount int64
	for astId, amount := range bdAstList {
		if amount > tmpAmount {
			tmpAmount = amount
			tmpAstId = astId
		}
	}

	return tmpAstId, tmpAmount
}
