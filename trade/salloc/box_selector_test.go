package salloc

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
)

var assetId int64 = 123
var bdAstList = map[int64]int64{
	1: 9300,
	2: 9300,
	3: 9300,
	4: 9300,
	5: 9300,
	6: 2000,
	7: 2000,
	8: 83,
}

func TestGenBoxSelectTab(t *testing.T) {
	tab, err := GenBoxSelectTab(bdAstList)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("%v", err)
		return
	}

	for k, v := range tab {
		fmt.Printf("%d %v\n", k, v)
	}
}

func TestSelectAst(t *testing.T) {
	stor := newSelector()
	ast, his, err := stor.SelectAst()
	if err != nil {
		t.<PERSON><PERSON>rf("%v", err)
		return
	}
	fmt.Println(ast)
	fmt.Println(his)
}

func newSelector() *BoxSelector {
	adr, _ := addr.NewAddrByHost([]string{"127.0.0.1:6389"})
	obj, _ := NewBoxSelector(adr, assetId, bdAstList)
	return obj
}
