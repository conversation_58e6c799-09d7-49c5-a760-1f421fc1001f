package salloc

import (
	"encoding/json"
	"fmt"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/rediscli"
)

const (
	AKAirdrop    = "001"
	AKPurchase   = "002"
	AKBlindBox   = "003"
	AKCompose    = "004"
	AKDecompose  = "005"
	AKCode       = "006"
	AKRecvAward  = "007"
	AKCompensate = "008"
)

const (
	RedisDialTimeoutMs = 1000
)

const (
	OrderGrantShardsCtrlKey  = "xassets:horae:op_record:%d:%s"
	OrderGrantShardsCountKey = "xassets:horae:shard_count:%d"
)

type ShardsAlloc struct {
	addr *addr.Addr
}

func NewShardsAlloc(typ, addr string) (*ShardsAlloc, error) {
	raddr, err := GetRedisAddr(typ, addr)
	if err != nil {
		return nil, err
	}
	return &ShardsAlloc{
		addr: raddr,
	}, nil
}

func GetRedisAddr(typ, raddr string) (*addr.Addr, error) {
	switch typ {
	case "HOST":
		return addr.NewAddrByHost([]string{raddr})
	case "BNS":
		return addr.NewAddrByBns(raddr)
	}

	return nil, fmt.Errorf("redis addr type set error.type:%s", typ)
}

func GetShardsCtrlKey(assetId int64, opId string) string {
	return fmt.Sprintf(OrderGrantShardsCtrlKey, assetId, opId)
}

func GeShardsCountKey(assetId int64) string {
	return fmt.Sprintf(OrderGrantShardsCountKey, assetId)
}

func (t *ShardsAlloc) GenShardId(ak string, opId string, assetId int64, batch int) ([]int64, error) {
	if batch < 1 || batch > 100 {
		return nil, fmt.Errorf("batch number set error")
	}

	client, err := newRedisClient(t.addr)
	if err != nil {
		return nil, err
	}
	defer client.Close()
	opId = fmt.Sprintf("%s:%s", ak, opId)
	sdsCtrlKey := GetShardsCtrlKey(assetId, opId)
	countKey := GeShardsCountKey(assetId)

	//查询是否已有shard_ids
	shardIds := make([]int64, 0)
	oldShards, err := client.GetStr(sdsCtrlKey)
	if err != nil {
		return nil, err
	}
	if oldShards != "" {
		err = json.Unmarshal([]byte(oldShards), &shardIds)
		if err != nil {
			return nil, fmt.Errorf("unmarshal old shard fail.err:%v", err)
		}
		return shardIds, nil
	}

	total, err := client.Incrby(countKey, int64(batch))
	if err != nil {
		return nil, fmt.Errorf("incr number fail.err:%v", err)
	}
	last := total - int64(batch)
	for i := last + 1; i <= total; i++ {
		shardIds = append(shardIds, i)
	}
	strShards, _ := json.Marshal(shardIds)
	resp, err := client.SetNx(sdsCtrlKey, string(strShards))
	if err != nil {
		return nil, fmt.Errorf("set shard_ids fail.err:%v", err)
	}
	//如果setnx时已存在，再查一次
	if resp != "OK" {
		oldShards, err = client.GetStr(sdsCtrlKey)
		if err != nil {
			return nil, err
		}
		err = json.Unmarshal([]byte(oldShards), &shardIds)
		if err != nil {
			return nil, fmt.Errorf("unmarshal old shard fail.err:%v", err)
		}
	}

	return shardIds, nil
}

func newRedisClient(raddr *addr.Addr) (*rediscli.RedisClient, error) {
	return rediscli.NewRedis(raddr, RedisDialTimeoutMs*time.Millisecond)
}
