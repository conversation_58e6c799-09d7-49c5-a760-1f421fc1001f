package salloc

import (
	"fmt"
	"testing"
)

func TestGenShardId(t *testing.T) {
	cli, err := NewShardsAlloc("HOST", "127.0.0.1:6389")
	if err != nil {
		fmt.Println("get addr fail.err:", err)
		return
	}
	shards, err := cli.<PERSON>S<PERSON>d(AKAirdrop, "123456", 111111, 10)
	if err != nil {
		fmt.Println("gen shards fail.err:", err)
		return
	}
	fmt.Println("grant first times shards:", shards)

	shards, err = cli.GenShardId(AKAirdrop, "123456", 111111, 1)
	if err != nil {
		fmt.Println("gen shards fail.err:", err)
		return
	}
	fmt.Println("grant second times shards:", shards)

	shards, err = cli.GenShardId(AKBlindBox, "67890", 111111, 5)
	if err != nil {
		fmt.Println("gen shards fail.err:", err)
		return
	}
	fmt.Println("blind box first times shards:", shards)
}
