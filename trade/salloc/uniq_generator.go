package salloc

import (
	"fmt"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	rcli "icode.baidu.com/baidu/blockchain/xasset-golib/rediscli"
	"icode.baidu.com/baidu/blockchain/xasset-golib/utils"
)

// 最大到255，顺序分配，需要确保唯一的使用同一个mod值
const (
	// 上链任务ID
	UniqGenModRadarTask = 1
	// 上链nonce值
	UniqGenModRadarNonce = 2
)

const (
	// 毫秒
	RedisTimeoutMs = 200
	// 序号记录key
	IncrIdKeyFmt = "%s:uniq_generator_number:%d"
	// redis前缀
	RedisKeyPrefix = "xasset:salloc"
)

type UniqGenerator struct {
	rdsAddr *addr.Addr
	mod     int64
}

func NewUniqGenerator(rdsAddr *addr.Addr, mod int) *UniqGenerator {
	return &UniqGenerator{
		rdsAddr: rdsAddr,
		mod:     int64(mod & 0xFF),
	}
}

func (t *UniqGenerator) GenUniqId() (int64, error) {
	nums, err := t.incrRetry(1)
	if err != nil {
		return 0, err
	}

	uniqId := t.computeId(nums[0])
	return uniqId, nil
}

func (t *UniqGenerator) BatchGenUniqId(count int) ([]int64, error) {
	if count < 1 || count > 1000 {
		return nil, fmt.Errorf("generate id number set error")
	}
	nums, err := t.incrRetry(count)
	if err != nil {
		return nil, err
	}

	uniqIds := make([]int64, count)
	for idx, sNum := range nums {
		uniqIds[idx] = t.computeId(sNum)
	}

	return uniqIds, nil
}

// 0-4位随机数(5位)，5-28位有序编号(24位)，29-60位unix时间戳(32位)，61-62位mod(2位)，63位0(1位)
func (t *UniqGenerator) computeId(sNum int64) int64 {
	sNum = sNum & 0xFFFFFF
	rNum := utils.GenNonce() & 0x1F
	timestamp := time.Now().Unix() & 0xFFFFFFFF

	uniqId := (t.mod << 61) + (timestamp << 29) + (sNum << 5) + rNum
	return uniqId & 0x7FFFFFFFFFFFFFFF
}

func (t *UniqGenerator) incrRetry(count int) ([]int64, error) {
	var num int64
	var err error
	for i := 0; i < 3; i++ {
		num, err = t.incrBy(count)
		if err == nil {
			break
		}
	}
	if err != nil {
		return nil, err
	}

	mums := make([]int64, 0)
	for i := count; i > 0; i-- {
		mums = append(mums, num-int64(i)+1)
	}

	return mums, nil
}

func (t *UniqGenerator) incrBy(count int) (int64, error) {
	rHandle, err := rcli.NewRedis(t.rdsAddr, RedisTimeoutMs*time.Millisecond)
	if err != nil {
		return 0, err
	}
	defer rHandle.Close()

	rKey := fmt.Sprintf(IncrIdKeyFmt, RedisKeyPrefix, t.mod)
	num, err := rHandle.Incrby(rKey, int64(count))
	if err != nil {
		return 0, err
	}

	// 有序编号最大分配24位(16777215)，预留一些防止清0失败
	if num > 16000000 {
		rHandle.DelKeys(rKey)
	}

	return num, nil
}
