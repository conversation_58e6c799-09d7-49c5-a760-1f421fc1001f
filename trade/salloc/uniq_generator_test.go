package salloc

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
)

func TestGenUniqId(t *testing.T) {
	num, err := newGenerator().GenUniqId()
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GenUniqueId failed.err:%v", err)
		return
	}

	fmt.Println(num)
}

func TestBatchGenUniqId(t *testing.T) {
	nums, err := newGenerator().BatchGenUniqId(1000)
	if err != nil {
		t.Errorf("BatchGenUniqId failed.err:%v", err)
		return
	}

	for _, num := range nums {
		fmt.Println(num)
		mod, timestamp, sNum, rNum := decodeId(num)
		fmt.Printf("%d %d %d %d\n", mod, timestamp, sNum, rNum)
	}
}

func decodeId(id int64) (int64, int64, int64, int64) {
	mod := (id >> 61) & 0xFF
	timestamp := (id >> 29) & 0xFFFFFFFF
	sNum := (id >> 5) & 0xFFFFFF
	rNum := id & 0x1F

	return mod, timestamp, sNum, rNum
}

func newGenerator() *UniqGenerator {
	adr, _ := addr.NewAddrByHost([]string{"127.0.0.1:6389"})
	return NewUniqGenerator(adr, UniqGenModRadarTask)
}
