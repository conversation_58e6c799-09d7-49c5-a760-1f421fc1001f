package store

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type StoreClient struct {
	addr *addr.Addr
}

func NewStoreClient(addr *addr.Addr) *StoreClient {
	return &StoreClient{addr}
}

func (t *StoreClient) CreateStore(param *CreateOrUpdateStoreParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.CreateValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("name", param.Name)
	v.Set("logo", param.Logo)
	v.Set("cover", param.Cover)
	v.Set("short_desc", param.ShortDesc)
	v.Set("weight", fmt.Sprintf("%d", param.Weight))
	v.Set("ext_info", param.ExtInfo)
	v.Set("wechat", param.Wechat)

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiCreateStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for create store fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) AlterStore(param *CreateOrUpdateStoreParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param.AppId < 1 || param.StoreId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("name", param.Name)
	v.Set("logo", param.Logo)
	v.Set("cover", param.Cover)
	v.Set("short_desc", param.ShortDesc)
	v.Set("weight", fmt.Sprintf("%d", param.Weight))
	v.Set("ext_info", param.ExtInfo)
	v.Set("wechat", param.Wechat)

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiAlterStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for alter store fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) PushStore(param *BaseStoreParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("op_type", fmt.Sprintf("%d", param.Optype))

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiPushStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for push store fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) QueryStore(param *BaseStoreParam) (*QueryStoreResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiQueryStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for query store fail.err:%v", err)
	}
	var result QueryStoreResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) ListStore(param *BaseStoreParam) (*ListStoreResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param.AppId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiListStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for list store fail.err:%v", err)
	}
	var result ListStoreResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) BanStore(param *BaseStoreParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("op_type", fmt.Sprintf("%d", param.Optype))

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiBanStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for ban store fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) CreateAct(param *CreateOrUpdateActParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.CreateValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("issuer", param.Issuer)
	v.Set("act_name", param.ActName)
	v.Set("short_desc", param.ShortDesc)
	v.Set("thumb", param.Thumb)
	v.Set("img_desc", param.ImgDesc)
	v.Set("bookable", fmt.Sprintf("%d", param.Bookable))
	v.Set("start", fmt.Sprintf("%d", param.Start))
	v.Set("end", fmt.Sprintf("%d", param.End))
	v.Set("weight", fmt.Sprintf("%d", param.Weight))
	v.Set("jump_link", param.JumpLink)
	v.Set("ext_info", param.ExtInfo)

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiCreateAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for create act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) AlterAct(param *CreateOrUpdateActParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param.AppId < 1 || param.ActId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("issuer", param.Issuer)
	v.Set("act_name", param.ActName)
	v.Set("short_desc", param.ShortDesc)
	v.Set("thumb", param.Thumb)
	v.Set("img_desc", param.ImgDesc)
	v.Set("bookable", fmt.Sprintf("%d", param.Bookable))
	v.Set("start", fmt.Sprintf("%d", param.Start))
	v.Set("end", fmt.Sprintf("%d", param.End))
	v.Set("weight", fmt.Sprintf("%d", param.Weight))
	v.Set("jump_link", param.JumpLink)
	v.Set("ext_info", param.ExtInfo)

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiAlterAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for alter act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) PubAct(param *BaseActParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("op_type", fmt.Sprintf("%d", param.OpType))

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiPubAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for pub act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) PushAct(param *BaseActParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("op_type", fmt.Sprintf("%d", param.OpType))

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiPushAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for push act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) QueryAct(param *BaseActParam) (*QueryActResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiQueryAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for query act fail.err:%v", err)
	}
	var result QueryActResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) ListAct(param *ListActParam) (*ListActResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	v.Set("cursor", param.Cursor)

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiListAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for list act fail.err:%v", err)
	}
	var result ListActResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) BanAct(param *BaseActParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("op_type", fmt.Sprintf("%d", param.OpType))

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiBanAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for ban act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) RemoveAct(param *BaseActParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiRemoveAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for remove act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) BindAst(param *BindOrAlterAstParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.BindValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("split_id", fmt.Sprintf("%d", param.SplitId))
	v.Set("asset_info", param.AssetInfo)
	v.Set("amount", fmt.Sprintf("%d", param.Amount))
	v.Set("apply_form", fmt.Sprintf("%d", param.ApplyForm))
	v.Set("asset_cate", fmt.Sprintf("%d", param.AssetCate))
	v.Set("price", fmt.Sprintf("%d", param.Price))
	v.Set("ori_price", fmt.Sprintf("%d", param.OriPrice))
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("start", fmt.Sprintf("%d", param.Start))
	v.Set("end", fmt.Sprintf("%d", param.End))
	v.Set("grant_mode", fmt.Sprintf("%d", param.GrantMode))
	v.Set("jump_link", param.JumpLink)
	v.Set("ext_info", param.ExtInfo)
	v.Set("is_box", fmt.Sprintf("%d", param.IsBox))

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiBindAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for bind ast fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) AlterAst(param *BindOrAlterAstParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.AlterValid() {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("split_id", fmt.Sprintf("%d", param.SplitId))
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("is_box", fmt.Sprintf("%d", param.IsBox))
	if param.AssetCate >= 0 {
		v.Set("asset_cate", fmt.Sprintf("%d", param.AssetCate))
	}
	if param.Start > 0 {
		v.Set("start", fmt.Sprintf("%d", param.Start))
	}
	if param.End > 0 {
		v.Set("end", fmt.Sprintf("%d", param.End))
	}
	if param.GrantMode >= 0 {
		v.Set("grant_mode", fmt.Sprintf("%d", param.GrantMode))
	}
	if param.JumpLink != "" {
		v.Set("jump_link", param.JumpLink)
	}
	if param.ExtInfo != "" {
		v.Set("ext_info", param.ExtInfo)
	}
	if param.Price >= 0 {
		v.Set("price", fmt.Sprintf("%d", param.Price))
	}
	if param.OriPrice >= 0 {
		v.Set("ori_price", fmt.Sprintf("%d", param.OriPrice))
	}
	if param.Amount >= 0 {
		v.Set("amount", fmt.Sprintf("%d", param.Amount))
	}
	if param.ApplyForm >= 0 {
		v.Set("apply_form", fmt.Sprintf("%d", param.ApplyForm))
	}
	v.Set("asset_info", param.AssetInfo)

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiAlterAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for bind ast fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) CancelAst(param *BaseAstParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiCancelAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for cancel ast fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) CancelAstByAct(param *BaseActParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("is_box", fmt.Sprintf("%d", param.IsBox))

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiCancelAstByAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for cancel ast by act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) QueryAst(param *BaseAstParam) (*QueryAstResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiQueryAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for query ast fail.err:%v", err)
	}
	var result QueryAstResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) ListAst(param *BaseActParam) (*ListAstResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiListAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for list act ast fail.err:%v", err)
	}
	var result ListAstResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) BanAst(param *BaseAstParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param.AssetId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiBanAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for ban ast fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) ListActByStore(param *ListActParam) (*ListActByStoreResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	v.Set("cursor", param.Cursor)

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiListActByStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for list act by store fail.err:%v", err)
	}
	var result ListActByStoreResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) ListAstByAct(param *BaseActParam) (*QueryActResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiListAstByAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for list act ast by act fail.err:%v", err)
	}
	var result QueryActResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *StoreClient) ListActByAst(assetId int64, mono ...int) (*ListActByAstResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if assetId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	if len(mono) > 0 {
		v.Set("mono", fmt.Sprintf("%d", mono[0]))
	}

	body := v.Encode()
	reqRes, err := t.doRequest(StoreApiListActByAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset store for list act by ast fail.err:%v", err)
	}
	var result ListActByAstResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// post
func (t *StoreClient) doRequest(api string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *StoreClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
