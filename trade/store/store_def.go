package store

import (
	"net/http"
)

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000

	MaxLimit = 50
)

const (
	StoreApiCreateStore    = "/internal/store/v1/create"
	StoreApiAlterStore     = "/internal/store/v1/alter"
	StoreApiQueryStore     = "/internal/store/v1/query"
	StoreApiBanStore       = "/internal/store/v1/ban"
	StoreApiListStore      = "/internal/store/v1/liststorebyapp"
	StoreApiPushStore      = "/internal/store/v1/pushstore"
	StoreApiCreateAct      = "/internal/store/v1/createact"
	StoreApiAlterAct       = "/internal/store/v1/alteract"
	StoreApiPubAct         = "/internal/store/v1/pubact"
	StoreApiQueryAct       = "/internal/store/v1/queryact"
	StoreApiListAct        = "/internal/store/v1/listact"
	StoreApiBanAct         = "/internal/store/v1/banact"
	StoreApiRemoveAct      = "/internal/store/v1/removeact"
	StoreApiPushAct        = "/internal/store/v1/pushact"
	StoreApiBindAst        = "/internal/store/v1/bindast"
	StoreApiAlterAst       = "/internal/store/v1/alterast"
	StoreApiCancelAst      = "/internal/store/v1/cancelast"
	StoreApiCancelAstByAct = "/internal/store/v1/cancelastbyact"
	StoreApiQueryAst       = "/internal/store/v1/queryast"
	StoreApiListAst        = "/internal/store/v1/listast"
	StoreApiBanAst         = "/internal/store/v1/banast"
	StoreApiListActByStore = "/internal/store/v1/listactbystore"
	StoreApiListAstByAct   = "/internal/store/v1/listastbyact"
	StoreApiListActByAst   = "/internal/store/v1/listactbyast"
)

const (
	AstStatusPreRelease = 0 // 预发布
	AstStatusInProgress = 1 // 进行中
	AstStatusOutOfStock = 2 // 已售罄
	AstStatusOutOfDate  = 3 // 已结束

	GrantModeUnordered = 0 // 碎片授予 无序
	GrantModeOrdered   = 1 // 碎片授予有序
	GrantModeInfinite  = 2 // 碎片授予不限量

	AssetApplyFormAirdrop  = 0 // 空投
	AssetApplyFormBuy      = 1 // 购买
	AssetApplyFormExchange = 2 // 兑换
	AssetApplyFormCompose  = 3 // 合成

	ActListTypeSquare   = 1 // 活动广场
	ActListTypeCalendar = 2 // 活动日历

	ActOpTypePublish = 0 // 发布活动
	ActOpTypeOffline = 1 // 下线活动
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

type CreateOrUpdateStoreParam struct {
	AppId     int64  `json:"app_id"`
	StoreId   int    `json:"store_id"`
	Name      string `json:"name"`
	Logo      string `json:"logo"`
	Cover     string `json:"cover"`
	ShortDesc string `json:"short_desc"`
	Weight    int    `json:"weight"`
	ExtInfo   string `json:"ext_info"`
	Wechat    string `json:"wechat"`
}

func (s *CreateOrUpdateStoreParam) CreateValid() bool {
	return s.AppId > 0 && s.StoreId > 0 && s.Name != "" && s.Logo != "" && s.Cover != ""
}

type BaseStoreParam struct {
	AppId   int64 `json:"app_id"`
	StoreId int   `json:"store_id"`
	Optype  int   `json:"op_type"`
}

func (s *BaseStoreParam) Valid() bool {
	return s.StoreId > 0
}

type QueryStoreResp struct {
	BaseResp
	Meta *StoreInfo `json:"meta"`
}

type ListStoreResp struct {
	BaseResp
	List []*StoreInfo `json:"list"`
}

type StoreInfo struct {
	AppId     int64  `json:"app_id"`
	StoreId   int    `json:"store_id"`
	Name      string `json:"name"`
	Logo      string `json:"logo"`
	Cover     string `json:"cover"`
	ShortDesc string `json:"short_desc"`
	Status    int    `json:"status"`
	Ctime     int64  `json:"ctime"`
	Mtime     int64  `json:"mtime"`
	Weight    int    `json:"weight"`
	ExtInfo   string `json:"ext_info"`
	Wechat    string `json:"wechat"`
	Likes     int64  `json:"likes"`
	StoreType int    `json:"store_type"`
}

type CreateOrUpdateActParam struct {
	AppId     int64  `json:"app_id"`
	ActId     int    `json:"act_id"`
	StoreId   int    `json:"store_id"`
	JumpLink  string `json:"jump_link"`
	ActName   string `json:"act_name"`
	ShortDesc string `json:"short_desc"`
	Issuer    string `json:"issuer"`
	ImgDesc   string `json:"img_desc"`
	Thumb     string `json:"thumb"`
	Bookable  int    `json:"bookable"`
	Start     int64  `json:"start"`
	End       int64  `json:"end"`
	Weight    int    `json:"weight"`
	ExtInfo   string `json:"ext_info"`
}

func (s *CreateOrUpdateActParam) CreateValid() bool {
	return s.AppId > 0 && s.StoreId > 0 && s.ActId > 0 && s.ActName != "" && s.Issuer != "" && s.Thumb != "" && s.Start > 0 && s.End > 0
}

type BaseActParam struct {
	AppId  int64 `json:"app_id"`
	ActId  int64 `json:"act_id"`
	OpType int   `json:"op_type"`
	IsBox  int   `json:"is_box"`
}

func (s *BaseActParam) Valid() bool {
	return s.ActId > 0
}

type QueryActResp struct {
	BaseResp
	Meta *ActInfo `json:"meta"`
}

type ListActParam struct {
	AppId   int64  `json:"app_id"`
	StoreId int    `json:"store_id"`
	Cursor  string `json:"cursor"`
	Limit   int    `json:"limit"`
}

func (s *ListActParam) Valid() bool {
	return s.StoreId > 0 && s.Limit < 50
}

type ListActResp struct {
	BaseResp
	List    []*ActInfo `json:"list"`
	Cursor  string     `json:"cursor"`
	HasMore int        `json:"has_more"`
}

type ListActByStoreResp struct {
	BaseResp
	Meta *StoreActInfo `json:"meta"`
}

type StoreActInfo struct {
	AppId     int64      `json:"app_id"`
	StoreId   int        `json:"store_id"`
	Name      string     `json:"name"`
	ShortDesc string     `json:"short_desc"`
	Likes     int64      `json:"likes"`
	Wechat    string     `json:"wechat"`
	Logo      string     `json:"logo"`
	Cover     string     `json:"cover"`
	Weight    int        `json:"weight"`
	Status    int        `json:"status"`
	StoreType int        `json:"store_type"`
	ExtInfo   string     `json:"ext_info"`
	Ctime     int64      `json:"ctime"`
	Mtime     int64      `json:"mtime"`
	ActList   []*ActInfo `json:"act_list"`

	HasMore int    `json:"has_more"`
	Cursor  string `json:"cursor"`
}

type ActInfo struct {
	AppId         int64            `json:"app_id"`
	StoreId       int              `json:"store_id"`
	StoreName     string           `json:"store_name"`
	ActId         int64            `json:"act_id"`
	JumpLink      string           `json:"jump_link"`
	Issuer        string           `json:"issuer"`
	ActName       string           `json:"act_name"`
	Thumb         []string         `json:"thumb"`
	ImgDesc       []string         `json:"img_desc"`
	ShortDesc     string           `json:"short_desc"`
	Status        int              `json:"status"`
	PublishStatus int              `json:"publish_status"`
	Bookable      int              `json:"bookable"`
	Start         int64            `json:"start"`
	End           int64            `json:"end"`
	Weight        int              `json:"weight"`
	ExtInfo       string           `json:"ext_info"`
	Ctime         int64            `json:"ctime"`
	Mtime         int64            `json:"mtime"`
	AstInfoList   []*SimpleAstInfo `json:"ast_info_list,omitempty"`
}

type SimpleAstInfo struct {
	AppId         int64    `json:"app_id"`
	AssetId       int64    `json:"asset_id"`
	Title         string   `json:"title"`
	Amount        int64    `json:"amount"`
	GrantMode     int      `json:"grant_mode"`
	Thumb         []string `json:"thumb"`
	JumpLink      string   `json:"jump_link"`
	Status        int      `json:"status"`
	PublishStatus int      `json:"publish_status"`
	ApplyForm     int      `json:"apply_form"`
	ActId         int64    `json:"act_id"`
	Start         int64    `json:"start"`
	End           int64    `json:"end"`
	ExtInfo       string   `json:"ext_info"`
	Price         int64    `json:"price"`
	OriPrice      int64    `json:"ori_price"`
}

type BindOrAlterAstParam struct {
	AppId     int64  `json:"app_id"`
	AssetId   int64  `json:"asset_id"`
	SplitId   int64  `json:"split_id"`
	Amount    int    `json:"amount"`
	AssetInfo string `json:"asset_info"`
	ApplyForm int    `json:"apply_form"`
	AssetCate int    `json:"asset_cate"`
	Price     int64  `json:"price"`
	OriPrice  int64  `json:"ori_price"`
	ActId     int64  `json:"act_id"`
	Start     int64  `json:"start"`
	End       int64  `json:"end"`
	GrantMode int    `json:"grant_mode"`
	JumpLink  string `json:"jump_link"`
	ExtInfo   string `json:"ext_info"`
	IsBox     int    `json:"is_box"`
}

func (s *BindOrAlterAstParam) BindValid() bool {
	return s.AppId > 0 && s.ActId > 0 && s.AssetId > 0 && s.End >= s.Start
}

func (s *BindOrAlterAstParam) AlterValid() bool {
	return s.AppId > 0 && s.AssetId > 0 && s.ActId > 0
}

type BaseAstParam struct {
	AppId   int64 `json:"app_id"`
	ActId   int64 `json:"act_id"`
	AssetId int64 `json:"asset_id"`
}

func (s *BaseAstParam) Valid() bool {
	return s.ActId > 0 && s.AssetId > 0
}

type QueryAstResp struct {
	BaseResp
	Meta *AstInfo `json:"meta"`
}

type ListAstResp struct {
	BaseResp
	List []*AstInfo `json:"list"`
}

type AstInfo struct {
	AppId         int64    `json:"app_id"`
	StoreId       int64    `json:"store_id"`
	StoreName     string   `json:"store_name"`
	Addr          string   `json:"addr"`
	AssetId       int64    `json:"asset_id"`
	SplitId       int64    `json:"split_id"`
	AssetCate     int      `json:"asset_cate"`
	Thumb         []string `json:"thumb"`
	Title         string   `json:"title"`
	ShortDesc     string   `json:"short_desc"`
	TxId          string   `json:"tx_id"`
	AssetUrl      []string `json:"asset_url"`
	ImgDesc       []string `json:"img_desc"`
	ActId         int64    `json:"act_id"`
	Start         int64    `json:"start"`
	End           int64    `json:"end"`
	Price         int64    `json:"price"`
	OriPrice      int64    `json:"ori_price"`
	Amount        int64    `json:"amount"`
	ApplyForm     int      `json:"apply_form"`
	GrantMode     int      `json:"grant_mode"`
	Status        int      `json:"status"`
	PublishStatus int      `json:"publish_status"`
	JumpLink      string   `json:"jump_link"`
	ExtInfo       string   `json:"ext_info"`
	Ctime         int64    `json:"ctime"`
	Mtime         int64    `json:"mtime"`
	Series        int      `json:"series"`
}

type ListActByAstResp struct {
	BaseResp
	List []*SimpleAstInfo `json:"list"`
}
