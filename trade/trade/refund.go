package trade

import (
	"encoding/json"
	"fmt"
	"net/url"
)

func (t *TradeClient) CheckRefund(param *CheckRefundParam) (*CheckRefundResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, fmt.Errorf("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))

	body := v.Encode()
	reqRes, err := t.doRequest(TradeRefundCheck, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for check refund fail. err: %v", err)
	}
	var result CheckRefundResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) CreateRefund(param *CreateRefundParam) (*CreateRefundResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, fmt.Errorf("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	v.Set("addr", param.Addr)
	v.Set("reason", param.Reason)

	body := v.Encode()
	reqRes, err := t.doRequest(TradeRefundCreate, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for create refund fail. err: %v", err)
	}
	var result CreateRefundResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) CancelRefund(param *CancelRefundParam) (*CancelRefundResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, fmt.Errorf("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("rid", fmt.Sprintf("%d", param.Rid))
	v.Set("addr", param.Addr)

	body := v.Encode()
	reqRes, err := t.doRequest(TradeRefundCancel, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for cancel refund fail. err: %v", err)
	}
	var result CancelRefundResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) ConfirmRefund(param *ConfirmRefundParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, fmt.Errorf("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("rid", fmt.Sprintf("%d", param.Rid))
	v.Set("message", param.Message)
	v.Set("operator", param.Operator)

	body := v.Encode()
	reqRes, err := t.doRequest(TradeRefundConfirm, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for confirm refund fail. err: %v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) RefuseRefund(param *RefuseRefundParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, fmt.Errorf("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("rid", fmt.Sprintf("%d", param.Rid))
	v.Set("message", param.Message)
	v.Set("operator", param.Operator)

	body := v.Encode()
	reqRes, err := t.doRequest(TradeRefundRefuse, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for refuse refund fail. err: %v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) QueryRefundDetail(param *QueryRefundDetailParam) (*QueryRefundDetailResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, fmt.Errorf("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("rid", fmt.Sprintf("%d", param.Rid))
	body := v.Encode()

	reqRes, err := t.doRequest(TradeQueryRefund, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset trade for query refund fail.err:%v", err)
	}
	var result QueryRefundDetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) QueryRefundList(param *QueryRefundListParam) (*QueryRefundListResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, fmt.Errorf("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("address", param.Address)
	if param.StoreIds != "" {
		v.Set("store_ids", param.StoreIds)
	}
	if param.RefundStatus != "" {
		v.Set("refund_status", param.RefundStatus)
	}
	v.Set("cursor", param.Cursor)
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}
	body := v.Encode()

	reqRes, err := t.doRequest(TradeQueryRefundList, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset trade for list refund fail.err:%v", err)
	}
	var result QueryRefundListResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) QueryRefundPage(param *QueryRefundPageParam) (*QueryRefundPageResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, fmt.Errorf("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("address", param.Address)
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	if param.RefundStatus != "" {
		v.Set("refund_status", param.RefundStatus)
	}
	v.Set("page", fmt.Sprintf("%d", param.Page))
	v.Set("size", fmt.Sprintf("%d", param.Size))
	body := v.Encode()

	reqRes, err := t.doRequest(TradeQueryRefundPage, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset trade for pagelist refund fail.err:%v", err)
	}
	var result QueryRefundPageResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) SumRefundPrice(param *SumRefundPriceParam) (*SumRefundPriceResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, fmt.Errorf("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	if param.RefundStatus != "" {
		v.Set("refund_status", param.RefundStatus)
	}
	body := v.Encode()

	reqRes, err := t.doRequest(TradeSumRefundPrice, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset trade for sum refund price fail.err:%v", err)
	}
	var result SumRefundPriceResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}
