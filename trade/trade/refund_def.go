package trade

const (
	TradeRefundCheck     = "/internal/trade/v1/check_refund"
	TradeRefundCreate    = "/internal/trade/v1/create_refund"
	TradeRefundCancel    = "/internal/trade/v1/cancel_refund"
	TradeRefundConfirm   = "/internal/trade/v1/confirm_refund"
	TradeRefundRefuse    = "/internal/trade/v1/refuse_refund"
	TradeQueryRefund     = "/internal/trade/v1/query_refund"
	TradeQueryRefundList = "/internal/trade/v1/list_refund"
	TradeQueryRefundPage = "/internal/trade/v1/pagelist_refund"
	TradeSumRefundPrice  = "/internal/trade/v1/sum_refund_price"
)

type CheckRefundParam struct {
	AppId int64 `json:"app_id"`
	Oid   int64 `json:"oid"`
}

func (p *CheckRefundParam) Valid() bool {
	return p.AppId > 0 && p.Oid > 0
}

type CheckRefundResp struct {
	BaseResp
	Data struct {
		Refundable   int `json:"refundable"`
		RefuseReason int `json:"refuse_reason"`
	} `json:"data"`
}

type CreateRefundParam struct {
	AppId  int64  `json:"app_id"`
	Oid    int64  `json:"oid"`
	Addr   string `json:"addr"`
	Reason string `json:"reason"`
}

func (p *CreateRefundParam) Valid() bool {
	return p.AppId > 0 && p.Oid > 0
}

type CreateRefundResp struct {
	BaseResp
	Data struct {
		Rid          int64 `json:"rid"`
		Refundable   int   `json:"refundable"`
		RefuseReason int   `json:"refuse_reason"`
	} `json:"data"`
}

type CancelRefundParam struct {
	AppId int64  `json:"app_id"`
	Rid   int64  `json:"oid"`
	Addr  string `json:"addr"`
}

func (p *CancelRefundParam) Valid() bool {
	return p.AppId > 0 && p.Rid > 0
}

type CancelRefundResp struct {
	BaseResp
	Data struct {
		Rid int64 `json:"rid"`
	} `json:"data"`
}

type ConfirmRefundParam struct {
	AppId    int64  `json:"app_id"`
	Rid      int64  `json:"rid"`
	Message  string `json:"message"`
	Operator string `json:"operator"`
}

func (p *ConfirmRefundParam) Valid() bool {
	return p.AppId > 0 && p.Rid > 0
}

type RefuseRefundParam struct {
	AppId    int64  `json:"app_id"`
	Rid      int64  `json:"rid"`
	Message  string `json:"message"`
	Operator string `json:"operator"`
}

func (p *RefuseRefundParam) Valid() bool {
	return p.AppId > 0 && p.Rid > 0
}

type RefundInfo struct {
	Rid          int64    `json:"rid"`
	Oid          int64    `json:"oid"`
	StoreId      int64    `json:"store_id"`
	StoreName    string   `json:"store_name"`
	BuyerAddr    string   `json:"buyer_addr"`
	AssetId      int64    `json:"asset_id"`
	ShardIds     []int64  `json:"shard_ids"`
	Title        string   `json:"title"`
	Thumb        []string `json:"thumb"`
	SinglePrice  int      `json:"single_price"`
	PayPrice     int      `json:"pay_price"`
	Count        int      `json:"count"`
	Reason       string   `json:"reason"`
	Message      string   `json:"message"`
	RefundStatus int      `json:"refund_status"`
	Rtime        int64    `json:"rtime"`
	Ctime        int64    `json:"ctime"`
}

type QueryRefundDetailParam struct {
	AppId int64 `json:"app_id"`
	Rid   int64 `json:"rid"`
}

func (p *QueryRefundDetailParam) Valid() bool {
	return p.AppId >= 0 && p.Rid > 0
}

type QueryRefundDetailResp struct {
	BaseResp
	Data RefundInfo `json:"data"`
}

type QueryRefundListParam struct {
	AppId        int64  `json:"app_id"`
	Address      string `json:"address"`
	StoreIds     string `json:"store_ids"`
	RefundStatus string `json:"refund_status"`
	Cursor       string `json:"cursor"`
	Limit        int    `json:"limit"`
}

func (p *QueryRefundListParam) Valid() bool {
	return p.AppId >= 0 && p.Address != ""
}

type QueryRefundListResp struct {
	BaseResp
	Data struct {
		List    []*RefundInfo `json:"list"`
		Cursor  string        `json:"cursor"`
		HasMore int           `json:"has_more"`
	} `json:"data"`
}

type QueryRefundPageParam struct {
	AppId        int64  `json:"app_id"`
	Address      string `json:"address"`
	StoreId      int64  `json:"store_id"`
	RefundStatus string `json:"refund_status"`
	Page         int    `json:"page"`
	Size         int    `json:"size"`
}

func (p *QueryRefundPageParam) Valid() bool {
	return p.AppId > 0 && p.StoreId > 0 && p.Page > 0 && p.Size > 0
}

type QueryRefundPageResp struct {
	BaseResp
	Data struct {
		List        []*RefundInfo `json:"list"`
		TotalAmount int           `json:"total_amount"`
	} `json:"data"`
}

type SumRefundPriceParam struct {
	AppId        int64  `json:"app_id"`
	StoreId      int64  `json:"store_id"`
	RefundStatus string `json:"refund_status"`
}

func (p *SumRefundPriceParam) Valid() bool {
	return p.AppId > 0 && p.StoreId > 0
}

type SumRefundPriceResp struct {
	BaseResp
	Data struct {
		SumPrice    int `json:"sum_price"`
		TotalAmount int `json:"total_amount"`
	} `json:"data"`
}
