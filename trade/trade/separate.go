package trade

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
)

func (t *TradeClient) PreCreateSeparate(param *PreCreateSeparateParam) (*PreCreateSeparateResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("appId", fmt.Sprintf("%d", param.AppId))
	v.Set("cmsContractId", param.CmsContractId)
	v.Set("businessLicense", param.BusinessLicense)
	v.Set("companyName", param.CompanyName)
	body := v.Encode()
	reqRes, err := t.doRequest(PreCreateSeparateApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for PreCreateSeparateApi fail.err:%v", err)
	}
	var result PreCreateSeparateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) simplifyParamUrlValue(param interface{}) (string, error) {
	v := url.Values{}
	st, err := json.Marshal(param)
	if err != nil {
		return "", errors.New("marshal error")
	}
	s := map[string]string{}
	if err := json.Unmarshal(st, &s); err != nil {
		return "", errors.New("unmarshal error")
	}
	for key, value := range s {
		v.Set(key, value)
	}
	return v.Encode(), nil
}

func (t *TradeClient) EditCreateSeparate(param *EditNSubmitSeparateParam) (*EditSeparateResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}

	body, err := t.simplifyParamUrlValue(param)
	if err != nil {
		return nil, nil, err
	}
	reqRes, err := t.doRequest(EditCreateSeparateApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for EditCreateSeparateApi fail.err:%v", err)
	}
	var result EditSeparateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) SubmitSeparate(param *EditNSubmitSeparateParam) (*SubmitSeparateResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}

	body, err := t.simplifyParamUrlValue(param)
	if err != nil {
		return nil, nil, err
	}
	reqRes, err := t.doRequest(SubmitSeparateApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for SubmitSeparateApi fail.err:%v", err)
	}
	var result SubmitSeparateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) AuditSeparate(param *AuditSeparateParam) (*AuditCreateSeparateAccountResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("splitName", fmt.Sprintf("%d", param.SplitName))
	v.Set("cmsContractId", param.CmsContractId)
	v.Set("skuId", param.SkuId)
	v.Set("success", fmt.Sprintf("%d", param.Success))
	v.Set("failReason", param.FailReason)
	v.Set("separateRatio", fmt.Sprintf("%d", param.SeparateRatio))
	v.Set("commissionSeparateRatio", fmt.Sprintf("%d", param.CommissionSeparateRatio))
	v.Set("commissionRate", fmt.Sprintf("%d", param.CommissionRate))
	body := v.Encode()
	reqRes, err := t.doRequest(AuditSeparateApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for AuditSeparateApi fail.err:%v", err)
	}
	var result AuditCreateSeparateAccountResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) UpdateSeparateRadio(param *UpdateSeparateRadioParam) (*UpdateSeparateRadioResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("splitName", fmt.Sprintf("%d", param.SplitName))
	v.Set("cmsContractId", param.CmsContractId)
	v.Set("separateRatio", fmt.Sprintf("%d", param.SeparateRatio))
	v.Set("commissionSeparateRatio", fmt.Sprintf("%d", param.CommissionSeparateRatio))
	body := v.Encode()
	reqRes, err := t.doRequest(UpdateSeparateRatioApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for UpdateSeparateRatioApi fail.err:%v", err)
	}
	var result UpdateSeparateRadioResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) SeparateDetail(param *QuerySeparateDetail) (*DetailResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("appId", fmt.Sprintf("%d", param.AppId))
	v.Set("splitName", fmt.Sprintf("%d", param.SplitName))
	body := v.Encode()
	reqRes, err := t.doRequest(QuerySeparateDetailApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for QuerySeparateDetailApi fail.err:%v", err)
	}
	var result DetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) SeparateAuditDetail(splitName int64) (*DetailResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if splitName <= 0 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("splitName", fmt.Sprintf("%d", splitName))
	body := v.Encode()
	reqRes, err := t.doRequest(AuditDetailApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for AuditDetailApi fail.err:%v", err)
	}
	var result DetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) ListSeparate(param *ListSeparateParam) (*ListSeparateResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("appId", fmt.Sprintf("%d", param.AppId))
	v.Set("page", fmt.Sprintf("%d", param.Page))
	v.Set("size", fmt.Sprintf("%d", param.Size))
	v.Set("status", fmt.Sprintf("%d", param.Status))
	body := v.Encode()
	reqRes, err := t.doRequest(ListSeparateApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for ListSeparateApi fail.err:%v", err)
	}
	var result ListSeparateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) AuditListSeparate(param *ListAuditSeparateParam) (*ListSeparateResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("page", fmt.Sprintf("%d", param.Page))
	v.Set("size", fmt.Sprintf("%d", param.Size))
	body := v.Encode()
	reqRes, err := t.doRequest(AuditListSeparateApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for AuditListSeparateApi fail.err:%v", err)
	}
	var result ListSeparateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) AuditQuerySeparate(param *AuditFilterSeparateParam) (*ListSeparateResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("companyName", param.CompanyName)
	v.Set("page", fmt.Sprintf("%d", param.Page))
	v.Set("size", fmt.Sprintf("%d", param.Size))
	body := v.Encode()
	reqRes, err := t.doRequest(AuditQuerySeparateApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for AuditQuerySeparateApi fail.err:%v", err)
	}
	var result ListSeparateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) BrowseSeparateInfo() (*BankCommonInfoResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	reqRes, err := t.doRequest(BrowserBankApi, "")
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for BrowserBankApi fail.err:%v", err)
	}
	var result BankCommonInfoResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) BankBranch(param *BankBranchParam) (*QueryBankBranchResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("province", param.Province)
	v.Set("city", param.City)
	v.Set("bankName", param.BankName)
	v.Set("branchName", param.BranchName)
	body := v.Encode()
	reqRes, err := t.doRequest(BankBranchApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for BankBranchApi fail.err:%v", err)
	}
	var result QueryBankBranchResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}
