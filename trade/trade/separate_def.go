package trade

import (
	"errors"
)

const (
	BrowserBankApi         = "/internal/trade/billing/separate/v1/browse_banks"
	BankBranchApi          = "/internal/trade/billing/separate/v1/bank_branch"
	PreCreateSeparateApi   = "/internal/trade/billing/separate/v1/pre_create"
	EditCreateSeparateApi  = "/internal/trade/billing/separate/v1/edit_account"
	SubmitSeparateApi      = "/internal/trade/billing/separate/v1/submit"
	QuerySeparateDetailApi = "/internal/trade/billing/separate/v1/detail"
	ListSeparateApi        = "/internal/trade/billing/separate/v1/list"
	// 审核端
	AuditSeparateApi       = "/internal/trade/billing/separate/v1/audit"
	AuditDetailApi         = "/internal/trade/billing/separate/v1/audit_detail"
	AuditListSeparateApi   = "/internal/trade/billing/separate/v1/audit_list"
	AuditQuerySeparateApi  = "/internal/trade/billing/separate/v1/audit_query"
	UpdateSeparateRatioApi = "/internal/trade/billing/separate/v1/update_ratio"
)

type ListSeparateParam struct {
	AppId  int64 `json:"appId"`
	Page   int   `json:"page"`
	Size   int   `json:"size"`
	Status int   `json:"status"`
}

func (p *ListSeparateParam) Valid() error {
	if p.AppId <= 0 {
		return errors.New("appId invalid")
	}
	if p.Page <= 0 {
		return errors.New("page invalid")
	}
	if p.Size <= 0 {
		return errors.New("size invalid")
	}

	return nil
}

type ListAuditSeparateParam struct {
	Page int `json:"page"`
	Size int `json:"size"`
}

func (p *ListAuditSeparateParam) Valid() error {
	if p.Page <= 0 {
		return errors.New("page invalid")
	}
	if p.Size <= 0 {
		return errors.New("size invalid")
	}

	return nil
}

type ListSeparateAccNode struct {
	AppID                   int64  `json:"appId"`
	SeparateID              int64  `json:"splitName"`
	SeparateRatio           int    `json:"separateRatio"`
	CommissionSeparateRatio int    `json:"commissionSeparateRatio"`
	CompanyName             string `json:"companyName"`
	Status                  int    `json:"status"`
	Ctime                   int64  `json:"ctime"`
}

type ListSeparateResp struct {
	BaseResp
	Data struct {
		List  []*ListSeparateAccNode
		Total int64
	} `json:"data"`
}

type QueryBankBranchResp struct {
	BaseResp
	Data struct {
		BranchBanks []string `json:"branchBanks"` // 相关支行
	} `json:"data"`
}

type BankCommonInfoResp struct {
	BaseResp
	Data struct {
		Cities   []CityInfo     `json:"cities"`   // 省市信息
		Banks    []string       `json:"banks"`    // 银行名称列表
		Industry []IndustryInfo `json:"industry"` // 行业信息
	} `json:"data"`
}

type CityInfo struct {
	Province string   `json:"province"` // 省份
	CityList []string `json:"cityList"` // 市/区
}

type IndustryInfo struct {
	IndustryId       int             `json:"industryId"`       // 行业id
	IndustryName     string          `json:"industryName"`     // 行业名称
	NeedPermit       int             `json:"needPermit"`       // 是否需要行业许可证 0不需要，1需要
	IndustryLevel    int             `json:"industryLevel"`    // 行业层级
	ParentIndustryId int             `json:"parentIndustryId"` // 上级行业id
	PermitDesc       string          `json:"permitDesc"`       // 行业许可证描述
	SubIndustry      []*IndustryInfo `json:"subIndustry"`      // 子行业列表
}

type BankBranchParam struct {
	Province   string `json:"province"`
	City       string `json:"city"`
	BankName   string `json:"bankName"`
	BranchName string `json:"branchName"`
}

func (p *BankBranchParam) Valid() error {
	if len(p.Province) == 0 || len(p.City) == 0 || len(p.BankName) == 0 {
		return errors.New("invalid empty branch param")
	}
	return nil
}

type QuerySeparateDetail struct {
	AppId     int64 `json:"appId"`
	SplitName int64 `json:"splitName"`
}

func (p *QuerySeparateDetail) Valid() error {
	if p.AppId <= 0 || p.SplitName <= 0 {
		return errors.New("invalid empty detail param")
	}

	return nil
}

type DetailItem struct {
	AppID                   int64  `json:"appId"`
	SeparateID              int64  `json:"splitName"`             // splitName
	SeparateAccountNumber   int64  `json:"separateAccountNumber"` // billing返回
	SeparateAccountId       int64  `json:"separateAccountId"`     // billing返回
	SeparateRatio           int    `json:"separateRatio"`
	CommissionRatio         int    `json:"commissionRatio"`
	CommissionSeparateRatio int    `json:"commissionSeparateRatio"`
	CmsContractId           string `json:"cmsContractId"`
	SkuId                   string `json:"skuId"` // pm填写
	BusinessLicense         string `json:"businessLicense"`
	Status                  int    `json:"status"`
	StatusDesc              string `json:"statusDesc"` // 审核失败时填写失败原因

	DayMaxFrozenAmount    int    `json:"dayMaxFrozenAmount"`    // 每天最大退款限额/元, 默认为10000
	PoolCashPledge        int    `json:"poolCashPledge"`        // 提现后的保留金额/元, 默认0
	SupplierType          int    `json:"supplierType"`          // 企业类型 1：企业 2：个体工商户
	CompanyName           string `json:"companyName"`           // 企业名称
	BusinessScope         string `json:"businessScope"`         // 经营范围
	BusinessProvince      string `json:"businessProvince"`      // 经营省份
	BusinessCity          string `json:"businessCity"`          // 经营城市
	BusinessDetailAddress string `json:"businessDetailAddress"` // 经营地区详细地址
	LicenseUrl            string `json:"licenseUrl"`            // 营业执照
	LicenseStartTime      string `json:"licenseStartTime"`      // 营业执照开始时间
	LicenseEndTime        string `json:"licenseEndTime"`        // 营业执照结束时间
	Integrate             int    `json:"integrate"`
	TaxRegistrationNo     string `json:"taxRegistrationNo"`   // 税务登记证号, 非三证合一必填
	TaxRegistrationUrl    string `json:"taxRegistrationUrl"`  // 税务登录证图片URL, 非三证合一必填
	TaxEndTime            string `json:"taxEndTime"`          // 税务登记证结束时间, 非三证合一必填
	OrganizationCode      string `json:"organizationCode"`    // 组织机构代码, 非三证合一必填
	OrganizationUrl       string `json:"organizationUrl"`     // 组织机构代码图片URL, 非三证合一必填
	OrganizationEndTime   string `json:"organizationEndTime"` // 组织结构代码结束时间, 非三证合一必填

	PaymentDays     int    `json:"paymentDays"`     // capitalSettlementType=1时必传
	StlAcctType     int    `json:"stlAcctType"`     // 结算对象类型 1-企业对公户 2-法人对私户 3-经营联系人对私户, 默认1
	OccupationType  string `json:"occupationType"`  // 职业, 对私结算必填，0-国家机关、党群组织、企业、事业单位负责人 1-专业技术人员  3-办事人员和有关人员 4-商业、服务业人员  5-农、林、牧、渔、水利业生产人员 6-生产、运输设备操作人员及有关人员 X-军人
	IndustryId      int    `json:"industryId"`      // 行业id, 通过接口查询的结果
	ManagePermitUrl string `json:"managePermitUrl"` // 经营许可证，若接口查询为needPermit=1则必填
	AuthCapital     int    `json:"authCapital"`     // 注册资本（元）

	BankAccount    string `json:"bankAccount"`    // 开户名
	BankCard       string `json:"bankCard"`       // 银行卡号
	BankProvince   string `json:"bankProvince"`   // 开户省份
	BankCity       string `json:"bankCity"`       // 开户城市
	BankName       string `json:"bankName"`       // 所属银行
	BankBranchName string `json:"bankBranchName"` // 开户支行
	PhoneNumber    string `json:"phoneNumber"`    // 银行预留手机号

	LegalPerson        string `json:"legalPerson"`        // 法人姓名
	LegalId            string `json:"legalId"`            // 法人身份证号
	LegalPersonType    int    `json:"legalPersonType"`    // 法人证件类型: 1, “身份证”, 3, “外国护照”
	LegalCardStartTime string `json:"legalCardStartTime"` // 法人证件开始时间
	LegalCardEndTime   string `json:"legalCardEndTime"`   // 法人证件结束时间
	IdCardFrontUrl     string `json:"idCardFrontUrl"`     // 法人身份证正面
	IdCardBackUrl      string `json:"idCardBackUrl"`      // 法人身份证反面

	ManagerSame          int    `json:"managerSame"`          // 经营控股人是否与法人一致 1是 0或其它值不是
	Manager              string `json:"manager"`              // 经营控股人managerSame=1取法人信息，否则必填, 2到50字符
	ManagerCardType      int    `json:"managerCardType"`      // 经营控股人证件类型, managerSame=1取法人信息, managerSame!=1不传则默认身份证
	ManagerCard          string `json:"managerCard"`          // 经营控股人证件号 managerSame=1取法人信息，否则必填
	ManagerCardStartTime string `json:"managerCardStartTime"` // 经营控股人证件开始时间, managerSame=1取法人信息，否则必填,yyyy-MM-dd，不早于结束时间
	ManagerCardEndTime   string `json:"managerCardEndTime"`   // 经营控股人证件结束时间 managerSame=1取法人信息，否则必填 yyyy-MM-dd，今天之后
	ManagerCardFrontUrl  string `json:"managerCardFrontUrl"`  // 经营控股人证件正面 managerSame=1取法人信息，否则必填 不超过255字符
	ManagerCardBackUrl   string `json:"managerCardBackUrl"`   // 经营控股人证件反面 managerSame=1取法人信息，否则必填 不超过255字符

	ContactSame          int    `json:"contactSame"`          // 联系人是否与法人一致
	Contact              string `json:"contact"`              // 联系人，contact_same=1取法人信息，否则必填
	ContactCard          string `json:"contactCard"`          // 联系人证件, ContactSame=1取法人信息，否则必填
	ContactCardType      int    `json:"contactCardType"`      // 联系人证件类型, contactSame=1取法人信息 contactSame!=1默认身份证，录入值参考参考legalPersonType
	ContactCardStartTime string `json:"contactCardStartTime"` // 联系人证件开始时间, ContactSame=1取法人信息，否则必填
	ContactCardEndTime   string `json:"contactCardEndTime"`   // 联系人证件结束时间, ContactSame=1取法人信息，否则必填
	ContactCardFrontUrl  string `json:"contactCardFrontUrl"`  // 联系人证件正面, ContactSame=1取法人信息，否则必填
	ContactCardBackUrl   string `json:"contactCardBackUrl"`   // 联系人证件反面, ContactSame=1取法人信息，否则必填

	BenefitSame          int    `json:"benefitSame"`          // 受益人是否与法人一致 1是 0或其它值不是
	Benefit              string `json:"benefit"`              // benefitSame=1取法人信息，否则必填, 2到50字符
	BenefitCardType      int    `json:"benefitCardType"`      // 受益人证件类型, benefitSame=1取法人信息, benefitSame!=1不传则默认身份证
	BenefitCard          string `json:"benefitCard"`          // 受益人证件, benefitSame=1取法人信息，否则必填
	BenefitCardStartTime string `json:"benefitCardStartTime"` // 受益人证件开始时间, benefitSame=1取法人信息，否则必填
	BenefitCardEndTime   string `json:"benefitCardEndTime"`   // 受益人证件结束时间, benefitSame=1取法人信息，否则必填
	BenefitCardFrontUrl  string `json:"benefitCardFrontUrl"`  // 受益人证件正面, benefitSame=1取法人信息，否则必填
	BenefitCardBackUrl   string `json:"benefitCardBackUrl"`   // 受益人证件反面, benefitSame=1取法人信息，否则必填

	// pic https
	License string `json:"license"`

	Tax          string `json:"taxRegistration"`
	Organization string `json:"organization"`
	ManagePermit string `json:"managePermit"`
	IdFront      string `json:"idCardFront"`
	IdBack       string `json:"idCardBack"`
	ManagerFront string `json:"managerCardFront"`
	ManageBack   string `json:"managerCardBack"`
	ContactFront string `json:"contactCardFront"`
	ContactBack  string `json:"contactCardBack"`
	BenefitFront string `json:"benefitCardFront"`
	BenefitBack  string `json:"benefitCardBack"`

	Ctime int64 `json:"ctime"`
}

type DetailResp struct {
	BaseResp
	Data struct {
		Detail DetailItem `json:"detail"`
	} `json:"data"`
}

type AuditSeparateParam struct {
	SplitName               int64  `json:"splitName"`
	CmsContractId           string `json:"cmsContractId"`
	SkuId                   string `json:"skuId"`
	Success                 int    `json:"success"`
	FailReason              string `json:"failReason"`
	SeparateRatio           int    `json:"separateRatio"`
	CommissionSeparateRatio int    `json:"commissionSeparateRatio"`
	CommissionRate          int    `json:"commissionRate"`
}

func (p *AuditSeparateParam) Valid() error {
	if p.SplitName <= 0 {
		return errors.New("invalid split name")
	}
	if p.Success < 0 {
		return errors.New("invalid success")
	}

	return nil
}

type AuditCreateSeparateAccountResp struct {
	BaseResp
	Data struct {
		Success               int    `json:"success"`
		SeparateAccountNumber int64  `json:"separateAccountNumber"`
		FailReason            string `json:"failReason"`
	} `json:"data"`
}

type UpdateSeparateRadioParam struct {
	SplitName               int64  `json:"splitName"`
	CmsContractId           string `json:"cmsContractId"`
	SeparateRatio           int    `json:"separateRatio"`
	CommissionSeparateRatio int    `json:"commissionSeparateRatio"`
}

func (p *UpdateSeparateRadioParam) Valid() error {
	if p.SplitName <= 0 {
		return errors.New("invalid split name")
	}

	return nil
}

type UpdateSeparateRadioResp struct {
	BaseResp
	Data struct {
		Success    int    `json:"success"`
		FailReason string `json:"failReason"`
	} `json:"data"`
}

type PreCreateSeparateParam struct {
	AppId           uint64 `json:"appId"`
	CmsContractId   string `json:"cmsContractId"`
	BusinessLicense string `json:"businessLicense"`
	CompanyName     string `json:"companyName"`
}

type PreCreateSeparateResp struct {
	BaseResp
	Data struct {
		Success    int        `json:"success"`
		SplitName  int64      `json:"splitName"`
		FailReason string     `json:"failReason"`
		Detail     DetailItem `json:"detail"`
	} `json:"data"`
}

type EditNSubmitSeparateParam struct {
	AppId                 uint64 `json:"appId,string"`
	SplitName             int64  `json:"splitName,string"`
	CmsContractId         string `json:"cmsContractId"`
	BusinessLicense       string `json:"businessLicense"`
	CompanyName           string `json:"companyName"`
	DayMaxFrozenAmount    int    `json:"dayMaxFrozenAmount,string"` // 每天最大退款限额/元, 默认为10000
	PoolCashPledge        int    `json:"poolCashPledge,string"`     // 提现后的保留金额/元, 默认0
	SupplierType          int    `json:"supplierType,string"`       // 企业类型 1：企业 2：个体工商户
	BusinessScope         string `json:"businessScope"`             // 经营范围
	BusinessProvince      string `json:"businessProvince"`          // 经营省份
	BusinessCity          string `json:"businessCity"`              // 经营城市
	BusinessDetailAddress string `json:"businessDetailAddress"`     // 经营地区详细地址
	LicenseUrl            string `json:"licenseUrl"`                // 营业执照
	LicenseStartTime      string `json:"licenseStartTime"`          // 营业执照开始时间
	LicenseEndTime        string `json:"licenseEndTime"`            // 营业执照结束时间
	Integrate             int    `json:"integrate,string"`
	TaxRegistrationNo     string `json:"taxRegistrationNo"`      // 税务登记证号, 非三证合一必填
	TaxRegistrationUrl    string `json:"taxRegistrationUrl"`     // 税务登录证图片URL, 非三证合一必填
	TaxEndTime            string `json:"taxEndTime"`             // 税务登记证结束时间, 非三证合一必填
	OrganizationCode      string `json:"organizationCode"`       // 组织机构代码, 非三证合一必填
	OrganizationUrl       string `json:"organizationUrl"`        // 组织机构代码图片URL, 非三证合一必填
	OrganizationEndTime   string `json:"organizationEndTime"`    // 组织结构代码结束时间, 非三证合一必填
	PaymentDays           int    `json:"paymentDays,string"`     // capitalSettlementType=1时必传
	StlAcctType           int    `json:"stlAcctType,string"`     // 结算对象类型 1-企业对公户 2-法人对私户 3-经营联系人对私户, 默认1
	OccupationType        string `json:"occupationType"`         // 职业, 对私结算必填，0-国家机关、党群组织、企业、事业单位负责人 1-专业技术人员  3-办事人员和有关人员 4-商业、服务业人员  5-农、林、牧、渔、水利业生产人员 6-生产、运输设备操作人员及有关人员 X-军人
	IndustryId            int    `json:"industryId,string"`      // 行业id, 通过接口查询的结果
	ManagePermitUrl       string `json:"managePermitUrl"`        // 经营许可证，若接口查询为needPermit=1则必填
	AuthCapital           int    `json:"authCapital,string"`     // 注册资本（元）
	BankAccount           string `json:"bankAccount"`            // 开户名
	BankCard              string `json:"bankCard"`               // 银行卡号
	BankProvince          string `json:"bankProvince"`           // 开户省份
	BankCity              string `json:"bankCity"`               // 开户城市
	BankName              string `json:"bankName"`               // 所属银行
	BankBranchName        string `json:"bankBranchName"`         // 开户支行
	PhoneNumber           string `json:"phoneNumber"`            // 银行预留手机号
	LegalPerson           string `json:"legalPerson"`            // 法人姓名
	LegalId               string `json:"legalId"`                // 法人身份证号
	LegalPersonType       int    `json:"legalPersonType,string"` // 法人证件类型: 1, “身份证”, 3, “外国护照”
	LegalCardStartTime    string `json:"legalCardStartTime"`     // 法人证件开始时间
	LegalCardEndTime      string `json:"legalCardEndTime"`       // 法人证件结束时间
	IdCardFrontUrl        string `json:"idCardFrontUrl"`         // 法人身份证正面
	IdCardBackUrl         string `json:"idCardBackUrl"`          // 法人身份证反面
	ManagerSame           int    `json:"managerSame,string"`     // 经营控股人是否与法人一致 1是 0或其它值不是
	Manager               string `json:"manager"`                // 经营控股人managerSame=1取法人信息，否则必填, 2到50字符
	ManagerCardType       int    `json:"managerCardType,string"` // 经营控股人证件类型, managerSame=1取法人信息, managerSame!=1不传则默认身份证
	ManagerCard           string `json:"managerCard"`            // 经营控股人证件号 managerSame=1取法人信息，否则必填
	ManagerCardStartTime  string `json:"managerCardStartTime"`   // 经营控股人证件开始时间, managerSame=1取法人信息，否则必填,yyyy-MM-dd，不早于结束时间
	ManagerCardEndTime    string `json:"managerCardEndTime"`     // 经营控股人证件结束时间 managerSame=1取法人信息，否则必填 yyyy-MM-dd，今天之后
	ManagerCardFrontUrl   string `json:"managerCardFrontUrl"`    // 经营控股人证件正面 managerSame=1取法人信息，否则必填 不超过255字符
	ManagerCardBackUrl    string `json:"managerCardBackUrl"`     // 经营控股人证件反面 managerSame=1取法人信息，否则必填 不超过255字符
	ContactSame           int    `json:"contactSame,string"`     // 联系人是否与法人一致
	Contact               string `json:"contact"`                // 联系人，contact_same=1取法人信息，否则必填
	ContactCard           string `json:"contactCard"`            // 联系人证件, ContactSame=1取法人信息，否则必填
	ContactCardType       int    `json:"contactCardType,string"` // 联系人证件类型, contactSame=1取法人信息 contactSame!=1默认身份证，录入值参考参考legalPersonType
	ContactCardStartTime  string `json:"contactCardStartTime"`   // 联系人证件开始时间, ContactSame=1取法人信息，否则必填
	ContactCardEndTime    string `json:"contactCardEndTime"`     // 联系人证件结束时间, ContactSame=1取法人信息，否则必填
	ContactCardFrontUrl   string `json:"contactCardFrontUrl"`    // 联系人证件正面, ContactSame=1取法人信息，否则必填
	ContactCardBackUrl    string `json:"contactCardBackUrl"`     // 联系人证件反面, ContactSame=1取法人信息，否则必填
	BenefitSame           int    `json:"benefitSame,string"`     // 受益人是否与法人一致 1是 0或其它值不是
	Benefit               string `json:"benefit"`                // benefitSame=1取法人信息，否则必填, 2到50字符
	BenefitCardType       int    `json:"benefitCardType,string"` // 受益人证件类型, benefitSame=1取法人信息, benefitSame!=1不传则默认身份证
	BenefitCard           string `json:"benefitCard"`            // 受益人证件, benefitSame=1取法人信息，否则必填
	BenefitCardStartTime  string `json:"benefitCardStartTime"`   // 受益人证件开始时间, benefitSame=1取法人信息，否则必填
	BenefitCardEndTime    string `json:"benefitCardEndTime"`     // 受益人证件结束时间, benefitSame=1取法人信息，否则必填
	BenefitCardFrontUrl   string `json:"benefitCardFrontUrl"`    // 受益人证件正面, benefitSame=1取法人信息，否则必填
	BenefitCardBackUrl    string `json:"benefitCardBackUrl"`     // 受益人证件反面, benefitSame=1取法人信息，否则必填
}

type EditSeparateResp struct {
	BaseResp
	Data struct {
		Success    int    `json:"success"`
		SplitName  int64  `json:"splitName"`
		FailReason string `json:"failReason"`
	} `json:"data"`
}

type SubmitSeparateResp struct {
	BaseResp
	Data struct {
		Success    int    `json:"success"`
		FailReason string `json:"failReason"`
	} `json:"data"`
}

type AuditFilterSeparateParam struct {
	CompanyName string `json:"companyName"`
	Page        int    `json:"page"`
	Size        int    `json:"size"`
}

func (p *AuditFilterSeparateParam) Valid() error {
	if len(p.CompanyName) <= 0 {
		return errors.New("companyName invalid")
	}
	if p.Size <= 0 {
		return errors.New("size invalid")
	}
	if p.Page <= 0 {
		return errors.New("page invalid")
	}

	return nil
}
