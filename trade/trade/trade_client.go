package trade

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type TradeFunc func(hc *TradeClient) (interface{}, *RequestRes, error)

type TradeClient struct {
	addr *addr.Addr
}

func NewTradeClient(addr *addr.Addr) *TradeClient {
	return &TradeClient{addr}
}

func RetryTrade(getAddr func() (*addr.Addr, error), f TradeFunc, tryTime int) (resp interface{}, reqResp *RequestRes, err error) {
	if f == nil {
		return nil, nil, errors.New("need trade function")
	}

	var tradeAddr *addr.Addr
	for i := 0; i < tryTime; i++ {
		tradeAddr, err = getAddr()
		if err != nil {
			continue
		}
		client := NewTradeClient(tradeAddr)
		resp, reqResp, err = f(client)
		if err == nil {
			break
		}
	}
	if err != nil {
		return nil, nil, err
	}
	return resp, reqResp, nil
}

func (t *TradeClient) CompleteOrder(param *CompleteOrderParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	body := v.Encode()
	reqRes, err := t.doRequest(TradeCompleteOrder, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for complete order detail fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) QueryFirstPaidOrder(param *FirstPaidOrderParam) (*HubOrderDetailResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("addr", param.Addr)

	body := v.Encode()
	reqRes, err := t.doRequest(TradeFirstPaidOrder, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for query first paid order fail. err: %v", err)
	}
	var result HubOrderDetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) QueryOrderList(param *ListOrderParam) (*ListOrderResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	if param.Cursor != "" {
		v.Set("cursor", param.Cursor)
	}
	if param.Status >= 0 {
		v.Set("status", fmt.Sprintf("%d", param.Status))
	}
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(TradeQueryOrderList, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for query order list fail.err:%v", err)
	}
	var result ListOrderResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) HubCreateOrder(param *HubCreateOrderParam, auth string) (*HubCreateResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("code", fmt.Sprintf("%d", param.Code))
	v.Set("order_type", fmt.Sprintf("%d", param.OrderType))
	v.Set("executor", param.ExecutorAPI)
	v.Set("executor_data", param.ExecutorData)
	v.Set("timestamp", fmt.Sprintf("%d", param.Timestamp))
	v.Set("time_expire", fmt.Sprintf("%d", param.TimeExpire))
	v.Set("profit_sharing", fmt.Sprintf("%d", param.ProfitSharing))
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("account_id", param.AccountId)
	v.Set("creator_details", param.Details)
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("store_name", param.StoreName)
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("split_id", fmt.Sprintf("%d", param.SplitId))
	v.Set("buyer_addr", param.BuyerAddr)
	v.Set("seller_addr", param.SellerAddr)
	v.Set("asset_info", param.AssetInfo)
	v.Set("buy_count", fmt.Sprintf("%d", param.BuyCount))
	v.Set("asset_amount", fmt.Sprintf("%d", param.AssetAmount))
	v.Set("origin_price", fmt.Sprintf("%d", param.OriginPrice))
	v.Set("pay_price", fmt.Sprintf("%d", param.PayPrice))
	v.Set("client_type", fmt.Sprintf("%d", param.ClientType))
	v.Set("chan", fmt.Sprintf("%d", param.Chan))
	v.Set("scene", fmt.Sprintf("%d", param.Scene))
	v.Set("gift", param.Gift)
	body := v.Encode()
	reqRes, err := t.doRequest(HubCreateOrder, body, map[string]interface{}{
		"Cookie":    auth,
		"ReqRwTime": CreateOrderRwTimeoutMs,
	})
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for create hub order fail. err: %v", err)
	}
	var result HubCreateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal hub response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) HubConfirmOrder(param *HubConfirmOrderParam, auth string) (*HubCreateResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("code", fmt.Sprintf("%d", param.Code))
	v.Set("order_type", fmt.Sprintf("%d", param.OrderType))
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	v.Set("client_type", fmt.Sprintf("%d", param.ClientType))
	v.Set("creator_details", param.Details)
	body := v.Encode()
	reqRes, err := t.doRequest(HubConfirmOrder, body, map[string]interface{}{
		"Cookie":    auth,
		"ReqRwTime": CreateOrderRwTimeoutMs,
	})
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for confirm hub order fail. err: %v", err)
	}
	var result HubCreateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal hub response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) HubEditOrder(param *HubEditOrderParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	v.Set("status", fmt.Sprintf("%d", param.Status))
	v.Set("pay_channel", fmt.Sprintf("%d", param.PayChannel))
	v.Set("third_oid", param.ThirdOid)
	v.Set("pay_info", param.PayInfo)
	v.Set("pay_time", fmt.Sprintf("%d", param.PayTime))
	v.Set("close_time", fmt.Sprintf("%d", param.CloseTime))
	v.Set("close_reason", param.CloseReason)

	body := v.Encode()
	reqRes, err := t.doRequest(HubEditOrder, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for edit hub order fail. err: %v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal hub response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) HubDetailOrder(param *HubOrderDetailParam) (*HubOrderDetailResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))

	body := v.Encode()
	reqRes, err := t.doRequest(HubDetailOrder, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for query hub order fail. err: %v", err)
	}
	var result HubOrderDetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal hub response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) HubListOrder(param *HubListOrderParam) (*HubListOrderResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("address", param.Addr)
	v.Set("status", fmt.Sprintf("%d", param.Status))
	v.Set("cursor", param.Cursor)
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}
	v.Set("time_begin", fmt.Sprintf("%d", param.TimeBegin))
	v.Set("time_end", fmt.Sprintf("%d", param.TimeEnd))
	v.Set("monotonicity", fmt.Sprintf("%d", param.Mono))

	body := v.Encode()
	reqRes, err := t.doRequest(HubListOrder, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for list hub order fail. err: %v", err)
	}
	var result HubListOrderResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal hub response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) HubNotifyOrder(param *NotifyFromSmartApp) (*NotifyOwnedMallResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("userId", fmt.Sprintf("%d", param.UserId))
	v.Set("orderId", fmt.Sprintf("%d", param.OrderId))
	v.Set("unitPrice", fmt.Sprintf("%d", param.UnitPrice))
	v.Set("count", fmt.Sprintf("%d", param.Count))
	v.Set("totalMoney", fmt.Sprintf("%d", param.TotalMoney))
	v.Set("payMoney", fmt.Sprintf("%d", param.PayMoney))
	v.Set("promoMoney", fmt.Sprintf("%d", param.PromoMoney))
	v.Set("hbMoney", fmt.Sprintf("%d", param.HbMoney))
	v.Set("hbBalanceMoney", fmt.Sprintf("%d", param.HbBalanceMoney))
	v.Set("giftCardMoney", fmt.Sprintf("%d", param.GiftCardMoney))
	v.Set("dealId", fmt.Sprintf("%d", param.DealId))
	v.Set("payTime", fmt.Sprintf("%d", param.PayTime))
	v.Set("promoDetail", param.PromoDetail)
	v.Set("payType", fmt.Sprintf("%d", param.PayType))
	v.Set("partnerId", fmt.Sprintf("%d", param.PartnerId))
	v.Set("status", fmt.Sprintf("%d", param.Status))
	v.Set("tpOrderId", param.TpOrderId)
	v.Set("returnData", param.ReturnData)
	v.Set("rsaSign", param.RsaSign)
	body := v.Encode()
	reqRes, err := t.doRequest(HubNotifyOrder, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for hub notify order fail.err:%v", err)
	}
	var result NotifyOwnedMallResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) HubKnockOrder(param *HubKnockOrderParam) (*HubKnockOrderResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("code", fmt.Sprintf("%d", param.Code))
	v.Set("order_type", fmt.Sprintf("%d", param.OrderType))
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	body := v.Encode()
	reqRes, err := t.doRequest(HubKnockOrder, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for hub knock order fail.err:%v", err)
	}
	var result HubKnockOrderResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) HubOrdePager(param *HubOrderPageParam) (*HubOrderPageResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("address", param.Addr)
	v.Set("status", fmt.Sprintf("%d", param.Status))
	v.Set("page", fmt.Sprintf("%d", param.Page))
	v.Set("size", fmt.Sprintf("%d", param.Size))
	v.Set("time_begin", fmt.Sprintf("%d", param.TimeBegin))
	v.Set("time_end", fmt.Sprintf("%d", param.TimeEnd))

	body := v.Encode()
	reqRes, err := t.doRequest(HubOrderPage, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for list hub order page fail. err: %v", err)
	}
	var result HubOrderPageResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal hub response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) CountOrder(param *CountOrderParam) (*CountOrderResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("status", fmt.Sprintf("%d", param.Status))
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("refund_status", fmt.Sprintf("%d", param.RefundStatus))

	body := v.Encode()
	reqRes, err := t.doRequest(PGCCountOrder, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for count order fail. err: %v", err)
	}
	var result CountOrderResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal hub response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) SumTotalPrice(param *SumTotalPriceParam) (*SumTotalPriceResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("status", fmt.Sprintf("%d", param.Status))
	if param.End >= 0 {
		v.Set("end", fmt.Sprintf("%d", param.End))
	}
	if param.Start >= 0 {
		v.Set("start", fmt.Sprintf("%d", param.Start))
	}

	body := v.Encode()
	reqRes, err := t.doRequest(PGCSumTotalPrice, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for sum total price fail. err: %v", err)
	}
	var result SumTotalPriceResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal hub response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) ListOrderByTpOid(param *ListOrderByTpOidParam) (*ListOrderByTpOidResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("tp_oid_list", param.TpOidList)
	if param.Addr != "" {
		v.Set("addr", param.Addr)
	}
	if param.Status >= 0 {
		v.Set("status", fmt.Sprintf("%d", param.Status))
	}
	if param.AppId > 0 {
		v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(PGCListOrderByTpOid, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for list order by tp_oid_list fail.err:%v", err)
	}
	var result ListOrderByTpOidResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) PGCQueryOrderInfo(oid int64) (*PGCOrderInfoResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("oid", fmt.Sprintf("%d", oid))

	body := v.Encode()
	reqRes, err := t.doRequest(PGCQueryOrderInfo, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for query order info fail.err:%v", err)
	}
	var result PGCOrderInfoResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) IntranetCreateOrder(param *IntranetCreateParam) (*HubCreateResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("seller_addr", param.SellerAddr)
	v.Set("asset_info", param.AssetInfo)
	v.Set("buy_count", fmt.Sprintf("%d", param.BuyCount))
	v.Set("asset_amount", fmt.Sprintf("%d", param.AssetAmount))
	v.Set("origin_price", fmt.Sprintf("%d", param.OriginPrice))
	v.Set("pay_price", fmt.Sprintf("%d", param.PayPrice))

	body := v.Encode()
	reqRes, err := t.doRequest(IntranetCreateOrder, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for intranet create fail. err: %v", err)
	}
	var result HubCreateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal intranet response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) HubRefundAudit(param *RefundAuditParam) (*RefundAuditResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("orderId", fmt.Sprintf("%d", param.BaiduOid))
	v.Set("userId", fmt.Sprintf("%d", param.UserId))
	v.Set("tpOrderId", param.TpOrderId)
	v.Set("refundBatchId", param.RefundBatchId)
	v.Set("applyRefundMoney", fmt.Sprintf("%d", param.ApplyRefundMoney))
	v.Set("rsaSign", param.RsaSign)

	body := v.Encode()
	reqRes, err := t.doRequest(HubRefundAudit, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for refund audit fail. err: %v", err)
	}
	var result RefundAuditResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal hub response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) HubRefundNotify(param *RefundNotifyParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("orderId", fmt.Sprintf("%d", param.BaiduOid))
	v.Set("userId", fmt.Sprintf("%d", param.UserId))
	v.Set("tpOrderId", param.TpOrderId)
	v.Set("refundBatchId", param.RefundBatchId)
	v.Set("refundStatus", fmt.Sprintf("%d", param.RefundStatus))
	v.Set("rsaSign", param.RsaSign)

	body := v.Encode()
	reqRes, err := t.doRequest(HubRefundNotify, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for refund notify fail. err: %v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal hub response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) OrderExecute(param *OrderExecuteParam) (*OrderExecuteResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}

	v := url.Values{}
	v.Set("orderId", param.OrderId)

	body := v.Encode()

	reqRes, err := t.doRequest(BillingOrderExecute, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset trade for order check fail.err:%v", err)
	}
	var result OrderExecuteResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) OrderCheck(param *OrderCheckParam) (*OrderCheckResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("orderId", param.OrderId)

	body := v.Encode()

	reqRes, err := t.doRequest(BillingOrderCheck, body)

	if err != nil {
		return nil, nil, fmt.Errorf("request xasset trade for order check fail.err:%v", err)
	}
	var result OrderCheckResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) QueryAdvance(param *HubAdvanceQueryParam) (*HubAdvanceQueryResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("address", param.Address)
	v.Set("asset_info", param.AssetInfo)
	reqRes, err := t.doRequest(HubAdvanceQuery, v.Encode())
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset trade for query advance fail.err:%v", err)
	}
	var result HubAdvanceQueryResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *TradeClient) GetFreebies(param *HubOrderDetailParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	reqRes, err := t.doRequest(HubGetFreebies, v.Encode())
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset trade for get freebies fail, param: %+v, err: %v", param, err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal account response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// post
func (t *TradeClient) doRequest(api string, data string, opt ...map[string]interface{}) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	connTime, rwTime := time.Duration(ReqConnTimeoutMs), time.Duration(ReqRWTimeoutMs)
	if len(opt) != 0 {
		m := opt[0]
		if cookie, ok := m["Cookie"]; ok {
			header["Cookie"] = fmt.Sprintf("%v", cookie)
		}
		if reqRwTime, ok := m["ReqRwTime"]; ok {
			if reqTime, ok := reqRwTime.(int64); ok {
				rwTime = time.Duration(reqTime)
			}
		}
	}
	resp, err := httpclient.Post(reqUrl, header, connTime, rwTime, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

func (t *TradeClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
