package trade

import (
	"errors"
	"net/http"
)

const (
	ReqConnTimeoutMs       = 1000
	ReqRWTimeoutMs         = 3000
	CreateOrderRwTimeoutMs = int64(5000) // 创建订单超时时间设置长一些
)

const (
	OrderStatusInit    = 0 // 初始化
	OrderStatusPaid    = 1 // 支付成功
	OrderStatusGranted = 3 // 授予成功
	OrderStatusTimeout = 7 // 超时关闭
	OrderStatusRefund  = 9 // 退款成功
)

const (
	TradeQueryOrderList = "/internal/trade/v1/order_list"
	TradeCompleteOrder  = "/internal/trade/v1/complete_order"
	TradeFirstPaidOrder = "/internal/trade/v1/first_paid_order"

	// --------Hub-----------
	HubCreateOrder      = "/internal/trade/hub/v1/create_order"
	HubConfirmOrder     = "/internal/trade/hub/v1/confirm_order"
	HubEditOrder        = "/internal/trade/hub/v1/edit_order"
	HubDetailOrder      = "/internal/trade/hub/v1/order_detail"
	HubListOrder        = "/internal/trade/hub/v1/order_list"
	HubNotifyOrder      = "/internal/trade/hub/v1/order_notify"
	HubKnockOrder       = "/internal/trade/hub/v1/knock_order"
	HubOrderPage        = "/internal/trade/hub/v1/order_page"
	HubRefundAudit      = "/internal/trade/hub/v1/refund_audit"
	HubRefundNotify     = "/internal/trade/hub/v1/refund_notify"
	HubAdvanceQuery     = "/internal/trade/hub/v1/advanceqry"
	HubGetFreebies      = "/internal/trade/hub/v1/get_freebies"
	PGCCountOrder       = "/internal/trade/pgc/v1/count_order"
	PGCSumTotalPrice    = "/internal/trade/pgc/v1/sum_total_price"
	PGCListOrderByTpOid = "/internal/trade/pgc/v1/list_order_bytpoid"
	PGCQueryOrderInfo   = "/internal/trade/pgc/v1/order_detail"
	IntranetCreateOrder = "/intranet/trade/v1/create_order"

	// billing
	BillingOrderExecute = "/internal/trade/v1/order_executor/execute"
	BillingOrderCheck   = "/internal/trade/v1/order_executor/check"
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

type NotifyFromSmartApp struct {
	UserId         int64  `json:"userId"`
	OrderId        int64  `json:"orderId"`
	UnitPrice      int    `json:"unitPrice"`
	Count          int    `json:"count"`
	TotalMoney     int    `json:"totalMoney"`
	PayMoney       int    `json:"payMoney"`
	PromoMoney     int    `json:"promoMoney"`
	HbMoney        int    `json:"hbMoney"`
	HbBalanceMoney int    `json:"hbBalanceMoney"`
	GiftCardMoney  int    `json:"giftCardMoney"`
	DealId         int64  `json:"dealId"`
	PayTime        int    `json:"payTime"`
	PromoDetail    string `json:"promoDetail"`
	PayType        int    `json:"payType"`
	PartnerId      int    `json:"partnerId"`
	Status         int    `json:"status"`
	TpOrderId      string `json:"tpOrderId"`
	ReturnData     string `json:"returnData"`
	RsaSign        string `json:"rsaSign"`
}

func (p *NotifyFromSmartApp) Valid() error {
	if p.UserId <= 0 {
		return errors.New("userId invalid, must be a positive integer")
	}
	if p.OrderId <= 0 {
		return errors.New("orderId invalid, must be a positive integer")
	}
	if p.UnitPrice < 0 {
		return errors.New("unitPrice invalid")
	}
	if p.Count <= 0 {
		return errors.New("count invalid")
	}
	if p.TotalMoney < 0 {
		return errors.New("totalMoney invalid")
	}
	if p.TpOrderId == "" {
		return errors.New("TpOrderId invalid")
	}
	if p.RsaSign == "" {
		return errors.New("RsaSign invalid")
	}
	return nil
}

type NotifyOwnedMallResp struct {
	BaseResp
	Data struct {
		IsConsumed int `json:"isConsumed"`
	} `json:"data"`
}

type OwnedMallOrderItem struct {
	DealId          string `json:"dealId"`
	AppKey          string `json:"appKey"`
	TotalAmount     string `json:"totalAmount"`
	TpOrderId       string `json:"tpOrderId"`
	NotifyUrl       string `json:"notifyUrl"`
	RsaSign         string `json:"rsaSign"`
	SignFieldsRange string `json:"signFieldsRange"`
	CTime           int64  `json:"ctime"`
}

type OrderDetail struct {
	Oid         int64         `json:"oid"`
	AssetId     int64         `json:"asset_id"`
	ShardIds    []int64       `json:"shard_ids"`
	Status      int           `json:"status"`
	RefStatus   int           `json:"refund_status"`
	StoreId     int64         `json:"store_id"`
	StoreName   string        `json:"store_name"`
	BuyCount    int           `json:"buy_count"`
	BuyerAddr   string        `json:"buyer_addr"`
	Title       string        `json:"title"`
	Thumb       []string      `json:"thumb"`
	OriginPrice int           `json:"origin_price"`
	PayPrice    int           `json:"pay_price"`
	SinglePrice int           `json:"single_price"`
	TotalPrice  int           `json:"total_price"`
	PayTime     int64         `json:"pay_time"`
	Ctime       int64         `json:"ctime"`
	Gift        *FreebiesInfo `json:"gift,omitempty"`
	Coupons     []*CouponInfo `json:"coupons,omitempty"`
}

type CouponInfo struct {
	Count int `json:"count"`
	Value int `json:"value"`
}

type ListOrderParam struct {
	Uid    int64  `json:"uid"`
	Status int    `json:"status"`
	Limit  int    `json:"limit"`
	Cursor string `json:"cursor"`
}

func (p *ListOrderParam) Valid() error {
	if p.Uid < 0 {
		return errors.New("uid empty")
	}
	return nil
}

type ListOrderResp struct {
	BaseResp
	Data struct {
		List    []*OrderDetail `json:"list"`
		Cursor  string         `json:"cursor"`
		HasMore int            `json:"has_more"`
	} `json:"data"`
}

type H5OrderItem struct {
	TpOrderId    int64  `json:"oid"`
	OrderInfoUrl string `json:"order_url"`
	H5PayInfo    string `json:"pay_info"`
	TotalAmount  string `json:"total_amount"`
	CTime        int64  `json:"ctime"`
}

type CompleteOrderParam struct {
	Oid int64 `json:"oid"`
	Uid int64 `json:"uid"`
}

func (p *CompleteOrderParam) Valid() error {
	if p.Oid < 1 {
		return errors.New("oid invalid")
	}
	return nil
}

type FirstPaidOrderParam struct {
	AppId int64  `json:"app_id"`
	Addr  string `json:"addr"`
}

type HubCreateOrderParam struct {
	// 标记支付平台, 1001: 百度收银台-小程序 1002: 百度收银台-IOS 1003: 百度收银台-H5 1004: billing h5 2001: 第三方微信支付
	Code int `json:"code"`

	// 订单类型, 0:自营订单 1:PGC商城订单 2:PGC H5订单 3:toB订单
	OrderType int `json:"order_type"`

	// 使用自有收银台时，支付成功后，执行器回调接口，应用方需保证接口幂等
	ExecutorAPI string `json:"executor"`

	// object, 使用自有收银台时，支付成功后，执行器回调时携带参数
	ExecutorData string `json:"executor_data"`

	// 下单时间
	Timestamp int64 `json:"timestamp"`

	// 订单失效时间，对于第三方支付平台的订单，该值被忽略，
	// 对于自有收银台，执行器定期到时时取消订单
	// 当TimeExpire为0时，表示永久不过期
	// TimeExpire为秒偏移量，以create_time时间为准偏移
	TimeExpire int64 `json:"time_expire"`

	// 是否需要分账，0: 不分账，1: 分账
	ProfitSharing int `json:"profit_sharing"`

	//	用户标识，使用自有收银台时，该标识必须为baiduUID
	Uid int64 `json:"uid"`

	// 云上账户id，使用billing时必须设置
	AccountId string `json:"account_id"`

	// object，其余支付参数的序列化值，标记剩余的非通用参数
	Details string `json:"creator_details"`

	AppId     int64  `json:"app_id"`
	StoreId   int64  `json:"store_id"`
	StoreName string `json:"store_name"`
	ActId     int64  `json:"act_id"`
	AssetId   int64  `json:"asset_id"`

	// 分账对象id，使用billing下单必备
	SplitId int64 `json:"split_id"`

	BuyerAddr   string `json:"buyer_addr"`
	SellerAddr  string `json:"seller_addr"`
	AssetInfo   string `json:"asset_info"`
	BuyCount    int    `json:"buy_count"`
	AssetAmount int64  `json:"asset_amount"`
	OriginPrice int    `json:"origin_price"`
	PayPrice    int    `json:"pay_price"`
	ClientType  int    `json:"client_type"`
	Chan        int64  `json:"chan"`
	Scene       int64  `json:"scene"`
	Gift        string `json:"gift"`

	Auth string
}

type HubCreateResp struct {
	BaseResp
	Data struct {
		Code      int    `json:"code"`
		OrderType int    `json:"order_type"`
		Details   string `json:"details"`
		CTime     int64  `json:"ctime"`
	} `json:"data"`
}

type HubConfirmOrderParam struct {
	AppId      int64 `json:"app_id"`
	Code       int   `json:"code"`
	OrderType  int   `json:"order_type"`
	Oid        int64 `json:"oid"`
	ClientType int   `json:"client_type"`

	// object，其余支付参数的序列化值，标记剩余的非通用参数
	Details string `json:"creator_details"`

	Auth string
}

type HubEditOrderParam struct {
	AppId       int64  `json:"app_id"`
	Oid         int64  `json:"oid"`
	Status      int    `json:"status"`
	PayChannel  int    `json:"pay_channel"`
	ThirdOid    string `json:"third_oid"`
	PayInfo     string `json:"pay_info"`
	PayTime     int64  `json:"pay_time"`
	CloseTime   int64  `json:"close_time"`
	CloseReason string `json:"close_reason"`
}

type HubOrderDetailParam struct {
	AppId int64 `json:"app_id"`
	Oid   int64 `json:"oid"`
}

type HubOrderDetailResp struct {
	BaseResp
	Data struct {
		*HubOrderDetail
	} `json:"data"`
}

type HubOrderDetail struct {
	Code        int           `json:"code"`
	OrderType   int           `json:"order_type"`
	Oid         int64         `json:"oid"`
	Rid         int64         `json:"rid"`
	ActId       int64         `json:"act_id"`
	AssetId     int64         `json:"asset_id"`
	ShardIds    []int64       `json:"shard_ids"`
	BuyCount    int           `json:"buy_count"`
	BuyerAddr   string        `json:"buyer_addr"`
	Status      int           `json:"status"`
	RefStatus   int           `json:"refund_status"`
	Title       string        `json:"title"`
	Thumb       []string      `json:"thumb"`
	StoreId     int64         `json:"store_id"`
	StoreName   string        `json:"store_name"`
	OriginPrice int           `json:"origin_price"`
	PayPrice    int           `json:"pay_price"`
	SinglePrice int           `json:"single_price"`
	TotalPrice  int           `json:"total_price"`
	TimeExpire  int64         `json:"time_expire"`
	PayTime     int64         `json:"pay_time"`
	CloseTime   int64         `json:"close_time"`
	Ctime       int64         `json:"ctime"`
	AllowRef    int           `json:"allow_ref"`
	Gift        *FreebiesInfo `json:"gift,omitempty"`
	Coupons     []*CouponInfo `json:"coupons,omitempty"`
}

type HubListOrderParam struct {
	AppId     int64  `json:"app_id"`
	Addr      string `json:"address"`
	Status    int    `json:"status"`
	Cursor    string `json:"cursor"`
	Limit     int    `json:"limit"`
	TimeBegin int64  `json:"time_begin"`
	TimeEnd   int64  `json:"time_end"`
	Mono      int    `json:"monotonicity"`
}

type HubListOrderResp struct {
	BaseResp
	Data struct {
		List    []*HubOrderDetail `json:"list"`
		Cursor  string            `json:"cursor"`
		HasMore int               `json:"has_more"`
	} `json:"data"`
}

type HubOrderPageParam struct {
	AppId     int64  `json:"app_id"`
	Addr      string `json:"address"`
	Status    int    `json:"status"`
	Page      int    `json:"page"`
	Size      int    `json:"size"`
	TimeBegin int64  `json:"time_begin"`
	TimeEnd   int64  `json:"time_end"`
}

type HubOrderPageResp struct {
	BaseResp
	Data struct {
		List  []*HubOrderDetail `json:"list"`
		Total int64             `json:"total"`
	} `json:"data"`
}

type HubKnockOrderParam struct {
	Code      int   `json:"code"`
	OrderType int   `json:"order_type"`
	AppId     int64 `json:"app_id"`
	Oid       int64 `json:"oid"`
}

type HubKnockOrderResp struct {
	BaseResp
	Data struct {
		Code     int                 `json:"code"`
		Status   int                 `json:"status"`
		PayInfo  *OwnedMallOrderItem `json:"pay_info"`
		AssetId  int64               `json:"asset_id"`
		ShardIds []int64             `json:"shard_ids"`
	} `json:"data"`
}

type HubAdvanceQueryParam struct {
	AppId     int64  `json:"app_id"`
	Address   string `json:"address"`
	AssetInfo string `json:"asset_info"`
}

func (p *HubAdvanceQueryParam) Valid() error {
	if p.AppId <= 0 {
		return errors.New("app_id can't be empty")
	}
	if len(p.Address) <= 0 {
		return errors.New("address can't be empty")
	}
	if len(p.AssetInfo) <= 0 {
		return errors.New("asset_info can't be empty")
	}
	return nil
}

type HubAdvanceQueryResp struct {
	BaseResp
	Data struct {
		BTime int64 `json:"buy_time"`
	} `json:"data"`
}

type CountOrderParam struct {
	AppId        int64 `json:"app_id"`
	AssetId      int64 `json:"asset_id"`
	Status       int   `json:"status"`
	ActId        int64 `json:"act_id"`
	RefundStatus int   `json:"refund_status"`
}

type CountOrderResp struct {
	BaseResp
	Data struct {
		Total       int64 `json:"total"`
		BuyCountSum int64 `json:"buy_count_sum"`
		PayPriceSum int64 `json:"pay_price_sum"`
	} `json:"data"`
}

type SumTotalPriceParam struct {
	AppId  int64 `json:"app_id"`
	Status int   `json:"status"`
	End    int64 `json:"end"`
	Start  int64 `json:"start"`
}

type SumTotalPriceResp struct {
	BaseResp
	Data struct {
		TotalCnt   int64 `json:"total_cnt"`
		TotalPrice int64 `json:"total_price"`
	} `json:"data"`
}

type IntranetCreateParam struct {
	AppId       int64  `json:"app_id"`
	ActId       int64  `json:"act_id"`
	AssetId     int64  `json:"asset_id"`
	SellerAddr  string `json:"seller_addr"`
	AssetInfo   string `json:"asset_info"`
	BuyCount    int    `json:"buy_count"`
	AssetAmount int64  `json:"asset_amount"`
	OriginPrice int    `json:"origin_price"`
	PayPrice    int    `json:"pay_price"`
}

type RefundAuditParam struct {
	BaiduOid         int64  `json:"orderId"`
	UserId           int64  `json:"userId"`
	TpOrderId        string `json:"tpOrderId"`
	RefundBatchId    string `json:"refundBatchId"`
	ApplyRefundMoney int    `json:"applyRefundMoney"`
	RsaSign          string `json:"rsaSign"`
}

type RefundAuditResp struct {
	BaseResp
	Data RefundAuditData `json:"data"`
}

type RefundAuditData struct {
	Status    int          `json:"auditStatus"`
	Calculate CalculateObj `json:"calculateRes"`
}

type CalculateObj struct {
	RefundPayMoney int `json:"refundPayMoney"`
}

type RefundNotifyParam struct {
	BaiduOid      int64  `json:"orderId"`
	UserId        int64  `json:"userId"`
	TpOrderId     string `json:"tpOrderId"`
	RefundBatchId string `json:"refundBatchId"`
	RefundStatus  int    `json:"refundStatus"`
	RsaSign       string `json:"rsaSign"`
}

type OrderExecuteParam struct {
	OrderId string `json:"orderId"`
}

func (p *OrderExecuteParam) Valid() bool {
	return len(p.OrderId) != 0
}

type OrderExecuteResp struct {
	BaseResp
	ExecutionStatus string `json:"executionStatus"`
}

type OrderCheckParam = OrderExecuteParam

type OrderCheckResp = OrderExecuteResp

type ListOrderByTpOidParam struct {
	AppId     int64  `json:"app_id"`
	Addr      string `json:"address"`
	Status    int    `json:"status"`
	TpOidList string `json:"tp_oid_list"`
}

func (p *ListOrderByTpOidParam) Valid() error {
	if len(p.TpOidList) < 1 {
		return errors.New("tp_oid_list empty")
	}
	return nil
}

type ListOrderByTpOidResp struct {
	BaseResp
	Data struct {
		List []*OrderInfo `json:"list"`
	} `json:"data"`
}

type PGCOrderInfoResp struct {
	BaseResp
	Data struct {
		OrderInfo
	} `json:"data"`
}

type OrderInfo struct {
	AppId       int64    `json:"app_id"`
	Code        int      `json:"code"`
	OrderType   int      `json:"order_type"`
	Oid         int64    `json:"oid"`
	ThirdOid    string   `json:"third_oid"`
	SplitId     int64    `json:"split_id"`
	Rid         int64    `json:"rid"`
	ActId       int64    `json:"act_id"`
	AssetId     int64    `json:"asset_id"`
	ShardIds    []int64  `json:"shard_ids"`
	BuyerAddr   string   `json:"buyer_addr"`
	BuyCount    int      `json:"buy_count"`
	Status      int      `json:"status"`
	RefStatus   int      `json:"refund_status"`
	Title       string   `json:"title"`
	Thumb       []string `json:"thumb"`
	StoreId     int64    `json:"store_id"`
	StoreName   string   `json:"store_name"`
	OriginPrice int      `json:"origin_price"`
	SinglePrice int      `json:"single_price"`
	PayPrice    int      `json:"pay_price"`
	TimeExpire  int64    `json:"time_expire"`
	PayTime     int64    `json:"pay_time"`
	CloseTime   int64    `json:"close_time"`
	Ctime       int64    `json:"ctime"`
}

type Freebie struct {
	AssetId int64    `json:"asset_id"`
	Thumb   []string `json:"thumb"`
	Title   string   `json:"title"`
	Desc    string   `json:"desc"`
	Count   int      `json:"count"`
}

type FreebiesInfo struct {
	Object        []*Freebie `json:"object"`
	GetExpireTime int64      `json:"gift_vtime"`
	Status        int        `json:"status"`
}

type FreebieObject struct {
	Version       int        `json:"version"`
	GetExpireTime int64      `json:"gift_vtime"`
	List          []*Freebie `json:"list"`
}
