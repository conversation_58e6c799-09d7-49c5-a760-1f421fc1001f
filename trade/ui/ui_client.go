package ui

import (
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/auth"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
	"icode.baidu.com/baidu/blockchain/xasset-golib/trade/store"
	"icode.baidu.com/baidu/blockchain/xasset-golib/trade/trade"
)

type UiClient struct {
	addr     *addr.Addr
	intraApp *IntraApp
}

type IntraApp struct {
	appName  string
	appToken string
}

func NewUiClient(addr *addr.Addr, isPGC ...bool) *UiClient {
	app := &IntraApp{
		appName:  XassetAppName,
		appToken: XassetAppToken,
	}
	if len(isPGC) > 0 && isPGC[0] {
		app = &IntraApp{
			appName:  XassetPGCName,
			appToken: XassetPGCToken,
		}
	}
	return &UiClient{addr, app}
}

func NewUiClientWithApp(addr *addr.Addr, app *IntraApp) *UiClient {
	if app == nil {
		app = XassetApp
	}
	return &UiClient{addr, app}
}

func (t *UiClient) ListShardIndex(param *IndexListShardParam) (*ListShardIndexResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param.Addr == "" {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("addr", param.Addr)
	v.Set("cursor", param.Cursor)
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}
	if param.DaysFilter > 0 {
		v.Set("time", fmt.Sprintf("%d", param.DaysFilter))
	}
	if param.Type >= 0 {
		v.Set("type", fmt.Sprintf("%d", param.Type))
	}
	if param.Title != "" {
		v.Set("title", param.Title)
	}
	if param.AppId > 0 {
		v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	}

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetListShardIdx, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for list shard from idx fail.err:%v", err)
	}
	var result ListShardIndexResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraQueryAsset(assetId int64) (*IntraQueryAssetResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if assetId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetQueryAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for query asset fail.err:%v", err)
	}
	var result IntraQueryAssetResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraListAssetByIds(appId int64, assetIds []int64) (*IntraListAssetResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 {
		return nil, nil, errors.New("param error")
	}
	assetIdsStr, err := json.Marshal(assetIds)
	if err != nil {
		return nil, nil, errors.New("param error, asset_ids json encode failed")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("asset_ids", string(assetIdsStr))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetListAssetByIds, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for list asset by assetids fail.err:%v", err)
	}
	var result IntraListAssetResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraQueryShard(assetId, shardId int64) (*IntraQueryShardResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if assetId < 1 || shardId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	v.Set("shard_id", fmt.Sprintf("%d", shardId))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetQueryShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for query shard fail.err:%v", err)
	}
	var result IntraQueryShardResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraListSdsByAssetIds(appId int64, address string, assetIds []int64) (*IntraListShardsResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || address == "" || len(assetIds) < 1 {
		return nil, nil, errors.New("param error")
	}
	jsAstIds, err := json.Marshal(assetIds)
	if err != nil {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("address", address)
	v.Set("asset_ids", string(jsAstIds))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetListSdsByIds, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for list shard fail.err:%v", err)
	}
	var result IntraListShardsResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraCountSdsByAssetIds(appId int64, address string, assetIds []int64) (*IntraListShardsResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if appId < 1 || address == "" || len(assetIds) < 1 {
		return nil, nil, errors.New("param error")
	}
	jsAstIds, err := json.Marshal(assetIds)
	if err != nil {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("address", address)
	v.Set("asset_ids", string(jsAstIds))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetCountSdsByIds, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for count shard fail.err:%v", err)
	}
	var result IntraListShardsResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraIntGetUInfo(uid int64) (*IntraIntGetUInfoResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if uid < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", uid))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetIntGetUInfo, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for intgetuinfo fail.err:%v", err)
	}
	var result IntraIntGetUInfoResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraOutGetUInfo(uid, appId int64) (*IntraIntGetUInfoResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if uid < 1 || appId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", uid))
	v.Set("app_id", fmt.Sprintf("%d", appId))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetOutGetUInfo, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for outgetuinfo fail.err:%v", err)
	}
	var result IntraIntGetUInfoResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraBindByMnem(uid int64, mnemonic string) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if uid < 1 || mnemonic == "" {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", uid))
	v.Set("mnemonic", mnemonic)

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetBindByMnem, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for bindbymnem fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraListAddr(uid int64) (*IntraListAddrResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if uid < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", uid))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetListAddr, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for listaddr fail.err:%v", err)
	}
	var result IntraListAddrResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraHasAddr(uid int64, addr string) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if uid < 1 || addr == "" {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", uid))
	v.Set("addr", addr)

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetHasAddr, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for hasaddr fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) SyncAccount(param *SyncAccountParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	body, err := json.Marshal(param)
	if err != nil {
		return nil, nil, err
	}
	reqRes, err := t.doRequest(UiApiIntranetSyncAccount, string(body))
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for sync account fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) AccountPreview(param *AccountPreviewParam) (*AccountPreviewResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", strconv.Itoa(param.AppId))
	v.Set("ak", param.AK)
	v.Set("account_id", param.AccountId)
	v.Set("need_mnemonic", strconv.FormatBool(param.NeedMnemonic))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetAccountPreview, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for account preview fail.err:%v", err)
	}
	var result AccountPreviewResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) ListAccount(param *ListAccountsParam) (*ListAccountsResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("page_no", strconv.Itoa(param.PageNo))
	v.Set("page_size", strconv.Itoa(param.PageSize))
	v.Set("account_type", strconv.Itoa(param.AccountType))
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("status", strconv.Itoa(param.Status))
	v.Set("non_stop", strconv.Itoa(param.NonStop))
	v.Set("ak", param.AK)
	v.Set("account_id", param.AccountId)
	v.Set("ban", strconv.Itoa(param.Ban))
	v.Set("pause", strconv.Itoa(param.Pause))
	v.Set("auto_pause", strconv.Itoa(param.AutoPause))
	v.Set("business", param.Business)
	v.Set("pgc_status", strconv.Itoa(param.PgcStatus))
	v.Set("need_total_count", strconv.FormatBool(param.NeedTotalCount))
	query := v.Encode()

	reqRes, err := t.doRequest(UiApiIntranetAccountList, query)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for account list fail.err:%v", err)
	}
	var result ListAccountsResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) QueryCompanyFile(param *QueryCompanyFileParam) (*QueryCompanyFileResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || param.StoreId <= 0 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("store_id", strconv.Itoa(int(param.StoreId)))
	query := v.Encode()

	reqRes, err := t.doRequest(UiApiIntranetCompanyFile, query)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for query company file fail.err:%v", err)
	}
	var result QueryCompanyFileResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraGetAccByAddr(address string, uid int64) (*IntraGetAccByAddrResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if address == "" || uid < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("addr", address)
	v.Set("uid", fmt.Sprintf("%d", uid))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetGetAccByAddr, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for getaccbyaddr fail.err:%v", err)
	}
	var result IntraGetAccByAddrResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraAddAssetTag(assetId int64, tagId int64) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if assetId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	v.Set("tag_id", fmt.Sprintf("%d", tagId))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetAddTag, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for add asset tag fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraDelAssetTag(assetId int64, tagId int64) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if assetId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	v.Set("tag_id", fmt.Sprintf("%d", tagId))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetDelTag, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for del asset tag fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraGetAssetTag(assetId int64) (*IntraGetAssetTagResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if assetId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetAstTag, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for get asset tag fail.err:%v", err)
	}
	var result IntraGetAssetTagResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraListbyTag(addr, cursor string, tagId int64, limit int) (*ListShardbyTagResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if addr == "" || limit < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("tag_id", fmt.Sprintf("%d", tagId))
	v.Set("addr", addr)
	v.Set("cursor", cursor)
	v.Set("limit", fmt.Sprintf("%d", limit))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetListbyTag, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for list shard by tag fail.err:%v", err)
	}
	var result ListShardbyTagResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraListAssetHistory(param *ListAssetHisParam) (*IntraListAssetHistoryResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("shard_id", fmt.Sprintf("%d", param.ShardId))
	v.Set("page", fmt.Sprintf("%d", param.Page))
	v.Set("limit", fmt.Sprintf("%d", param.Limit))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetQueryHistory, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for list asset history fail.err:%v", err)
	}
	var result IntraListAssetHistoryResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraIncrPoints(param *OpPointsParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("op_id", fmt.Sprintf("%d", param.OpId))
	v.Set("points", fmt.Sprintf("%d", param.Points))

	v.Set("address", param.Address)
	v.Set("describe", param.Describe)
	v.Set("ext_info", param.ExtInfo)
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetIncrPoints, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request points to incr points fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal points response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraReducePoints(param *OpPointsParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("op_id", fmt.Sprintf("%d", param.OpId))
	v.Set("points", fmt.Sprintf("%d", param.Points))
	v.Set("op_cate", fmt.Sprintf("%d", param.OpCate))

	v.Set("address", param.Address)
	v.Set("describe", param.Describe)
	v.Set("ext_info", param.ExtInfo)
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetReducePoints, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request points to incr points fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal points response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraGetBalance(param *GetBalanceParam) (*IntraGetBalanceResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}

	v.Set("address", param.Address)
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetGetBalance, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request points to get balance fail.err:%v", err)
	}
	var result IntraGetBalanceResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal points response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraQryOperate(param *QryOperateParam) (*IntraQryOperateResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("op_id", fmt.Sprintf("%d", param.OpId))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetQryOperate, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request points to query operate fail.err:%v", err)
	}
	var result IntraQryOperateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal points response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraListOperate(param *ListOperateParam) (*IntraListOperateResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("address", param.Address)
	v.Set("cursor", param.Cursor)
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetListOperate, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request points to list operate fail.err:%v", err)
	}
	var result IntraListOperateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal points response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) HubKnockOrder(param *HubKnockOrderParam, cred *auth.Credentials) (*trade.HubKnockOrderResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("code", fmt.Sprintf("%d", param.Code))
	v.Set("order_type", fmt.Sprintf("%d", param.OrderType))
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("oid", fmt.Sprintf("%d", param.Oid))
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiKnockOrder, body, []*auth.Credentials{cred}...)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for hub knock order fail.err:%v", err)
	}
	var result trade.HubKnockOrderResp

	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) HubQueryAdvance(act_id int64, asset_id int64, address string, cred *auth.Credentials) (*QueryAdvanceResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", act_id))
	v.Set("asset_id", fmt.Sprintf("%d", asset_id))
	v.Set("address", address)
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiQueryAdvance, body, []*auth.Credentials{cred}...)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for query advance fail.err:%v", err)
	}
	var result QueryAdvanceResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraPushStore(storeId, opType int) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if storeId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("store_id", fmt.Sprintf("%d", storeId))
	v.Set("op_type", fmt.Sprintf("%d", opType))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetPushStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for push store fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraPushAct(actId int64, opType int) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if actId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", actId))
	v.Set("op_type", fmt.Sprintf("%d", opType))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetPushAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for push act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraListActByStore(storeId, limit int, cursor string) (*store.ListActByStoreResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if storeId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("store_id", fmt.Sprintf("%d", storeId))
	v.Set("cursor", cursor)
	v.Set("limit", fmt.Sprintf("%d", limit))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetListActByStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for list act by store fail.err:%v", err)
	}
	var result store.ListActByStoreResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraListAstByAct(actId int64) (*store.QueryActResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if actId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", actId))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetListAstByAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for list ast by act fail.err:%v", err)
	}
	var result store.QueryActResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraQueryStore(storeId int) (*store.QueryStoreResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if storeId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("store_id", fmt.Sprintf("%d", storeId))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetQueryStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for query store fail.err:%v", err)
	}
	var result store.QueryStoreResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraQueryStoreAst(actId, assetId int64) (*store.QueryAstResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if actId < 1 || assetId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", actId))
	v.Set("asset_id", fmt.Sprintf("%d", assetId))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetQueryStoreAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for query store asset fail.err:%v", err)
	}
	var result store.QueryAstResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraBanStore(storeId, opType int) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if storeId < 1 || opType < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("store_id", fmt.Sprintf("%d", storeId))
	v.Set("op_type", fmt.Sprintf("%d", opType))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetBanStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for ban store fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraBanAct(actId int64, opType int) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if actId < 1 || opType < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", actId))
	v.Set("op_type", fmt.Sprintf("%d", opType))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetBanAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for ban act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraBanAst(assetId int64) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if assetId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetBanAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for ban asset fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraListActByAst(assetId int64, mono ...int) (*store.ListActByAstResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if assetId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", assetId))
	if len(mono) > 0 {
		v.Set("mono", fmt.Sprintf("%d", mono[0]))
	}

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetListActByAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for list act by ast fail.err:%v", err)
	}
	var result store.ListActByAstResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraCreateCollect(param *CreateCollectParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("addr", param.Addr)
	v.Set("type", fmt.Sprintf("%d", param.Type))
	v.Set("title", param.Title)
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetCreateCollect, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for create collect fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraDeleteCollect(param *DelCollectParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("addr", param.Addr)
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetDeleteCollect, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for delete collect fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraSetCollectTitle(param *UpdateCollectParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("addr", param.Addr)
	v.Set("title", param.Title)
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetSetCollectTitle, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for set collect title fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraSetCollectCover(param *UpdateCollectParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("addr", param.Addr)
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("shard_id", fmt.Sprintf("%d", param.ShardId))
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetSetCollectCover, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for set collect cover fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraQueryCollect(param *QueryCollectParam) (*QueryCollectResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("addr", param.Addr)
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetQueryCollect, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset horae for query collect fail.err:%v", err)
	}
	var result QueryCollectResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraListCollect(param *ListCollectParam) (*ListCollectResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("addr", param.Addr)
	v.Set("cursor", param.Cursor)
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	if param.DaysFilter > 0 {
		v.Set("time", fmt.Sprintf("%d", param.DaysFilter))
	}
	if param.Type >= 0 {
		v.Set("type", fmt.Sprintf("%d", param.Type))
	}
	if len(param.Types) > 0 {
		v.Set("types", param.Types)
	}
	v.Set("sort", fmt.Sprintf("%d", param.Sort))
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetListCollect, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for list collect fail.err:%v", err)
	}
	var result ListCollectResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraCountCollect(param *CountCollectParam) (*CountCollectResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("addr", param.Addr)
	if len(param.Types) > 0 {
		v.Set("types", param.Types)
	}
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetCountCollect, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for count collect fail.err:%v", err)
	}
	var result CountCollectResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraAddCollectAssets(param *UpdateCollectAssetParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("addr", param.Addr)
	v.Set("assets", param.Assets)
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetAddCollectAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for add collect assets fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraDelCollectAssets(param *UpdateCollectAssetParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("addr", param.Addr)
	v.Set("assets", param.Assets)
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetDelCollectAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for delete collect assets fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraListCollectAssets(param *ListCollectAssetParam) (*ListCollectAssetResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("sort", fmt.Sprintf("%d", param.Sort))
	v.Set("cursor", param.Cursor)
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetListCollectAsset, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for list collect assets fail.err:%v", err)
	}
	var result ListCollectAssetResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraListCollectAggAssets(param *ListCollectAggParam) (*ListCollectAggResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil || !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("sort", fmt.Sprintf("%d", param.Sort))
	v.Set("collect_id", fmt.Sprintf("%d", param.CollectId))
	v.Set("cursor", param.Cursor)
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetListCollectAggAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for list collect agg asset fail.err:%v", err)
	}
	var result ListCollectAggResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraCompleteOrder(oid, uid int64) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if oid < 1 || uid < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("oid", fmt.Sprintf("%d", oid))
	v.Set("uid", fmt.Sprintf("%d", uid))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetCompletOrder, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for complete order fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraFirstPaidOrder(addr string) (*trade.HubOrderDetailResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("addr", addr)

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetFirstPaidOrder, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for query first paid order fail. err: %v", err)
	}
	var result trade.HubOrderDetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraListOrderByTpOid(param *ListOrderByTpOidParam) (*ListOrderByTpOidResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("tp_oid_list", param.TpOidList)
	if param.Addr != "" {
		v.Set("addr", param.Addr)
	}
	if param.Status >= 0 {
		v.Set("status", fmt.Sprintf("%d", param.Status))
	}
	if param.AppId > 0 {
		v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	}
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetListOrderByTpOid, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for list order by tp_oid list fail. err: %v", err)
	}
	var result ListOrderByTpOidResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraQueryOrderInfo(oid int64) (*QueryOrderInfoResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("oid", fmt.Sprintf("%d", oid))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetQueryOrderInfo, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for query order info fail. err: %v", err)
	}
	var result QueryOrderInfoResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraListRefundByAddr(appId int64, address, cursor string, limit int) (*ListRefundResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("address", address)
	v.Set("cursor", cursor)
	v.Set("limit", fmt.Sprintf("%d", limit))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntraListRefund, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request ui for list refund fail. err: %v", err)
	}
	var result ListRefundResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraComposeShard(param *ComposeParam) (*ComposeShardResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("strg_no", fmt.Sprintf("%d", param.StrgNo))
	v.Set("nonce", fmt.Sprintf("%d", param.Nonce))
	v.Set("addr", param.Addr)
	v.Set("pkey", param.Pkey)
	v.Set("sign", param.Sign)
	v.Set("uaddr", param.UAddr)
	v.Set("upkey", param.UPkey)
	v.Set("ast_list", param.AstList)
	v.Set("token", param.Token)
	v.Set("user_id", fmt.Sprintf("%d", param.UserId))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetComposeShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request ui for compose shard fail. err: %v", err)
	}
	var result ComposeShardResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraBatchFreezeShard(param *BatchFreezeShardParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("addr", param.Addr)
	v.Set("sds_list", param.Shards)

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetBatchFreezeShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request ui for batch freeze shard fail. err: %v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraBatchUnfreezeShard(param *BatchFreezeShardParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("addr", param.Addr)
	v.Set("sds_list", param.Shards)

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetBatchUnfreezeShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request ui for batch unfreeze shard fail. err: %v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraBatchCumfreShard(param *BatchCumfreShardParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("addr", param.Addr)
	v.Set("pkey", param.Pkey)
	v.Set("sds_list", param.Shards)

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetBatchCumfreShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request ui for batch consume freeze shard fail. err: %v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) IntraDivideShard(param *ComposeParam) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("nonce", fmt.Sprintf("%d", param.Nonce))
	v.Set("addr", param.Addr)
	v.Set("pkey", param.Pkey)
	v.Set("sign", param.Sign)
	v.Set("uaddr", param.UAddr)
	v.Set("upkey", param.UPkey)
	v.Set("ast_list", param.AstList)
	v.Set("token", param.Token)

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetDivideShard, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request ui for divide shard fail. err: %v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) SeparateAuditDetail(splitName int64) (*AuditSeparateDetailResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if splitName <= 0 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("splitName", fmt.Sprintf("%d", splitName))
	body := v.Encode()
	reqRes, err := t.doRequest(AuditDetailApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for ui AuditDetailApi fail.err:%v", err)
	}
	var result AuditSeparateDetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) AuditListSeparate(param *ListAuditSeparateParam) (*ListSeparateResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("page", fmt.Sprintf("%d", param.Page))
	v.Set("size", fmt.Sprintf("%d", param.Size))
	body := v.Encode()
	reqRes, err := t.doRequest(AuditListSeparateApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for ui AuditListSeparateApi fail.err:%v", err)
	}
	var result ListSeparateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) AuditQuerySeparate(param *AuditFilterSeparateParam) (*ListSeparateResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("companyName", param.CompanyName)
	v.Set("page", fmt.Sprintf("%d", param.Page))
	v.Set("size", fmt.Sprintf("%d", param.Size))
	body := v.Encode()
	reqRes, err := t.doRequest(AuditQuerySeparateApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for ui AuditQuerySeparateApi fail.err:%v", err)
	}
	var result ListSeparateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) UpdateSeparateRadio(param *UpdateSeparateRadioParam) (*UpdateSeparateRadioResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("splitName", fmt.Sprintf("%d", param.SplitName))
	v.Set("cmsContractId", param.CmsContractId)
	v.Set("separateRatio", fmt.Sprintf("%d", param.SeparateRatio))
	v.Set("commissionSeparateRatio", fmt.Sprintf("%d", param.CommissionSeparateRatio))
	body := v.Encode()
	reqRes, err := t.doRequest(AuditUpdateSeparateRatioApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for ui AuditUpdateSeparateRatioApi fail.err:%v", err)
	}
	var result UpdateSeparateRadioResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) AuditSeparate(param *AuditSeparateParam) (*AuditCreateSeparateAccountResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("splitName", fmt.Sprintf("%d", param.SplitName))
	v.Set("cmsContractId", param.CmsContractId)
	v.Set("skuId", param.SkuId)
	v.Set("success", fmt.Sprintf("%d", param.Success))
	v.Set("failReason", param.FailReason)
	v.Set("separateRatio", fmt.Sprintf("%d", param.SeparateRatio))
	v.Set("commissionSeparateRatio", fmt.Sprintf("%d", param.CommissionSeparateRatio))
	v.Set("commissionRate", fmt.Sprintf("%d", param.CommissionRate))
	body := v.Encode()
	reqRes, err := t.doRequest(AuditSeparateApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request trade for ui AuditSeparateApi fail.err:%v", err)
	}
	var result AuditCreateSeparateAccountResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal trade response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) CountNearExpire(param *CountNearExpireParam) (*CountNearExpireResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("addr", param.Addr)
	v.Set("asset_cate", fmt.Sprintf("%d", param.AssetCate))
	v.Set("expire_time", fmt.Sprintf("%d", param.ExpireTime))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetCountNearExpire, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request horae for ui CountNearExpire fail.err:%v", err)
	}
	var result CountNearExpireResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) ListShardsByCate(param *ListShardsByCateParam) (*ListShardsByCateResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("addr", param.Addr)
	v.Set("asset_cate", fmt.Sprintf("%d", param.AssetCate))
	v.Set("sort_key", fmt.Sprintf("%d", param.SortKey))
	v.Set("cursor", param.Cursor)
	if param.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", param.Limit))
	}

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetListShardsByCate, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request horae for ui ListShardsByCate fail.err:%v", err)
	}
	var result ListShardsByCateResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal horae response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) GetFreebies(appId int64, oid int64) (*BaseResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}

	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", appId))
	v.Set("oid", fmt.Sprintf("%d", oid))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiGetFreebies, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request ui for get freebies fail. err: %v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// IntranetOrderListByUid 为历史兼容逻辑, wallet使用
func (t *UiClient) IntranetOrderListByUid(param *trade.ListOrderParam) (*trade.ListOrderResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("status", fmt.Sprintf("%d", param.Status))
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	v.Set("cursor", param.Cursor)
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiOrderListByUid, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request ui for query order list by uid fail. err: %v", err)
	}
	var result trade.ListOrderResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// IntranetOrderList 和sdk的QueryOrderList相同，只是会返回sdk不需要的字段
func (t *UiClient) IntranetOrderList(param *trade.HubListOrderParam, cred *auth.Credentials) (*trade.HubListOrderResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("address", param.Addr)
	v.Set("status", fmt.Sprintf("%d", param.Status))
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	v.Set("cursor", param.Cursor)
	v.Set("monotonicity", fmt.Sprintf("%d", param.Mono))
	v.Set("time_begin", fmt.Sprintf("%d", param.TimeBegin))
	v.Set("time_end", fmt.Sprintf("%d", param.TimeEnd))

	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetOrderList, body, cred)
	if err != nil {
		return nil, nil, fmt.Errorf("request ui for query order list fail. err: %v", err)
	}
	var result trade.HubListOrderResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// IntranetOrderDetail 和sdk的QueryOrderDetail相同，只是会返回sdk不需要的字段
func (t *UiClient) IntranetOrderDetail(oid int64, cred *auth.Credentials) (*IntranetOrderDetailResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if oid <= 0 {
		return nil, nil, errors.New("oid is invalid")
	}
	v := url.Values{}
	v.Set("oid", fmt.Sprintf("%d", oid))
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetOrderDetail, body, cred)
	if err != nil {
		return nil, nil, fmt.Errorf("request ui for query order detail fail. oid: %d, err: %v", oid, err)
	}
	var result IntranetOrderDetailResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail. url: %s, http_code: %d "+
			",resp: %s, err: %v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) CountAggrShards(param *CountAssetsTypeParam) (*CountAssetsTypeResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("addr", param.Addr)
	v.Set("type", fmt.Sprintf("%d", param.Type))
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetCountAggrShards, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for countsds by type fail.err:%v", err)
	}
	var result CountAssetsTypeResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) ListAggrAst(param *AggrAstListParam) (*AggrAstListResp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("app_id", fmt.Sprintf("%d", param.AppId))
	v.Set("addr", param.Addr)
	v.Set("title", param.Title)
	v.Set("type", fmt.Sprintf("%d", param.Type))
	v.Set("mono", fmt.Sprintf("%d", param.MonoIncr))
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	v.Set("cursor", param.Cursor)
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetListAggrAst, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for listaggrast fail.err:%v", err)
	}
	var result AggrAstListResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *UiClient) ListSdsByAstV3(param *ListSdsByAstV3Param) (*ListSdsByAstV3Resp, *RequestRes, error) {
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if err := param.Valid(); err != nil {
		return nil, nil, err
	}
	v := url.Values{}
	v.Set("asset_id", fmt.Sprintf("%d", param.AssetId))
	v.Set("addr", param.Addr)
	v.Set("mono", fmt.Sprintf("%d", param.MonoIncr))
	v.Set("limit", fmt.Sprintf("%d", param.Limit))
	v.Set("cursor", param.Cursor)
	body := v.Encode()
	reqRes, err := t.doRequest(UiApiIntranetListSdsByAstV3, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xasset ui for list_shards_by_asset_v3 by type fail.err:%v", err)
	}
	var result ListSdsByAstV3Resp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal ui response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// post
func (t *UiClient) doRequest(api string, data string, cred ...*auth.Credentials) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	header["App"] = t.intraApp.appName
	header["Timestamp"] = timestamp
	header["Authorization"] = t.genSign(t.intraApp.appName, timestamp, t.intraApp.appToken)

	// wallet调用ui外部接口时，增加简单鉴权验证
	if len(cred) > 0 {
		req, _ := http.NewRequest("POST", api, strings.NewReader(data))
		req.Header.Add("host", hostInfo.Host)
		auth, err := auth.Sign(req, cred[0], &auth.SignOptions{
			HeadersToSign: nil,
			Timestamp:     0,
			ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS,
		})
		if err != nil {
			return nil, fmt.Errorf("sign ui request fail.url:%s err:%v", api, err)
		}
		header["Authorization"] = auth
	}

	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

func (t *UiClient) genSign(appName, timestamp, token string) string {
	h := md5.New()
	io.WriteString(h, fmt.Sprintf("%s%s%s", appName, timestamp, token))
	sign := fmt.Sprintf("%x", h.Sum(nil))
	return sign
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *UiClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
