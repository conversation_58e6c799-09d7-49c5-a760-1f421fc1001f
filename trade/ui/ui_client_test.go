package ui

import (
	"flag"
	"fmt"
	"strconv"
	"sync"
	"testing"
	"time"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
)

var hosts string = "*************:8360"
var assetId int64 = 1946763876139046056
var shardId int64 = 8010631470723856800

func TestIntraQueryAsset(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewUiClient(a).IntraQueryAsset(assetId)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestIntraQueryShard(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewUiClient(a).IntraQueryShard(assetId, shardId)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestListShardIndex(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	p := &IndexListShardParam{
		Addr:  "XgVpudDKZC5TeZHr3KTwMyHKoM2dtF7fT",
		Limit: 20,
		Type:  -1,
	}
	resp, reqInfo, err := NewUiClient(a).ListShardIndex(p)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestIntraListAddr(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewUiClient(a).IntraListAddr(0)
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

var tagId int64 = 8191
var addrTag string = "eKMguNehvDcNcfKbPNhUV6evAphjrBqfR"

func TestAddDelGetTag(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewUiClient(a).IntraDelAssetTag(assetId, tagId)
	if err == nil && resp.Errno == 0 {
		// delete an already exist item
		fmt.Println("delete fault, please alter asset_id or tag_id in test unit")
		resp, reqInfo, err := NewUiClient(a).IntraAddAssetTag(assetId, tagId)
		if err == nil && resp.Errno == 0 {
			return
		}
		fmt.Println(resp, reqInfo)
		fmt.Println("fail to add agin to database, check and add it manually")
		return
	}
	//expect fail here
	fmt.Println(resp, reqInfo)

	resp, reqInfo, err = NewUiClient(a).IntraAddAssetTag(assetId, tagId)
	fmt.Println(resp, reqInfo)
	if err != nil || resp.Errno != 0 {
		return
	}

	lresp, reqInfo, err := NewUiClient(a).IntraGetAssetTag(assetId)
	fmt.Println(lresp, reqInfo)
	if err != nil || lresp.Errno != 0 || len(lresp.List) != 1 {
		return
	}

	resp, reqInfo, err = NewUiClient(a).IntraDelAssetTag(assetId, tagId)
	fmt.Println(resp, reqInfo)
	if err != nil || resp.Errno != 0 {
		return
	}
}

func TestListbyTag(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewUiClient(a).IntraListbyTag(addrTag, "", 0, 10)
	if err != nil {
		fmt.Println(resp, reqInfo)
	}
	fmt.Println(resp, reqInfo)
}

func TestUiClient_IncrPoints(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewUiClient(a).IntraIncrPoints(&OpPointsParam{
		Address:  "abcd",
		OpId:     1234,
		Points:   10,
		Describe: "test",
		ExtInfo:  "",
	})
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestUiClient_ReducePoints(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewUiClient(a).IntraIncrPoints(&OpPointsParam{
		Address:  "abcd",
		OpId:     2000,
		Points:   10,
		Describe: "test",
		ExtInfo:  "",
	})
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestUiClient_GetBalance(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewUiClient(a).IntraGetBalance(&GetBalanceParam{Address: "abcd"})
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

// listbytag 流量冲击测试
func TestParaReqListbyTag(t *testing.T) {
	var routines int = 10

	if !flag.Parsed() {
		flag.Parse()
	}
	argList := flag.Args()
	for _, arg := range argList {
		ar, err := strconv.Atoi(arg)
		if err == nil {
			routines = ar
			break
		}
	}

	if routines < 1 || routines > 500 {
		fmt.Println("bad requests num: ", routines)
	}

	var wg sync.WaitGroup

	for i := 0; i < routines; i++ {
		wg.Add(1)
		go func(idx int) {
			defer wg.Done()
			start := time.Now()
			a, _ := addr.NewAddrByHost([]string{hosts})
			uc := NewUiClient(a)
			resp, reqInfo, err := uc.IntraListbyTag(addrTag, "", 1, 100)
			if err != nil {
				fmt.Println(idx, " routine finish: ", resp, reqInfo)
				return
			}
			cost := time.Since(start)
			fmt.Println(idx, " routine finish: get ", len(resp.List), " items, cost: ", cost, " ", time.Now().Unix())
		}(i)
	}

	wg.Wait()
	return
}
func TestUiClient_ListOperate(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewUiClient(a).IntraListOperate(&ListOperateParam{
		Address: "abcd",
	})
	if err != nil {
		fmt.Println(err, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestUiClient_QryOperate(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})

	resp, reqInfo, err := NewUiClient(a).IntraQryOperate(&QryOperateParam{OpId: 1234})
	if err != nil {
		fmt.Println(err, reqInfo)
	}
	fmt.Println(resp, reqInfo)
}

func TestListSdsByAssetIds(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewUiClient(a).IntraListSdsByAssetIds(300100,
		"dpr8vv841sVRT1aEA9riRTtgLpx7RxPPy",
		[]int64{
			1468947535008836,
			18023744358618180,
			49813717894075460,
			139467552425546820,
		},
	)
	if err != nil {
		fmt.Println(resp, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}

func TestCountSdsByAssetIds(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewUiClient(a).IntraCountSdsByAssetIds(300100,
		"dpr8vv841sVRT1aEA9riRTtgLpx7RxPPy",
		[]int64{
			66795331387692100,
			141102526216049732,
		},
	)
	if err != nil {
		fmt.Println(resp, reqInfo)
		return
	}
	for _, v := range resp.List {
		fmt.Printf("asset_id:%d title:%s thumb:%v cnt:%d\n", v.AssetId, v.AssetInfo.Title, v.AssetInfo.Thumb, v.Cnt)
	}
}

func TestIntranetCompleteOrder(t *testing.T) {
	a, _ := addr.NewAddrByHost([]string{hosts})
	resp, reqInfo, err := NewUiClient(a).IntraCompleteOrder(123, 456)
	if err != nil {
		fmt.Println(resp, reqInfo)
		return
	}
	fmt.Println(resp, reqInfo)
}
