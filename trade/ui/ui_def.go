package ui

import (
	"errors"
	"net/http"

	"icode.baidu.com/baidu/blockchain/xasset-golib/trade/horae"
	"icode.baidu.com/baidu/blockchain/xasset-golib/trade/trade"
)

const (
	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000
)

const (
	XassetAppName  = "baidu_xasset"
	XassetAppToken = "d7bba086f5ac7eb0a60b6480687e3006"

	XassetPGCName  = "baidu_xassetpgc"
	XassetPGCToken = "73305379646b653a48a600d6cbd48521"

	XassetDevName  = "baidu_astdev"
	XassetDevToken = "a91510b83af2fab37a4f10fe0550f169"

	// 用于对外网调用增加内网鉴权
	//  比如 pgc 平台从外网调用商城 saas，需要识别是 pgc 平台发起的请求
	IntraExtraAuthHeader = "Xasset-Extra-Auth"
	IntraExtraTMHeader   = "Xasset-Extra-Timestamp"
)

var (
	XassetApp = &IntraApp{
		appName:  XassetAppName,
		appToken: XassetAppToken,
	}

	XassetPGC = &IntraApp{
		appName:  XassetPGCName,
		appToken: XassetPGCToken,
	}
	XassetDev = &IntraApp{
		appName:  XassetDevName,
		appToken: XassetDevToken,
	}
)

const (
	UiApiIntranetListShardIdx       = "/intranet/horae/v1/listidxsds"
	UiApiIntranetQueryAsset         = "/intranet/horae/v1/query"
	UiApiIntranetListAssetByIds     = "/intranet/horae/v1/listbyids"
	UiApiIntranetQueryShard         = "/intranet/horae/v1/querysds"
	UiApiIntranetQueryHistory       = "/intranet/horae/v1/history"
	UiApiIntranetComposeShard       = "/intranet/horae/v1/compose"
	UiApiIntranetBatchFreezeShard   = "/intranet/horae/v1/batchfreezesds"
	UiApiIntranetBatchUnfreezeShard = "/intranet/horae/v1/batchunfreezesds"
	UiApiIntranetBatchCumfreShard   = "/intranet/horae/v1/batchcumfresds"
	UiApiIntranetListShardsByCate   = "/intranet/horae/v1/listsdsbycate"
	UiApiIntranetCountNearExpire    = "/intranet/horae/v1/countnearexpire"
	UiApiIntranetCountAggrShards    = "/intranet/horae/v1/countsds"
	UiApiIntranetListAggrAst        = "/intranet/horae/v1/listaggrast"
	UiApiIntranetListSdsByAstV3     = "/intranet/horae/v1/listsdsbyastv3"

	UiApiIntranetDivideShard    = "/intranet/horae/v1/divide"
	UiApiIntranetIntGetUInfo    = "/intranet/did/v1/intgetuinfo"
	UiApiIntranetOutGetUInfo    = "/intranet/did/v1/outgetuinfo"
	UiApiIntranetGetAccByAddr   = "/intranet/did/v1/getmnem"
	UiApiIntranetBindByMnem     = "/intranet/did/v1/bindbymnem"
	UiApiIntranetListAddr       = "/intranet/did/v1/listaddr"
	UiApiIntranetHasAddr        = "/intranet/did/v1/hasaddr"
	UiApiIntranetSyncAccount    = "/intranet/account/v1/account/sync"
	UiApiIntranetAccountPreview = "/intranet/account/v1/account/preview"
	UiApiIntranetAccountList    = "/intranet/account/v1/account/list"
	UiApiIntranetCompanyFile    = "/intranet/account/v1/account/companyfile"
	UiApiIntranetListSdsByIds   = "/intranet/horae/v1/listsdsbyastids"
	UiApiIntranetCountSdsByIds  = "/intranet/horae/v1/countsdsbyastids"
	UiApiIntranetAddTag         = "/intranet/tag/v1/addtag"
	UiApiIntranetDelTag         = "/intranet/tag/v1/deltag"
	UiApiIntranetAstTag         = "/intranet/tag/v1/asttag"
	UiApiIntranetListbyTag      = "/intranet/tag/v1/listshrdsbytag"
	UiApiIntranetPushStore      = "/intranet/store/v1/push"
	UiApiIntranetPushAct        = "/intranet/store/v1/pushact"
	UiApiIntranetListActByStore = "/intranet/store/v1/listactbystore"
	UiApiIntranetListAstByAct   = "/intranet/store/v1/listastbyact"
	UiApiIntranetQueryStore     = "/intranet/store/v1/querystore"
	UiApiIntranetQueryStoreAst  = "/intranet/store/v1/queryast"
	UiApiIntranetBanStore       = "/intranet/store/v1/ban"
	UiApiIntranetBanAct         = "/intranet/store/v1/banact"
	UiApiIntranetBanAst         = "/intranet/store/v1/banast"
	UiApiIntranetListActByAst   = "/intranet/store/v1/listactbyast"

	UiApiIntranetIncrPoints   = "/intranet/point/v1/increase"
	UiApiIntranetReducePoints = "/intranet/point/v1/reduce"
	UiApiIntranetGetBalance   = "/intranet/point/v1/balance"
	UiApiIntranetListOperate  = "/intranet/point/v1/listoperate"
	UiApiIntranetQryOperate   = "/intranet/point/v1/qryoperate"

	UiApiIntranetCreateCollect     = "/intranet/collect/v1/create"
	UiApiIntranetDeleteCollect     = "/intranet/collect/v1/delete"
	UiApiIntranetSetCollectTitle   = "/intranet/collect/v1/settitle"
	UiApiIntranetSetCollectCover   = "/intranet/collect/v1/setcover"
	UiApiIntranetQueryCollect      = "/intranet/collect/v1/query"
	UiApiIntranetListCollect       = "/intranet/collect/v1/listbyaddr"
	UiApiIntranetCountCollect      = "/intranet/collect/v1/countbyaddr"
	UiApiIntranetAddCollectAsset   = "/intranet/collect/v1/addast"
	UiApiIntranetDelCollectAsset   = "/intranet/collect/v1/delast"
	UiApiIntranetListCollectAsset  = "/intranet/collect/v1/listastbycid"
	UiApiIntranetListCollectAggAst = "/intranet/collect/v1/listaggastbycid"

	// knock为自营商城的订单特殊逻辑，暂时不迁移到sdk
	UiApiKnockOrder               = "/xasset/trade/v1/knock_order"
	UiApiIntranetCompletOrder     = "/intranet/trade/v1/complete_order"
	UiApiIntranetFirstPaidOrder   = "/intranet/trade/v1/first_paid_order"
	UiApiQueryAdvance             = "/xasset/trade/v1/advanceqry"
	UiApiIntraListRefund          = "/intranet/trade/v1/listrefundbyaddr"
	UiApiIntranetListOrderByTpOid = "/intranet/trade/v1/list_order_bytpoid"
	UiApiIntranetQueryOrderInfo   = "/intranet/trade/v1/query_order_info"
	UiApiGetFreebies              = "/intranet/trade/v1/get_freebies"
	UiApiOrderListByUid           = "/intranet/trade/v1/list_order"

	// 部分目前星际口袋专属逻辑暂时使用golib
	UiApiIntranetOrderList   = "/xasset/trade/v1/order_list"
	UiApiIntranetOrderDetail = "/xasset/trade/v1/order_detail"

	AuditSeparateApi            = "/intranet/consolehub/v1/separate/audit"
	AuditDetailApi              = "/intranet/consolehub/v1/separate/audit_detail"
	AuditListSeparateApi        = "/intranet/consolehub/v1/separate/audit_list"
	AuditQuerySeparateApi       = "/intranet/consolehub/v1/separate/audit_query"
	AuditUpdateSeparateRatioApi = "/intranet/consolehub/v1/separate/update_ratio"
)

type IndexListShardParam struct {
	AppId      int64  `json:"app_id"`
	Addr       string `json:"addr"`
	Cursor     string `json:"cursor"`
	Limit      int    `json:"limit"`
	Type       int    `json:"type"`
	DaysFilter int64  `json:"time"`
	Title      string `json:"title"`
}

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

type ListShardIndexResp struct {
	BaseResp
	List    []*ListShardIndexObject `json:"list"`
	HasMore int                     `json:"has_more"`
	Cursor  string                  `json:"cursor"`
}

type ListShardIndexObject struct {
	AssetId int64            `json:"asset_id"`
	ShardId int64            `json:"shard_id"`
	Title   string           `json:"title"`
	Thumb   []horae.ThumbMap `json:"thumb"`
	Ctime   int64            `json:"ctime"`
	Type    int              `json:"type"`
	Status  int              `json:"status"`
}

type IntraQueryAssetResp struct {
	BaseResp
	Meta *AssetObject `json:"meta"`
}

type AssetObject struct {
	AssetId    int64            `json:"asset_id,omitempty"`
	AssetCate  int              `json:"asset_cate"`
	Title      string           `json:"title"`
	Thumb      []horae.ThumbMap `json:"thumb"`
	ShortDesc  string           `json:"short_desc"`
	LongDesc   string           `json:"long_desc,omitempty"`
	ImgDesc    []string         `json:"img_desc,omitempty"`
	AssetUrl   []string         `json:"asset_url,omitempty"`
	AssetExt   string           `json:"asset_ext,omitempty"`
	Price      int64            `json:"price,omitempty"`
	Amount     int              `json:"amount,omitempty"`
	Status     int              `json:"status,omitempty"`
	GroupId    int64            `json:"group_id,omitempty"`
	TxId       string           `json:"tx_id"`
	CreateAddr string           `json:"create_addr"`
	ProcScript string           `json:"proc_script,omitempty"`
	Version    int64            `json:"version"`
	ViewType   int              `json:"view_type"`
	Value      int              `json:"value,omitempty"`
}

type IntraListAssetResp struct {
	BaseResp
	List []*AssetObject `json:"list"`
}

type IntraQueryShardResp struct {
	BaseResp
	Meta *ShardObject `json:"meta"`
}

type ShardObject struct {
	AssetId    int64        `json:"asset_id,omitempty"`
	ShardId    int64        `json:"shard_id"`
	OwnerAddr  string       `json:"owner_addr"`
	Price      int64        `json:"price,omitempty"`
	Status     int          `json:"status,omitempty"`
	GroupId    int64        `json:"group_id,omitempty"`
	Cnt        int          `json:"cnt,omitempty"`
	TxId       string       `json:"tx_id"`
	AssetInfo  *AssetObject `json:"asset_info"`
	Ctime      int64        `json:"ctime"`
	Mtime      int64        `json:"mtime"`
	Version    int64        `json:"version"`
	ExpireTime int64        `json:"expire_time"`
}

type IntraListShardsResp struct {
	BaseResp
	List []*ShardObject `json:"list"`
}

type IntraIntGetUInfoResp struct {
	BaseResp
	Address string `json:"address"`
	IsNew   int    `json:"is_new"`
}

type IntraListAddrResp struct {
	BaseResp
	List   []string     `json:"list"`
	AgList []*AddrGroup `json:"ag_list"`
}

type AddrGroup struct {
	Addr    string `json:"addr"`
	GroupId int64  `json:"group_id"`
}

type SyncAccountParam struct {
	EncryptedAcc []byte
	Nonce        string
}

func (p *SyncAccountParam) Valid() bool {
	return len(p.EncryptedAcc) != 0 && len(p.Nonce) != 0
}

type AccountPreviewParam struct {
	AppId        int
	AK           string
	AccountId    string
	NeedMnemonic bool
}

func (p *AccountPreviewParam) Valid() bool {
	return p.AppId > 0 || len(p.AK) > 0 || len(p.AccountId) > 0
}

type AccountPreview struct {
	AppId       uint64 `json:"app_id"`        // 登记平台的用户标识
	AccountType int    `json:"account_type"`  // 账户类型
	AccountId   string `json:"account_id"`    // 百度云账户的唯一标识
	Status      int    `json:"status"`        // 费用状态 0:INIT 1:RUNNING 2:STOPPED
	NonStop     int    `json:"non_stop"`      // 是否欠费不停服
	Pause       int    `json:"pause"`         // 是否手动暂停 0:FALSE 1:TRUE
	Ban         int    `json:"ban"`           // 是否被封禁 0:FALSE 1:TRUE
	AK          string `json:"ak"`            // 登记平台的用户ak
	SK          string `json:"sk"`            // 登记平台的用户sk
	AuthGroup   string `json:"auth_group"`    // 权限组
	BanApis     string `json:"ban_apis"`      // 被封禁的api
	Business    string `json:"business"`      // 业务说明,公司名称
	ShowPrice   int    `json:"show_price"`    // 是否显示价格 0/1
	PgcStatus   int    `json:"pgc_status"`    // pgc服务状态 INIT|RUNNING|STOPPED。INIT 表示未开通
	PgcExpireAt uint64 `json:"pgc_expire_at"` // pgc服务过期时间
	PgcAddr     string `json:"pgc_addr"`      // pgc链上账户地址
	PgcMnemonic string `json:"pgc_mnemonic"`  // pgc链上账户
	Ctime       uint64 `json:"ctime"`         // 创建时间
}

type AccountPreviewResp struct {
	BaseResp
	Account AccountPreview `json:"account"`
}

type ListAccountsParam struct {
	PageNo         int
	PageSize       int
	AccountType    int
	AppId          int64
	Status         int
	NonStop        int
	AK             string
	AccountId      string
	Ban            int
	Pause          int
	AutoPause      int
	Business       string
	PgcStatus      int
	NeedTotalCount bool
}

func GetDefaultListAccountsParam() *ListAccountsParam {
	return &ListAccountsParam{
		PageNo:      1,
		PageSize:    10,
		AccountType: -1,
		AppId:       0,
		Status:      -1,
		NonStop:     -1,
		Ban:         -1,
		Pause:       -1,
		AutoPause:   -1,
		PgcStatus:   -1,
	}
}

type ListAccountsResp struct {
	BaseResp
	Accounts []AccountTabNode `json:"accounts"`
	Total    int64            `json:"total"`
}

type AccountTabNode struct {
	Id          uint64 `json:"id"`           // 自增id
	AppId       uint64 `json:"app_id"`       // 登记平台的用户标识
	AccountType int    `json:"account_type"` // 账户类型
	AccountId   string `json:"account_id"`   // 百度云账户的唯一标识
	Status      int    `json:"status"`       // 费用状态 0:INIT 1:RUNNING 2:STOPPED
	NonStop     int    `json:"non_stop"`     // 是否欠费不停服 0:FALSE 1:TRUE
	Pause       int    `json:"pause"`        // 是否手动暂停 0:FALSE 1:TRUE
	AutoPause   int    `json:"auto_pause"`   // 是否开启自动暂停 0:FALSE 1:TRUE
	Ban         int    `json:"ban"`          // 是否被封禁 0:FALSE 1:TRUE
	AK          string `json:"ak"`           // 登记平台的用户ak
	SK          string `json:"sk"`           // 登记平台的用户sk
	AuthGroup   string `json:"auth_group"`   // 权限组
	BanApis     string `json:"ban_apis"`     // 被封禁的api
	Business    string `json:"business"`     // 业务说明,公司名称
	Scope       int    `json:"scope"`        // 经营范围
	Description string `json:"description"`  // 应用描述，业务场景
	Contacts    string `json:"contacts"`     // 业务联系方式
	ShowPrice   int    `json:"show_price"`   // 是否显示价格 0/1
	Ctime       uint64 `json:"ctime"`        // 创建时间
}

type CompanyFile struct {
	AccountId    string `json:"accountId"`
	File         string `json:"file"`
	Suffix       string `json:"suffix"`
	MaterialType string `json:"materialType"`
	Type         string `json:"type"`
	Name         string `json:"name"`
	Number       string `json:"number"`
}

type QueryCompanyFileParam struct {
	StoreId int64
}

type QueryCompanyFileResp struct {
	BaseResp
	Business    string `json:"business"`
	CompanyFile string `json:"company_file"`
}

type IntraGetAccByAddrResp struct {
	BaseResp
	Addr     string `json:"addr"`
	AddrType int    `json:"addr_type"`
	Mnemonic string `json:"mnemonic"`
	GroupId  int64  `json:"group_id"`
}

type ListAssetHisParam struct {
	AssetId int64 `json:"asset_id"`
	ShardId int64 `json:"shard_id"`
	Page    int   `json:"page"`
	Limit   int   `json:"limit"`
}

func (p *ListAssetHisParam) Valid() bool {
	if p.AssetId < 1 || p.Page < 1 {
		return false
	}
	return true
}

type HistoryMeta struct {
	AssetId int64  `json:"asset_id"`
	Type    int    `json:"type"`
	ShardId int64  `json:"shard_id"`
	Price   int64  `json:"price"`
	TxId    string `json:"tx_id"`
	From    string `json:"from"`
	To      string `json:"to"`
	Ctime   int64  `json:"ctime"`
}

type IntraListAssetHistoryResp struct {
	BaseResp
	List     []*HistoryMeta `json:"list"`
	TotalCnt int            `json:"total_cnt"`
	Cursor   string         `json:"cursor"`
	HasMore  int            `json:"has_more"`
}

type IntraGetAssetTagResp struct {
	BaseResp
	List []horae.TagInfo `json:"list"`
}

type ListShardbyTagResp struct {
	BaseResp
	List    []*ListShardbyTagObject `json:"list"`
	HasMore int                     `json:"has_more"`
	Cursor  string                  `json:"cursor"`
}

type ListShardbyTagObject struct {
	AssetId int64            `json:"asset_id"`
	ShardId int64            `json:"shard_id"`
	Title   string           `json:"title"`
	Thumb   []horae.ThumbMap `json:"thumb"`
}

type OpPointsParam struct {
	Address  string `json:"address"`
	OpId     int64  `json:"op_id"`
	Points   int64  `json:"points"`
	OpCate   int    `json:"op_cate"`
	Describe string `json:"describe"`
	ExtInfo  string `json:"ext_info"`
}

func (p *OpPointsParam) Valid() bool {
	if p.Address == "" || p.Points < 1 || p.OpId < 1 {
		return false
	}
	return true
}

type GetBalanceParam struct {
	Address string `json:"address"`
}

func (p *GetBalanceParam) Valid() bool {
	return p.Address != ""
}

type IntraGetBalanceResp struct {
	BaseResp
	Balance int64 `json:"balance"`
}

type QryOperateParam struct {
	OpId int64 `json:"op_id"`
}

func (p *QryOperateParam) Valid() bool {
	return p.OpId > 0
}

type IntraQryOperateResp struct {
	BaseResp
	OperateNode
}

type ListOperateParam struct {
	Address string `json:"address"`
	Limit   int    `json:"limit"`
	Cursor  string `json:"cursor"`
}

func (p *ListOperateParam) Valid() bool {
	return p.Address != ""
}

type IntraListOperateResp struct {
	BaseResp
	List    []*OperateNode `json:"list"`
	HasMore int            `json:"has_more"`
	Cursor  string         `json:"cursor"`
}

type OperateNode struct {
	Address  string `json:"address"`
	OpType   int    `json:"op_type"`
	OpId     int64  `json:"op_id"`
	Points   int64  `json:"points"`
	Describe string `json:"describe"`
	ExtInfo  string `json:"ext_info"`
	Ctime    int64  `json:"ctime"`
}

// --------------- hub ------------------
type HubKnockOrderParam struct {
	Code      int   `json:"code"`
	OrderType int   `json:"order_type"`
	AppId     int64 `json:"app_id"`
	Oid       int64 `json:"oid"`
}

type ListOrderByTpOidParam struct {
	AppId     int64  `json:"app_id"`
	Addr      string `json:"address"`
	Status    int    `json:"status"`
	TpOidList string `json:"tp_oid_list"`
}

type ListOrderByTpOidResp struct {
	BaseResp
	List []*trade.OrderInfo `json:"list"`
}

type QueryOrderInfoResp struct {
	BaseResp
	Data struct {
		trade.OrderInfo
	} `json:"data"`
}

// --------------- collect objects ----------------

type CreateCollectParam struct {
	Addr      string `json:"addr"`
	Title     string `json:"title"`
	Type      int    `json:"type"`
	CollectId int64  `json:"collect_id"`
}

func (p *CreateCollectParam) Valid() bool {
	return p.Addr != "" && p.Title != "" && p.CollectId > 0
}

type UpdateCollectParam struct {
	CollectId int64  `json:"collect_id"`
	Addr      string `json:"addr"`
	Title     string `json:"title"`
	AssetId   int64  `json:"asset_id"`
	ShardId   int64  `json:"shard_id"`
}

func (p *UpdateCollectParam) Valid() bool {
	return p.Addr != "" && p.CollectId > 0
}

type DelCollectParam struct {
	CollectId int64  `json:"collect_id"`
	Addr      string `json:"addr"`
}

func (p *DelCollectParam) Valid() bool {
	return p.Addr != "" && p.CollectId > 0
}

type QueryCollectParam struct {
	CollectId int64  `json:"collect_id"`
	Addr      string `json:"addr"`
}

func (p *QueryCollectParam) Valid() bool {
	return p.Addr != "" && p.CollectId > 0
}

type QueryCollectResp struct {
	BaseResp
	Meta *ShardsCollectNode `json:"meta"`
}

type ListCollectAssetParam struct {
	CollectId int64  `json:"collect_id"`
	AssetId   int64  `json:"asset_id"`
	Cursor    string `json:"cursor"`
	Limit     int    `json:"limit"`
	Sort      int    `json:"sort"`
}

func (p *ListCollectAssetParam) Valid() bool {
	return p.CollectId > 0
}

type ListCollectAssetResp struct {
	BaseResp
	List    []*CollectRelationNode `json:"list"`
	Cursor  string                 `json:"cursor"`
	HasMore int                    `json:"has_more"`
}

type CollectRelationNode struct {
	AssetId int64            `json:"asset_id"`
	ShardId int64            `json:"shard_id"`
	Title   string           `json:"title"`
	Thumb   []horae.ThumbMap `json:"thumb"`
	Ctime   int64            `json:"ctime"`
}

type ListCollectParam struct {
	Addr       string `json:"addr"`
	Cursor     string `json:"cursor"`
	Limit      int    `json:"limit"`
	Sort       int    `json:"sort"`
	Type       int    `json:"type"`
	Types      string `json:"types"`
	DaysFilter int64  `json:"time"`
}

func (p *ListCollectParam) Valid() bool {
	return p.Addr != ""
}

type ListCollectResp struct {
	BaseResp
	List    []*ShardsCollectNode `json:"list"`
	Cursor  string               `json:"cursor"`
	HasMore int                  `json:"has_more"`
}

type CountCollectParam struct {
	Addr  string `json:"addr"`
	Types string `json:"types"`
}

func (p *CountCollectParam) Valid() bool {
	return p.Addr != ""
}

type CountCollectResp struct {
	BaseResp
	Count int `json:"count"`
}

type ListCollectAggParam struct {
	CollectId int64  `json:"collect_id"`
	Sort      int    `json:"sort"`
	Cursor    string `json:"cursor"`
	Limit     int    `json:"limit"`
}

func (p *ListCollectAggParam) Valid() bool {
	return p.CollectId > 0
}

type CollectAggInfo struct {
	AssetId int64            `json:"asset_id"`
	Title   string           `json:"title"`
	Thumb   []horae.ThumbMap `json:"thumb"`
	Ctime   int64            `json:"ctime"`
}

type ListCollectAggResp struct {
	BaseResp
	List    []*CollectAggInfo `json:"list"`
	Cursor  string            `json:"cursor"`
	HasMore int               `json:"has_more"`
}

type ShardsCollectNode struct {
	CollectId int64            `json:"collect_id"`
	Type      int              `json:"type"`
	Title     string           `json:"title"`
	Cover     []horae.ThumbMap `json:"cover"`
	Ctime     int64            `json:"ctime"`
}

type UpdateCollectAssetParam struct {
	Addr      string `json:"addr"`
	CollectId int64  `json:"collect_id"`
	Assets    string `json:"assets"`
}

func (p *UpdateCollectAssetParam) Valid() bool {
	return p.Addr != "" && p.CollectId > 0 && p.Assets != ""
}

type ComposeParam struct {
	AssetId int64
	StrgNo  int
	Addr    string
	Pkey    string
	Nonce   int64
	Sign    string
	UAddr   string
	UPkey   string
	AstList string
	Token   string
	UserId  int64
}

type ComposeShardResp struct {
	BaseResp
	AssetId int64 `json:"asset_id"`
	ShardId int64 `json:"shard_id"`
}

type BatchFreezeShardParam struct {
	Addr   string
	Shards string
}

type BatchCumfreShardParam struct {
	Addr   string
	Pkey   string
	Shards string
}

type ListAuditSeparateParam struct {
	Page int `json:"page"`
	Size int `json:"size"`
}

func (p *ListAuditSeparateParam) Valid() error {
	if p.Page <= 0 {
		return errors.New("page invalid")
	}
	if p.Size <= 0 {
		return errors.New("size invalid")
	}

	return nil
}

type AuditSeparateParam struct {
	SplitName               int64  `json:"splitName"`
	CmsContractId           string `json:"cmsContractId"`
	SkuId                   string `json:"skuId"`
	Success                 int    `json:"success"`
	FailReason              string `json:"failReason"`
	SeparateRatio           int    `json:"separateRatio"`
	CommissionSeparateRatio int    `json:"commissionSeparateRatio"`
	CommissionRate          int    `json:"commissionRate"`
}

func (p *AuditSeparateParam) Valid() error {
	if p.SplitName <= 0 {
		return errors.New("invalid split name")
	}
	if p.Success < 0 {
		return errors.New("invalid success")
	}

	return nil
}

type AuditCreateSeparateAccountResp struct {
	BaseResp
	Success               int    `json:"success"`
	SeparateAccountNumber int64  `json:"separateAccountNumber"`
	FailReason            string `json:"failReason"`
}

type AuditFilterSeparateParam struct {
	CompanyName string `json:"companyName"`
	Page        int    `json:"page"`
	Size        int    `json:"size"`
}

func (p *AuditFilterSeparateParam) Valid() error {
	if len(p.CompanyName) <= 0 {
		return errors.New("companyName invalid")
	}
	if p.Size <= 0 {
		return errors.New("size invalid")
	}
	if p.Page <= 0 {
		return errors.New("page invalid")
	}

	return nil
}

type UpdateSeparateRadioParam struct {
	SplitName               int64  `json:"splitName"`
	CmsContractId           string `json:"cmsContractId"`
	SeparateRatio           int    `json:"separateRatio"`
	CommissionSeparateRatio int    `json:"commissionSeparateRatio"`
}

func (p *UpdateSeparateRadioParam) Valid() error {
	if p.SplitName <= 0 {
		return errors.New("invalid split name")
	}

	return nil
}

type UpdateSeparateRadioResp struct {
	BaseResp
	Success    int    `json:"success"`
	FailReason string `json:"failReason"`
}

type AuditSeparateDetailResp struct {
	BaseResp
	Detail DetailItem `json:"detail"`
}

type DetailItem struct {
	AppID                   int64  `json:"appId"`
	SeparateID              int64  `json:"splitName"`             // splitName
	SeparateAccountNumber   int64  `json:"separateAccountNumber"` // billing返回
	SeparateAccountId       int64  `json:"separateAccountId"`     // billing返回
	SeparateRatio           int    `json:"separateRatio"`
	CommissionRatio         int    `json:"commissionRatio"`
	CommissionSeparateRatio int    `json:"commissionSeparateRatio"`
	CmsContractId           string `json:"cmsContractId"`
	SkuId                   string `json:"skuId"` // pm填写
	BusinessLicense         string `json:"businessLicense"`
	Status                  int    `json:"status"`
	StatusDesc              string `json:"statusDesc"` // 审核失败时填写失败原因

	DayMaxFrozenAmount    int    `json:"dayMaxFrozenAmount"`    // 每天最大退款限额/元, 默认为10000
	PoolCashPledge        int    `json:"poolCashPledge"`        // 提现后的保留金额/元, 默认0
	SupplierType          int    `json:"supplierType"`          // 企业类型 1：企业 2：个体工商户
	CompanyName           string `json:"companyName"`           // 企业名称
	BusinessScope         string `json:"businessScope"`         // 经营范围
	BusinessProvince      string `json:"businessProvince"`      // 经营省份
	BusinessCity          string `json:"businessCity"`          // 经营城市
	BusinessDetailAddress string `json:"businessDetailAddress"` // 经营地区详细地址
	LicenseUrl            string `json:"licenseUrl"`            // 营业执照
	LicenseStartTime      string `json:"licenseStartTime"`      // 营业执照开始时间
	LicenseEndTime        string `json:"licenseEndTime"`        // 营业执照结束时间
	Integrate             int    `json:"integrate"`
	TaxRegistrationNo     string `json:"taxRegistrationNo"`   // 税务登记证号, 非三证合一必填
	TaxRegistrationUrl    string `json:"taxRegistrationUrl"`  // 税务登录证图片URL, 非三证合一必填
	TaxEndTime            string `json:"taxEndTime"`          // 税务登记证结束时间, 非三证合一必填
	OrganizationCode      string `json:"organizationCode"`    // 组织机构代码, 非三证合一必填
	OrganizationUrl       string `json:"organizationUrl"`     // 组织机构代码图片URL, 非三证合一必填
	OrganizationEndTime   string `json:"organizationEndTime"` // 组织结构代码结束时间, 非三证合一必填

	PaymentDays     int    `json:"paymentDays"`     // capitalSettlementType=1时必传
	StlAcctType     int    `json:"stlAcctType"`     // 结算对象类型 1-企业对公户 2-法人对私户 3-经营联系人对私户, 默认1
	OccupationType  string `json:"occupationType"`  // 职业, 对私结算必填，0-国家机关、党群组织、企业、事业单位负责人 1-专业技术人员  3-办事人员和有关人员 4-商业、服务业人员  5-农、林、牧、渔、水利业生产人员 6-生产、运输设备操作人员及有关人员 X-军人
	IndustryId      int    `json:"industryId"`      // 行业id, 通过接口查询的结果
	ManagePermitUrl string `json:"managePermitUrl"` // 经营许可证，若接口查询为needPermit=1则必填
	AuthCapital     int    `json:"authCapital"`     // 注册资本（元）

	BankAccount    string `json:"bankAccount"`    // 开户名
	BankCard       string `json:"bankCard"`       // 银行卡号
	BankProvince   string `json:"bankProvince"`   // 开户省份
	BankCity       string `json:"bankCity"`       // 开户城市
	BankName       string `json:"bankName"`       // 所属银行
	BankBranchName string `json:"bankBranchName"` // 开户支行
	PhoneNumber    string `json:"phoneNumber"`    // 银行预留手机号

	LegalPerson        string `json:"legalPerson"`        // 法人姓名
	LegalId            string `json:"legalId"`            // 法人身份证号
	LegalPersonType    int    `json:"legalPersonType"`    // 法人证件类型: 1, “身份证”, 3, “外国护照”
	LegalCardStartTime string `json:"legalCardStartTime"` // 法人证件开始时间
	LegalCardEndTime   string `json:"legalCardEndTime"`   // 法人证件结束时间
	IdCardFrontUrl     string `json:"idCardFrontUrl"`     // 法人身份证正面
	IdCardBackUrl      string `json:"idCardBackUrl"`      // 法人身份证反面

	ManagerSame          int    `json:"managerSame"`          // 经营控股人是否与法人一致 1是 0或其它值不是
	Manager              string `json:"manager"`              // 经营控股人managerSame=1取法人信息，否则必填, 2到50字符
	ManagerCardType      int    `json:"managerCardType"`      // 经营控股人证件类型, managerSame=1取法人信息, managerSame!=1不传则默认身份证
	ManagerCard          string `json:"managerCard"`          // 经营控股人证件号 managerSame=1取法人信息，否则必填
	ManagerCardStartTime string `json:"managerCardStartTime"` // 经营控股人证件开始时间, managerSame=1取法人信息，否则必填,yyyy-MM-dd，不早于结束时间
	ManagerCardEndTime   string `json:"managerCardEndTime"`   // 经营控股人证件结束时间 managerSame=1取法人信息，否则必填 yyyy-MM-dd，今天之后
	ManagerCardFrontUrl  string `json:"managerCardFrontUrl"`  // 经营控股人证件正面 managerSame=1取法人信息，否则必填 不超过255字符
	ManagerCardBackUrl   string `json:"managerCardBackUrl"`   // 经营控股人证件反面 managerSame=1取法人信息，否则必填 不超过255字符

	ContactSame          int    `json:"contactSame"`          // 联系人是否与法人一致
	Contact              string `json:"contact"`              // 联系人，contact_same=1取法人信息，否则必填
	ContactCard          string `json:"contactCard"`          // 联系人证件, ContactSame=1取法人信息，否则必填
	ContactCardType      int    `json:"contactCardType"`      // 联系人证件类型, contactSame=1取法人信息 contactSame!=1默认身份证，录入值参考参考legalPersonType
	ContactCardStartTime string `json:"contactCardStartTime"` // 联系人证件开始时间, ContactSame=1取法人信息，否则必填
	ContactCardEndTime   string `json:"contactCardEndTime"`   // 联系人证件结束时间, ContactSame=1取法人信息，否则必填
	ContactCardFrontUrl  string `json:"contactCardFrontUrl"`  // 联系人证件正面, ContactSame=1取法人信息，否则必填
	ContactCardBackUrl   string `json:"contactCardBackUrl"`   // 联系人证件反面, ContactSame=1取法人信息，否则必填

	BenefitSame          int    `json:"benefitSame"`          // 受益人是否与法人一致 1是 0或其它值不是
	Benefit              string `json:"benefit"`              // benefitSame=1取法人信息，否则必填, 2到50字符
	BenefitCardType      int    `json:"benefitCardType"`      // 受益人证件类型, benefitSame=1取法人信息, benefitSame!=1不传则默认身份证
	BenefitCard          string `json:"benefitCard"`          // 受益人证件, benefitSame=1取法人信息，否则必填
	BenefitCardStartTime string `json:"benefitCardStartTime"` // 受益人证件开始时间, benefitSame=1取法人信息，否则必填
	BenefitCardEndTime   string `json:"benefitCardEndTime"`   // 受益人证件结束时间, benefitSame=1取法人信息，否则必填
	BenefitCardFrontUrl  string `json:"benefitCardFrontUrl"`  // 受益人证件正面, benefitSame=1取法人信息，否则必填
	BenefitCardBackUrl   string `json:"benefitCardBackUrl"`   // 受益人证件反面, benefitSame=1取法人信息，否则必填

	// pic https
	License string `json:"license"`

	Tax          string `json:"taxRegistration"`
	Organization string `json:"organization"`
	ManagePermit string `json:"managePermit"`
	IdFront      string `json:"idCardFront"`
	IdBack       string `json:"idCardBack"`
	ManagerFront string `json:"managerCardFront"`
	ManageBack   string `json:"managerCardBack"`
	ContactFront string `json:"contactCardFront"`
	ContactBack  string `json:"contactCardBack"`
	BenefitFront string `json:"benefitCardFront"`
	BenefitBack  string `json:"benefitCardBack"`

	Ctime int64 `json:"ctime"`
}

type ListSeparateAccNode struct {
	AppID                   int64  `json:"appId"`
	SeparateID              int64  `json:"splitName"`
	SeparateRatio           int    `json:"separateRatio"`
	CommissionSeparateRatio int    `json:"commissionSeparateRatio"`
	CompanyName             string `json:"companyName"`
	Status                  int    `json:"status"`
	Ctime                   int64  `json:"ctime"`
}

type ListSeparateResp struct {
	BaseResp
	List  []*ListSeparateAccNode
	Total int64
}

type QueryAdvanceResp struct {
	BaseResp
	BTime int64 `json:"buy_time"`
}

// Asset tag Handle
const (
	// 可合成
	AstTagAllowCompose = 100001

	// 可回收
	AstTagAllowRecyc = 100002

	// 可发布
	AstTagAllowPub = 100003

	// 不可转赠
	AstTagNotAllowTrans = 100004

	// 即时转赠
	AstTagImAllowTrans = 100005

	// 可分解
	AstTagAllowDivide = 100006
)

type AstTagHandle struct {
	TagList map[int]string
}

func NewAstTagHandle(tags map[int]string) *AstTagHandle {
	return &AstTagHandle{tags}
}

func (t *AstTagHandle) hasTag(tagId int) bool {
	if t == nil {
		return false
	}
	if _, ok := t.TagList[tagId]; ok {
		return true
	}

	return false
}

func (t *AstTagHandle) IsAllowTrans() bool {
	return !t.hasTag(AstTagNotAllowTrans)
}

func (t *AstTagHandle) IsAllowRecyc() bool {
	return t.hasTag(AstTagAllowRecyc)
}

func (t *AstTagHandle) IsAllowCompose() bool {
	return t.hasTag(AstTagAllowCompose)
}

func (t *AstTagHandle) IsAllowPub() bool {
	return t.hasTag(AstTagAllowPub)
}

// 检查是否允许立即转赠
func (t *AstTagHandle) IsImAllowTrans() bool {
	return t.hasTag(AstTagImAllowTrans)
}

func (t *AstTagHandle) IsAllowDivide() bool {
	return t.hasTag(AstTagAllowDivide)
}

func (t *AstTagHandle) GetTagList() []int {
	tags := make([]int, 0)
	if t == nil || len(t.TagList) < 1 {
		return tags
	}

	for tag := range t.TagList {
		tags = append(tags, tag)
	}

	return tags
}

type ListRefundResp struct {
	BaseResp
	Data struct {
		List    []*RefundInfo `json:"list"`
		Cursor  string        `json:"cursor"`
		HasMore int           `json:"has_more"`
	} `json:"data"`
}

type RefundInfo struct {
	Rid          int64    `json:"rid"`
	Oid          int64    `json:"oid"`
	UK           int64    `json:"uk"`
	StoreId      int64    `json:"store_id"`
	StoreName    string   `json:"store_name"`
	BuyerAddr    string   `json:"buyer_addr"`
	AssetId      int64    `json:"asset_id"`
	ShardIds     []int64  `json:"shard_ids"`
	Title        string   `json:"title"`
	Thumb        []string `json:"thumb"`
	SinglePrice  int      `json:"single_price"`
	PayPrice     int      `json:"pay_price"`
	Count        int      `json:"count"`
	Reason       string   `json:"reason"`
	Message      string   `json:"message"`
	RefundStatus int      `json:"refund_status"`
	Rtime        int64    `json:"rtime"`
	Ctime        int64    `json:"ctime"`
}

type CountNearExpireParam struct {
	AppId      int64  `json:"app_id"`
	Addr       string `json:"addr"`
	AssetCate  int    `json:"asset_cate"`
	ExpireTime int64  `json:"expire_time"`
}

func (p *CountNearExpireParam) Valid() bool {
	return p.AppId > 0 && p.Addr != "" && p.ExpireTime > 0
}

type CountNearExpireResp struct {
	BaseResp
	Count int `json:"count"`
}

type ListShardsByCateParam struct {
	AppId     int64  `json:"app_id"`
	Addr      string `json:"addr"`
	AssetCate int    `json:"asset_cate"`
	SortKey   int    `json:"sort_key"`
	Cursor    string `json:"cursor"`
	Limit     int    `json:"limit"`
}

func (p *ListShardsByCateParam) Valid() bool {
	return p.AppId > 0 && p.Addr != "" && p.AssetCate > 0 && p.SortKey > 0
}

type ListShardsByCateResp struct {
	BaseResp
	List    []*ShardObject `json:"list"`
	HasMore int            `json:"has_more"`
	Cursor  string         `json:"cursor"`
}

type IntranetOrderDetailResp struct {
	BaseResp
	Data *trade.HubOrderDetail `json:"data"`
}

type CountAssetsTypeParam struct {
	AppId int64  `json:"app_id"`
	Addr  string `json:"addr"`
	Type  int    `json:"type"`
}

func (p *CountAssetsTypeParam) Valid() error {
	if len(p.Addr) <= 0 {
		return errors.New("addr invalid")
	}
	if p.Type < 0 {
		return errors.New("type invalid")
	}
	return nil
}

type CountAssetsTypeResp struct {
	BaseResp
	AssetCnt int `json:"asset_cnt"`
	ShardCnt int `json:"shard_cnt"`
}

type AggrAstListParam struct {
	AppId    int64  `json:"app_id"`
	Title    string `json:"title"`
	Addr     string `json:"addr"`
	Type     int    `json:"type"`
	MonoIncr int    `json:"mono"`
	Limit    int    `json:"limit"`
	Cursor   string `json:"cursor"`
}

func (p *AggrAstListParam) Valid() error {
	if len(p.Addr) <= 0 {
		return errors.New("addr invalid")
	}
	if p.Type < 0 {
		return errors.New("type invalid")
	}
	if p.Limit <= 0 && len(p.Cursor) <= 0 {
		return errors.New("limit or cursor invalid")
	}
	return nil
}

type AggrAstListNode struct {
	AssetId int64    `json:"asset_id"`
	Title   string   `json:"title"`
	Thumb   []string `json:"thumb"`
	SrdsCnt int      `json:"shard_cnt"`
	Mtime   int64    `json:"mtime"`
}

type AggrAstListResp struct {
	BaseResp
	List    []*AggrAstListNode `json:"list"`
	HasMore int                `json:"has_more"`
	Cursor  string             `json:"cursor"`
}

type ListSdsByAstV3Param struct {
	Addr     string `json:"addr"`
	MonoIncr int    `json:"mono"`
	AssetId  int64  `json:"asset_id"`
	Limit    int    `json:"limit"`
	Cursor   string `json:"cursor"`
}

func (p *ListSdsByAstV3Param) Valid() error {
	if len(p.Addr) <= 0 {
		return errors.New("addr invalid")
	}
	if p.AssetId <= 0 {
		return errors.New(" asset_id invalid")
	}
	if p.Limit <= 0 && len(p.Cursor) <= 0 {
		return errors.New("limit or cursor invalid")
	}
	return nil
}

type ListSdsByAstV3RNode struct {
	AssetId int64 `json:"asset_id"`
	ShardId int64 `json:"shard_id"`
	AppId   int64 `json:"app_id"`
}

type ListSdsByAstV3Resp struct {
	BaseResp
	List    []*ListSdsByAstV3RNode `json:"list"`
	HasMore int                    `json:"has_more"`
	Cursor  string                 `json:"cursor"`
}
