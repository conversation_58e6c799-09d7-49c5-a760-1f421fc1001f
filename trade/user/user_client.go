package user

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strings"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type UserFunc func(hc *UserClient) (interface{}, *RequestRes, error)

type UserClient struct {
	addr *addr.Addr
}

func NewUserClient(addr *addr.Addr) (*UserClient, error) {
	if addr == nil {
		return nil, errors.New("addr is nil")
	}

	return &UserClient{addr}, nil
}

func RetryUser(getAddr func() (*addr.Addr, error), f UserFunc, tryTime int) (resp interface{}, reqResp *RequestRes, err error) {
	if f == nil {
		return nil, nil, errors.New("need user function")
	}

	var userAddr *addr.Addr
	var client *UserClient
	for i := 0; i < tryTime; i++ {
		userAddr, err = getAddr()
		if err != nil {
			continue
		}
		client, err = NewUserClient(userAddr)
		if err != nil {
			continue
		}
		resp, reqResp, err = f(client)
		if err == nil && resp != nil {
			break
		}
	}
	if err != nil {
		return nil, nil, err
	}
	if resp == nil {
		return nil, nil, errors.New("nil resp")
	}
	return resp, reqResp, nil
}

func (c *UserClient) GetUInfo(param *UInfoParam) (*UInfoResp, *RequestRes, error) {
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("user_name", param.UserName)
	body := v.Encode()
	reqRes, err := c.doRequest(GetUInfoApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request user @ loaduser fail.err:%v", err)
	}
	var result UInfoResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal user response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (c *UserClient) BindInviter(param *BindInviterParam) (*BaseResp, *RequestRes, error) {
	if param == nil {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("uid", fmt.Sprintf("%d", param.Uid))
	v.Set("user_name", param.UserName)
	v.Set("invite_code", param.InviteCode)
	body := v.Encode()
	reqRes, err := c.doRequest(BindInviterApi, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request user @ bind fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal user response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// post
func (t *UserClient) doRequest(api string, data string, opt ...map[string]interface{}) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	if !strings.HasPrefix(api, "/") {
		api = "/" + api
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	if len(opt) != 0 {
		for _, m := range opt {
			if cookie, ok := m["Cookie"]; ok {
				header["Cookie"] = fmt.Sprintf("%v", cookie)
				break
			}
		}
	}
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}
