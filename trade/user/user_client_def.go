package user

import "net/http"

const (
	GetUInfoApi    = "/internal/user/v1/load_user"
	BindInviterApi = "/internal/user/v1/bind_inviter"

	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type UInfoParam struct {
	Uid      int64  `json:"uid"`
	UserName string `json:"user_name"`
}

type UInfoResp struct {
	BaseResp
	Data struct {
		Uname        string `json:"uname"`
		Photo        string `json:"photo"`
		Address      string `json:"address"`
		IsNew        int    `json:"is_new"`
		RealName     int    `json:"is_real"`
		Uid          int64  `json:"uid"`
		Status       int    `json:"status"`
		HasPurchased int    `json:"has_purchased"`
		InviteCode   string `json:"invite_code"`
		Inviter      int64  `json:"inviter"`
		IBtime       int64  `json:"ibtime"`
		Ctime        int64  `json:"ctime"`
	} `json:"data"`
}

type BindInviterParam struct {
	Uid        int64  `json:"uid"`
	UserName   string `json:"user_name"`
	InviteCode string `json:"invite_code"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}
