package wallet

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
	"icode.baidu.com/baidu/blockchain/xasset-golib/trade/activity"
)

type WalletClient struct {
	addr *addr.Addr
}

func NewWalletClient(addr *addr.Addr) *WalletClient {
	return &WalletClient{addr}
}

func (t *WalletClient) CreateOrAlterPubStore(param *activity.CreateOrUpdateStoreParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.CreateValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("store_name", param.StoreName)
	v.Set("store_type", fmt.Sprintf("%d", param.StoreType))
	v.Set("logo", param.Logo)
	v.Set("cover", param.Cover)
	v.Set("short_desc", param.ShortDesc)
	v.Set("weight", fmt.Sprintf("%d", param.Weight))
	v.Set("ext_info", param.ExtInfo)

	body := v.Encode()
	reqRes, err := t.doRequest(WalletApiCreateOrAlterPubStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xwallet for create store fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *WalletClient) PublishStore(param *activity.BaseStoreParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.Valid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("op_type", fmt.Sprintf("%d", param.OpType))

	body := v.Encode()
	reqRes, err := t.doRequest(WalletApiPublishStore, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xwallet for publish store fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *WalletClient) CreateOrAlterPubAct(param *activity.CreateOrUpdateActParam) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if !param.CreateValid() {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", param.ActId))
	v.Set("store_id", fmt.Sprintf("%d", param.StoreId))
	v.Set("owner_type", fmt.Sprintf("%d", param.OwnerType))
	v.Set("link_type", fmt.Sprintf("%d", param.LinkType))
	v.Set("jump_link", param.JumpLink)
	v.Set("position", fmt.Sprintf("%d", param.Position))
	v.Set("issuer", param.Issuer)
	v.Set("act_name", param.ActName)
	v.Set("short_desc", param.ShortDesc)
	v.Set("thumb", param.Thumb)
	v.Set("img_desc", param.ImgDesc)
	v.Set("start", fmt.Sprintf("%d", param.Start))
	v.Set("end", fmt.Sprintf("%d", param.End))
	v.Set("act_style", fmt.Sprintf("%d", param.ActStyle))
	v.Set("bookable", fmt.Sprintf("%d", param.Bookable))
	v.Set("weight", fmt.Sprintf("%d", param.Weight))
	v.Set("ext_info", param.ExtInfo)
	body := v.Encode()
	reqRes, err := t.doRequest(WalletApiCreateOrAlterPubAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xwallet for create or alter pub act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

func (t *WalletClient) PublishAct(actId int64, opType int) (*BaseResp, *RequestRes, error) {
	// check param
	if err := t.isInit(); err != nil {
		return nil, nil, err
	}
	if actId < 1 {
		return nil, nil, errors.New("param error")
	}
	v := url.Values{}
	v.Set("act_id", fmt.Sprintf("%d", actId))
	v.Set("op_type", fmt.Sprintf("%d", opType))

	body := v.Encode()
	reqRes, err := t.doRequest(WalletApiPublishAct, body)
	if err != nil {
		return nil, nil, fmt.Errorf("request xwallet for publish act fail.err:%v", err)
	}
	var result BaseResp
	err = json.Unmarshal([]byte(reqRes.Body), &result)
	if err != nil {
		return nil, nil, fmt.Errorf("json unmarshal store response fail.url:%s http_code:%d "+
			"resp:%s err:%v", reqRes.ReqUrl, reqRes.HttpCode, reqRes.Body, err)
	}
	return &result, reqRes, nil
}

// post
func (t *WalletClient) doRequest(api string, data string) (*RequestRes, error) {
	hostInfo, err := t.addr.GetAddr()
	if err != nil {
		return nil, fmt.Errorf("get addr fail.err:%v", err)
	}
	reqUrl := fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
	result := &RequestRes{
		ReqUrl: reqUrl,
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		t.addr.SetFail(hostInfo)
		return result, fmt.Errorf("request asset fail.url:%s err:%v", reqUrl, err)
	}
	t.addr.SetOk(hostInfo)
	result.HttpCode = resp.StatusCode
	result.Header = resp.Header
	result.Body = string(resp.Body)
	if resp.StatusCode < 200 {
		return result, fmt.Errorf("request asset http code < 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}
	return result, nil
}

// public方法必须调用检查是否初始化，防止错误使用导致panic
func (t *WalletClient) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}
	return nil
}
