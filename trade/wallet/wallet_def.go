package wallet

import "net/http"

const (
	WalletApiCreateOrAlterPubStore = "/internal/wallet/v1/capstore"
	WalletApiPublishStore          = "/internal/wallet/v1/publishstore"
	WalletApiCreateOrAlterPubAct   = "/internal/wallet/v1/capact"
	WalletApiPublishAct            = "/internal/wallet/v1/publishact"

	ReqConnTimeoutMs = 1000
	ReqRWTimeoutMs   = 3000
)

type RequestRes struct {
	HttpCode int         `json:"http_code"`
	ReqUrl   string      `json:"req_url"`
	Header   http.Header `json:"header"`
	Body     string      `json:"body"`
}

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}
