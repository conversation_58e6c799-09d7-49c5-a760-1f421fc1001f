package uas

import (
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

const (
	uasStatusSucc = 0
)

const (
	ReqConnTimeoutMs = 200
	ReqRWTimeoutMs   = 600
)

const (
	UasHost = "http://uas.baidu-int.com:8081"
)

const (
	UasApiJsonPortrait = "/json/v2/"
)

const QueryPortraitJson = `{
	"user_attribute": [
        {
            "user_id": {
                "id": "%d",
                "type": "USERID"
            },
            "attribute": [
                {"name": "性别"},
                {"name": "年龄"},
                {"name": "教育水平"},
                {"name": "职业类别"},
                {"name": "所在行业"},
                {"name": "人生阶段"},
                {"name": "消费水平"}
            ]
        }
    ]
}`

type QueryPortraitItem struct {
	Value  string `json:"value"`
	Weight int    `json:"weight"`
}
type QueryPortraitAttr struct {
	Desc map[string]string   `json:"description"`
	Item []QueryPortraitItem `json:"item"`
}
type QueryPortraitRes struct {
	UserId map[string]string   `json:"user_id"`
	Attr   []QueryPortraitAttr `json:"attribute"`
}
type QueryPortraitResp struct {
	UserAttr []QueryPortraitRes `json:"user_attribute"`
	Status   string             `json:"status"`
}

type UasClient struct {
	author string
	passwd string
}

func NewUasClient(author, passwd string) *UasClient {
	return &UasClient{author, passwd}
}

func (t *UasClient) QueryPortrait(uid int64, minWeight int) (map[string]QueryPortraitItem, error) {
	reqUrl := fmt.Sprintf("%s%s", UasHost, UasApiJsonPortrait)
	reqData := fmt.Sprintf(QueryPortraitJson, uid)
	resp, err := t.doRequest(reqUrl, reqData)
	if err != nil {
		return nil, fmt.Errorf("request uas fail.err:%v", err)
	}

	var res map[string]interface{}
	err = json.Unmarshal([]byte(resp), &res)
	if err != nil {
		return nil, err
	}
	if v, ok := res["error"]; ok {
		return nil, fmt.Errorf("%v", v)
	}
	if v, ok := res["status"]; !ok || v != "SUCCESS" {
		return nil, fmt.Errorf("query portrait status error.status:%s", v)
	}

	var queryRes QueryPortraitResp
	err = json.Unmarshal([]byte(resp), &queryRes)
	if err != nil {
		return nil, fmt.Errorf("json unmarshal result failed.resp:%s err:%v", resp, err)
	}
	if len(queryRes.UserAttr) < 1 {
		return nil, fmt.Errorf("query portrait result error.resp:%s", resp)
	}

	returnRes := make(map[string]QueryPortraitItem, 0)
	for _, v := range queryRes.UserAttr[0].Attr {
		returnRes[v.Desc["name"]] = QueryPortraitItem{"未知", 0}
		if len(v.Item) > 0 && v.Item[0].Weight > minWeight {
			returnRes[v.Desc["name"]] = v.Item[0]
		}
	}

	return returnRes, nil
}

func (t *UasClient) doRequest(reqUrl, data string) (string, error) {
	header := make(map[string]string)
	header["Authorization"] = t.author
	header["Password"] = t.passwd
	resp, err := httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
	if err != nil {
		return "", fmt.Errorf("request uas fail.url:%s err:%v", reqUrl, err)
	}

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("request uas http code not 200.url:%s http_code:%d",
			reqUrl, resp.StatusCode)
	}

	return string(resp.Body), nil
}
