package utils

import (
	"bytes"
	"crypto/md5"
	"encoding/binary"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"net"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	mcrypto "icode.baidu.com/baidu/blockchain/xasset-golib/crypto"

	"github.com/spf13/afero"
)

const (
	// 未知默认模块名
	APP_NAME_UNKNOW_DEFAULT = "unknow"
)

func StringInSlice(a string, list []string) bool {
	for _, b := range list {
		if b == a {
			return true
		}
	}
	return false
}

func UserHomeDir() string {
	if runtime.GOOS == "windows" {
		home := os.Getenv("HOMEDRIVE") + os.Getenv("HOMEPATH")
		if home == "" {
			home = os.Getenv("USERPROFILE")
		}
		return home
	}
	return os.Getenv("HOME")
}

// Check if File / Directory Exists
func PathExists(fs afero.Fs, path string) (bool, error) {
	_, err := fs.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

// 用户外部接口响应
func GenRequestId() uint64 {
	return uint64(GenNonce())
}

// 生成nonce值
func GenNonce() int64 {
	randId1 := GenRandId()
	randId2 := GenRandId()
	content := fmt.Sprintf("%d#%d#%d#%s", randId1, randId2, time.Now().UnixNano(), GetHostName())
	s := Sign(content)
	return int64(s & 0x7FFFFFFFFFFFFFFF)
}

// 根据APPID生成伪唯一ID
func GenIdByAppId(appId int64) int64 {
	return int64(GenIdHelp(uint64(appId), 0))
}

// 通过长ID获取APPID
func GetAppId(id int64) int64 {
	return int64(id & 0x00000000000FFFFF)
}

// logid统一使用字符串
func GenLogId() string {
	return fmt.Sprintf("%d", GenRequestId())
}

// 用于内部系统间串联
// appName:app自身名字
// logId:请求方传入logId
// traceId:求方传入traceId
func GenTraceId(appName, logId, traceId string) string {
	if appName == "" {
		appName = APP_NAME_UNKNOW_DEFAULT
	}
	if logId == "" {
		logId = GenLogId()
	}

	if traceId != "" {
		traceId = traceId + "->" + GenSelfTraceId(appName, logId)
	} else {
		traceId = GenSelfTraceId(appName, logId)
	}

	return traceId
}

func GenSelfTraceId(appName, logId string) string {
	host := GetHostName()
	date := time.Now().Unix()
	traceId := fmt.Sprintf("%s#%s#%s#%d", appName, host, logId, date)
	return traceId
}

func DecodeUK(uk int64) int64 {
	if uk < 0 {
		return 0
	}
	uk ^= 100159
	uid := uk & 0x7f00000000000000
	uid += (uk & 0x00ff0000) << 8
	uid += (uk & 0x000000ff) << 16
	uid += ((uk & 0xff000000) >> 16) & 0x0000ff00
	uid += (uk & 0x0000ff00) >> 8
	uid += ((uk & 0x00ff000000000000) >> 8) & 0x0000ff0000000000
	uid += ((uk & 0x0000ff0000000000) >> 8) & 0x000000ff00000000
	uid += ((uk & 0x000000ff00000000) << 16) & 0x00ff000000000000
	return uid
}

func EncodeUid(uid int64) int64 {
	if uid < 1 {
		return -1
	}
	sid := uid & 0x7f00000000000000
	sid += ((uid & 0x0000ff0000000000) << 8) & 0x00ff000000000000
	sid += ((uid & 0x000000ff00000000) << 8) & 0x0000ff0000000000
	sid += ((uid & 0x00ff000000000000) >> 16) & 0x000000ff00000000
	sid += ((uid & 0x0000ff00) << 16) & 0xff000000
	sid += ((uid & 0xff000000) >> 8) & 0x00ff0000
	sid += ((uid & 0x000000ff) << 8) & 0x0000ff00
	sid += ((uid & 0x00ff0000) >> 16) & 0x000000ff
	sid ^= 100159
	return sid
}

// aes+base64 encode
func AesEncode(str, key string) (string, error) {
	if str == "" {
		return "", errors.New("str is empty")
	}

	aesRes, err := mcrypto.AesEncrypt([]byte(str), []byte(key))
	if err != nil {
		return "", err
	}

	return mcrypto.Base64UrlEncode(aesRes), nil
}

// aes+base64 decode
func AesDecode(str, key string) (string, error) {
	if str == "" {
		return "", errors.New("str is empty")
	}

	aesBytes, err := mcrypto.Base64UrlDecode(str)
	if err != nil {
		return "", err
	}

	traceBytes, err := mcrypto.AesDecrypt(aesBytes, []byte(key))
	if err != nil {
		return "", err
	}

	return string(traceBytes), nil
}

func GetHostName() string {
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "127.0.0.1"
	}

	return hostname
}

func GetCallerMethod(skip int) string {
	method := "unknow"
	pc, _, _, ok := runtime.Caller(skip)
	if ok {
		f := runtime.FuncForPC(pc)
		method = f.Name()
	}

	return method
}

func CmdRunWithTimeout(timeout time.Duration, arg string) (string, error) {
	// first
	cmd := exec.Command("/bin/bash", "-c", arg)
	var stdout bytes.Buffer
	cmd.Stdout = &stdout
	var stderr bytes.Buffer
	cmd.Stderr = &stderr
	cmd.Start()

	// second
	done := make(chan error)
	go func() {
		done <- cmd.Wait()
	}()

	var err error
	select {
	case <-time.After(timeout):
		// timeout, kill it
		killErr := ""
		if err = cmd.Process.Kill(); err != nil {
			killErr = fmt.Sprintf("kill cmd fail,err:%s", err.Error())
		}

		go func() {
			// allow goroutine to exit, chan default features
			<-done
		}()

		return stdout.String(), errors.New("cmd run timeout, process:" + cmd.Path +
			" has been killed." + killErr)
	case err = <-done:
		return stdout.String(), err
	}
}

func GetCurrentDir() (string, error) {
	dir, err := filepath.Abs(filepath.Dir(os.Args[0]))
	if err != nil {
		return "", err
	}

	return strings.Replace(dir, "\\", "/", -1), nil
}

func GetSelfIp() (string, error) {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return "", err
	}

	for _, a := range addrs {
		if ipnet, ok := a.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				return ipnet.IP.String(), nil
			}
		}
	}

	return "", errors.New("get self ip fail")
}

// SelfPath gets compiled executable file absolute path
func SelfPath() string {
	path, _ := filepath.Abs(os.Args[0])
	return path
}

// SelfDir gets compiled executable file directory
func SelfDir() string {
	return filepath.Dir(SelfPath())
}

// FileExists reports whether the named file or directory exists.
func FileExists(name string) bool {
	if _, err := os.Stat(name); err != nil {
		if os.IsNotExist(err) {
			return false
		}
	}
	return true
}

func ShowSubstr(str string, length uint32) string {
	if len(str) <= int(length) {
		return str
	}

	ss, sl, rs := "", uint32(0), []rune(str)
	for _, r := range rs {
		if sl++; sl > length {
			break
		}

		ss += string(r)
	}

	return ss
}

func ShowLen(str string) uint32 {
	if len(str) < 1 {
		return 0
	}

	return uint32(len([]rune(str)))
}

func Sign(content string) uint64 {
	h := md5.New()
	io.WriteString(h, content)
	digest := h.Sum(nil)
	var seg1, seg2, seg3, seg4 uint32
	seg1 = binary.LittleEndian.Uint32(digest[0:4])
	seg2 = binary.LittleEndian.Uint32(digest[4:8])
	seg3 = binary.LittleEndian.Uint32(digest[8:12])
	seg4 = binary.LittleEndian.Uint32(digest[12:16])
	var sign, sign1, sign2 uint64
	sign1 = uint64(seg1 + seg3)
	sign2 = uint64(seg2 + seg4)
	sign = (sign1 & 0x00000000ffffffff) | (sign2 << 32)
	return sign
}

/**
 * | 0 - 19  	 | 20-31  | 32   | 33 - 40 | 41 - 56   | 57 - 60 | 61-63 |
 * | 20位    	 |  12位  | 1位  | 8位     | 16位      |  4位    |  3位  |
 * | baseId低20位| 随机值 | 标记 | 随机值  | 签名低16位|  随机值 |  0    |
 */
func GenIdHelp(baseId uint64, flag int) uint64 {
	var s, r1, r2, lk uint64
	content := fmt.Sprintf("%d#%d#%d", baseId, flag, time.Now().UnixNano())
	s = Sign(content)
	r1 = GenRandId()
	r2 = GenRandId()
	lk = baseId

	var id uint64
	id = (lk & 0x0000000000fffff)
	id += ((r2 & 0x000000000000fff0 >> 4) << 20)
	if flag == 1 {
		id += (0x0000000000000001 << 32)
	}
	id += ((r1 & 0x00000000000000ff) << 33)
	id += ((s & 0x000000000000ffff) << 41)
	id += ((r2 & 0x000000000000000f) << 57)

	return id
}

// 生成伪唯一ID
func GenRandId() uint64 {
	nano := time.Now().UnixNano()
	rand.Seed(nano)
	randNum1 := rand.Int63()
	randNum2 := rand.Int63()
	shift1 := rand.Intn(16) + 2
	shift2 := rand.Intn(8) + 1

	randId := ((randNum1 >> uint(shift1)) + (randNum2 >> uint(shift2)) + (nano >> 1)) &
		0x7FFFFFFFFFFFFFFF
	return uint64(randId)
}

// 椭圆曲线私钥签名
func SignECDSA(key, msg string) ([]byte, error) {
	pk, err := mcrypto.GetEcdsaPrivateKeyFromJsonStr(key)
	if err != nil {
		return nil, err
	}
	return mcrypto.SignECDSA(pk, []byte(msg))
}

// 使用ECC公钥来验证签名
func VerifyECDSA(key, signature, msg []byte) (bool, error) {
	pk, err := mcrypto.GetEcdsaPublicKeyFromJsonStr(string(key))
	if err != nil {
		return false, err
	}
	return mcrypto.VerifyECDSA(pk, signature, msg)
}

//hex decode后 验证签名
func VerifySignStr(key, signature string, msg []byte) (bool, error) {
	sign, err := hex.DecodeString(signature)
	if err != nil {
		return false, err
	}
	pk, err := mcrypto.GetEcdsaPublicKeyFromJsonStr(key)
	if err != nil {
		return false, err
	}
	return mcrypto.VerifyECDSA(pk, sign, msg)
}

//计算签名后hex编码
func GenSignStr(key string, msg []byte) (string, error) {
	pk, err := mcrypto.GetEcdsaPrivateKeyFromJsonStr(key)
	if err != nil {
		return "", err
	}
	sign, err := mcrypto.SignECDSA(pk, []byte(msg))
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(sign), nil
}

// 使用椭圆曲线非对称加密
func EncryptByEcdsaKey(publicKey, msg string) ([]byte, error) {
	pk, err := mcrypto.GetEcdsaPublicKeyFromJsonStr(publicKey)
	if err != nil {
		return nil, err
	}
	return mcrypto.EncryptByEcdsaKey(pk, []byte(msg))
}

// 使用椭圆曲线非对称解密
func DecryptByEcdsaKey(privateKey, cypherText []byte) ([]byte, error) {
	pk, err := mcrypto.GetEcdsaPrivateKeyFromJsonStr(string(privateKey))
	if err != nil {
		return nil, err
	}
	return mcrypto.DecryptByEcdsaKey(pk, cypherText)
}

// 使用单个公钥来生成钱包地址
func GetAddressFromPublicKey(publicKey string) (string, error) {
	pk, err := mcrypto.GetEcdsaPublicKeyFromJsonStr(publicKey)
	if err != nil {
		return "", err
	}
	return mcrypto.GetAddressFromPublicKey(pk)
}

// 使用SHA256做单次哈希运算
func HashUsingSha256(data []byte) []byte {
	return mcrypto.HashUsingSha256(data)
}

// 验证钱包地址和公钥是否匹配
func VerifyAddressUsingPublicKey(address, publicKey string) (bool, uint8) {
	pk, err := mcrypto.GetEcdsaPublicKeyFromJsonStr(publicKey)
	if err != nil {
		return false, 0
	}
	return mcrypto.VerifyAddressUsingPublicKeys(address, pk)
}

// 验证钱包地址合法的格式
func IsValidAddress(address string) bool {
	return mcrypto.CheckAddressFormat(address)
}

// StrMapInt 提供一个输入str到输出int的固定映射, bit为二进制位数
// 注意, bit range(0, 16), 2^16 = 35536, 满足业务要求
func StrMapInt(content string, bit int) uint32 {
	h := md5.New()
	io.WriteString(h, content)
	digest := h.Sum(nil)
	sum := digest[0:bit]

	var id uint32
	for index := 0; index < bit; index++ {
		id += uint32(uint8(sum[index])&0x8>>3) << index
	}
	return id
}
