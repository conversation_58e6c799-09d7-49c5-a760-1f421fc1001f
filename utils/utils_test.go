package utils

import (
	"fmt"
	"math"
	"testing"
	"time"
)

func TestGenIdHelp(t *testing.T) {
	ids := []int64{500100, 200100, 100100, 900159, 600201, 520260, 300100, 300200, 300300}
	for _, aid := range ids {
		nid := GenIdByAppId(aid)
		naid := GetAppId(int64(nid))
		fmt.Println(aid, nid, naid)
	}

	assetIds := []int64{
		66749882043766028,
		125623893577995532,
		16547074472711436,
		104582883154629900,
		35336087513896204,
		71394597116613900,
		9088047719421196,
		66466749209679116,
		119475871232290884,
		122087323017388420,
		4155209060688260,
		72856************,
		72856************,
		129405002396967300,
		26804066261115268,
	}

	for _, id := range assetIds {
		appId := GetAppId(id)
		fmt.Println(id, appId)
	}
}

func TestGenRequestId(t *testing.T) {
	fmt.Println(GenRequestId())
	fmt.Println(GenRequestId())
	fmt.Println(GenRequestId())
	fmt.Println(GenRequestId())
	fmt.Println(GenRequestId())
	fmt.Println(GenRequestId())
}

func TestGenTraceId(t *testing.T) {
	tid := GenTraceId("data1", "", "")
	tid2 := GenTraceId("data2", "", tid)
	fmt.Println(tid, tid2)
}

func TestUkToUid(t *testing.T) {
	uid := int64(2418697132)
	uk := EncodeUid(uid)
	fmt.Println(uk)
	decUid := DecodeUK(uk)
	fmt.Println(decUid)
}

func TestGetCurrentDir(t *testing.T) {
	path, err := GetCurrentDir()
	fmt.Println(path, err)
}

func TestCmdRunWithTimeout(t *testing.T) {
	output, err := CmdRunWithTimeout(300*time.Second, "ls ")
	fmt.Println(output, err)
}

func TestGetSelfIp(t *testing.T) {
	ip, err := GetSelfIp()
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(ip)
}

func TestSelfDir(t *testing.T) {
	dir := SelfDir()
	fmt.Println(dir)
}

func TestFileExists(t *testing.T) {
	res := FileExists("/home/<USER>/develop/go-path/src/icode.baidu.com/baidu/netdisk/turbo")
	fmt.Println(res)
}

func TestAesEncode(t *testing.T) {
	key := "600ce14d636575eabe6fb005bd321254"
	str := `{"height":48.33,"speed":-1.00,"longitude":116.295736,"bluetooth_rssi":"{\"587956811_651200\":-29}","indoor":1,"latitude":40.053718,"local_time":1574734496,"in_china":1,"direction":37}`
	estr, err := AesEncode(str, key)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Printf("%s\n", estr)

	res, err := AesDecode(estr, key)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(res)
}

func TestAesDecode(t *testing.T) {
	key := "600ce14d636575eabe6fb005bd321254"
	estr := "5hFeCrDSCIrm_MtBd999Auf_FIMD4NZciAQOZaS1_8LpOiC2Df_wGNqOET-0a2rZfogFiZXTLsF68IA2I9sfcg"

	res, err := AesDecode(estr, key)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(res)
}

func TestSignAndVerifyECDSA(t *testing.T) {
	key := `{"Curvname":"P-256","X":29730051251067436053171860772052586643453015628593361507167893995792824700958,"Y":93908713846036751574671999146123019567388029845839743319886551554126325668328,"D":23745788955771652250131082494305383388380478179564057555384979268845434973214}`
	publicKey := `{"Curvname":"P-256","X":29730051251067436053171860772052586643453015628593361507167893995792824700958,"Y":93908713846036751574671999146123019567388029845839743319886551554126325668328}`
	msg := "this is a test"
	signature, err := SignECDSA(key, msg)
	fmt.Println(string(signature))
	if err != nil {
		t.Error(err.Error())
	}
	res, err := VerifyECDSA([]byte(publicKey), signature, []byte(msg))
	if err != nil {
		t.Error(err.Error())
	}
	if !res {
		t.Error("verify failed")
	}
}

func TestEncryptAndDecryptByEcdsaKey(t *testing.T) {
	key := `{"Curvname":"P-256","X":29730051251067436053171860772052586643453015628593361507167893995792824700958,"Y":93908713846036751574671999146123019567388029845839743319886551554126325668328,"D":23745788955771652250131082494305383388380478179564057555384979268845434973214}`
	publicKey := `{"Curvname":"P-256","X":29730051251067436053171860772052586643453015628593361507167893995792824700958,"Y":93908713846036751574671999146123019567388029845839743319886551554126325668328}`
	encryMsg, err := EncryptByEcdsaKey(publicKey, "this is a test")
	fmt.Println(string(encryMsg))
	if err != nil {
		t.Error(err.Error())
	}
	msg, err := DecryptByEcdsaKey([]byte(key), encryMsg)
	fmt.Println(string(msg))
	if err != nil {
		t.Error(err.Error())
	}
}

func TestVerifyAddressUsingPublicKey(t *testing.T) {
	address := "WcrGMFUSxGrvBhk96aPtkxMWU4YEtCmZp"
	pkey := `{"Curvname":"P-256","X":8041837073444679368272206191956647903150227292722032827616690638093619978334,"Y":10535944472769479394255453490812763767626721229837608662946423501011708567050}`

	isMatch, _ := VerifyAddressUsingPublicKey(address, pkey)
	fmt.Println("is match:", isMatch)

	//false
	address = "WcrGMFUSxGrvBhkHaHaHaHaHaHaHaHaHa"
	isMatch, _ = VerifyAddressUsingPublicKey(address, pkey)
	fmt.Println("is match:", isMatch)
}

func TestStrMapInt(t *testing.T) {
	testBit := 7
	pages := 128
	nums := 1000000

	li := make([]int, pages)
	for i := 0; i <= 1000000; i++ {
		randomStr := fmt.Sprintf("%d", GenRandId())
		randomID := StrMapInt(randomStr, testBit)
		li[randomID] += 1
	}

	var res float64 = 0
	for i := 0; i < pages; i++ {
		res += float64(li[i]) / float64(nums)
	}
	mean := res / float64(pages)

	res = float64(0)
	for i := 0; i < pages; i++ {
		res += (float64(li[i])/float64(nums) - mean) * (float64(li[i])/float64(nums) - mean)
	}

	variance := res / float64(pages)
	std := math.Sqrt(variance)
	if std > 0.0001 {
		t.Errorf("StrMapInt invalid")
	}
	t.Logf("mean: %.10f, variance: %.10f, std: %.10f", mean, variance, std)
}
