package uuap

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"

	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

const (
	BindCheckByUnameUrl  = "/restful/v1/userInfo/isEmployeeByPassportUsername" // 判断账号是否是公司内部账号
	GetUserByPassNameUrl = "/restful/v1/userInfo/getUserByPassportUsername"    // 通过passport username获取用户信息
	GetAllUsersUrl       = "/restful/full/userInfo/getAllUsers"                // 查询全部用户
	GetUserByEmailUrl    = "/restful/v1/userInfo/getUserByUserEmail"           // 通过邮箱获取用户信息
	GetUserByUserNameUrl = "/restful/v1/userInfo/getUserByUsername"            // 通过用户名获取用户信息
)

const (
	ReqConnTimeoutMs      = 600
	ReqRWTimeoutMs        = 1800
	AllUserConnTimeoutsMs = 600
	AllUserRWTimeoutsMs   = 20000 // 全量用户查询，耗时比较长，定义20s
)

type UuapClient struct {
	cfg *Config
}

type Config struct {
	Endpoint  string // uuap地址
	AppKey    string // uuap接入appKey
	SecretKey string // uuap接入secretKey
}

func NewUuapClient(endpoint, appKey, secretKey string) *UuapClient {
	return &UuapClient{
		cfg: &Config{
			Endpoint:  endpoint,
			AppKey:    appKey,
			SecretKey: secretKey,
		},
	}
}

// CheckIfInternalUser 判断是否是内部用户
func (c *UuapClient) CheckIfInternalUser(uname string) (bool, error) {
	args := make(map[string]string)
	args["passportUsername"] = uname
	c.genUuapSign(args)
	req, err := json.Marshal(args)
	if err != nil {
		return false, err
	}
	url := fmt.Sprintf("%s%s", c.cfg.Endpoint, BindCheckByUnameUrl)
	respBody, err := c.doPost(url, string(req))
	if err != nil {
		return false, err
	}
	resp := new(CheckUserBindResponse)
	if err := json.Unmarshal(respBody, resp); err != nil {
		return false, fmt.Errorf("unmarshal resp body failed, %#v", err)
	}
	if resp.Code != 200 {
		return false, fmt.Errorf("resp code %#v, desc %s", resp.Code, resp.Desc)
	}

	isInternal, ok := resp.Result.(bool)
	if !ok {
		return false, fmt.Errorf("resp code %d, result %#v", resp.Code, resp.Result)
	}
	return isInternal, nil
}

// GetUserInfoByPassName 通过passport用户名获取用户信息
func (c *UuapClient) GetUserInfoByPassName(passName string) (*User, error) {
	args := make(map[string]string)
	args["passportUsername"] = passName
	args["returnFields"] = strings.Join(UserInfoReturnField, ",")
	c.genUuapSign(args)
	req, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}
	url := fmt.Sprintf("%s%s", c.cfg.Endpoint, GetUserByPassNameUrl)
	respBody, err := c.doPost(url, string(req))
	if err != nil {
		return nil, err
	}
	resp := new(GetUserInfoResponse)
	if err := json.Unmarshal(respBody, resp); err != nil {
		return nil, fmt.Errorf("unmarshal resp body failed, %#v", err)
	}
	if resp.Code != 200 {
		return nil, fmt.Errorf("resp code %#v, desc %s", resp.Code, resp.Desc)
	}
	if resp.Result == nil {
		return nil, fmt.Errorf("no user info found")
	}
	return resp.Result, nil
}

// GetUserInfoByPassName 通过passport用户名获取用户信息
func (c *UuapClient) GetUserInfoByUserEmail(email string) (*User, error) {
	args := make(map[string]string)
	args["userEmail"] = email
	args["returnFields"] = strings.Join(UserInfoReturnField, ",")
	c.genUuapSign(args)
	req, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}
	url := fmt.Sprintf("%s%s", c.cfg.Endpoint, GetUserByEmailUrl)
	respBody, err := c.doPost(url, string(req))
	if err != nil {
		return nil, err
	}
	resp := new(GetUserInfoResponse)
	if err := json.Unmarshal(respBody, resp); err != nil {
		return nil, fmt.Errorf("unmarshal resp body failed, %#v", err)
	}
	if resp.Code != 200 {
		return nil, fmt.Errorf("resp code %#v, desc %s", resp.Code, resp.Desc)
	}
	if resp.Result == nil {
		return nil, fmt.Errorf("no user info found")
	}
	return resp.Result, nil
}

// GetUserInfoByPassName 通过passport用户名获取用户信息
func (c *UuapClient) GetUserInfoByUserName(uname string) (*User, error) {
	args := make(map[string]string)
	args["username"] = uname
	args["returnFields"] = strings.Join(UserInfoReturnField, ",")
	c.genUuapSign(args)
	req, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}
	url := fmt.Sprintf("%s%s", c.cfg.Endpoint, GetUserByUserNameUrl)
	respBody, err := c.doPost(url, string(req))
	if err != nil {
		return nil, err
	}
	resp := new(GetUserInfoResponse)
	if err := json.Unmarshal(respBody, resp); err != nil {
		return nil, fmt.Errorf("unmarshal resp body failed, %#v", err)
	}
	if resp.Code != 200 {
		return nil, fmt.Errorf("resp code %#v, desc %s", resp.Code, resp.Desc)
	}
	if resp.Result == nil {
		return nil, fmt.Errorf("no user info found")
	}
	return resp.Result, nil
}

// GetAllUsers 获取全部用户
func (c *UuapClient) GetAllUsers() ([]*User, error) {
	args := make(map[string]string)
	args["returnFields"] = strings.Join(UserInfoReturnField, ",")
	c.genUuapSign(args)
	req, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}
	url := fmt.Sprintf("%s%s", c.cfg.Endpoint, GetAllUsersUrl)
	// 全量用户接口，请求时间比较长
	respBody, err := c.doPost(url, string(req), AllUserConnTimeoutsMs, AllUserRWTimeoutsMs)
	if err != nil {
		return nil, err
	}
	resp := new(GetAllUserResponse)
	if err := json.Unmarshal(respBody, resp); err != nil {
		return nil, fmt.Errorf("unmarshal resp body failed, %#v", err)
	}
	if resp.Code != 200 {
		return nil, fmt.Errorf("resp code %#v, desc %s", resp.Code, resp.Desc)
	}
	var userList []*User
	users, err := json.Marshal(resp.Result)
	if err != nil {
		return nil, fmt.Errorf("get user info failed, %v", err)
	}
	if err := json.Unmarshal(users, &userList); err != nil {
		return nil, fmt.Errorf("get user info failed, %v", err)
	}
	return userList, nil
}

// genUuapSign 根据规范生成请求签名
// refer to http://wiki.baidu.com/pages/viewpage.action?pageId=522201057
func (c *UuapClient) genUuapSign(args map[string]string) {
	// 构造完整的参数
	args["appKey"] = c.cfg.AppKey
	args["sRandom"] = strings.Replace(uuid.NewV4().String(), "-", "", -1)
	args["timestamp"] = strconv.FormatInt(time.Now().Unix(), 10)
	signParams := ""
	keys := make([]string, 0)
	for k := range args {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		signParams = signParams + args[k]
	}
	signParams = signParams + c.cfg.SecretKey
	h := sha256.New()
	h.Write([]byte(signParams))
	bs := h.Sum(nil)
	sign := hex.EncodeToString(bs)
	args["sign"] = sign
	return
}

func (c *UuapClient) doPost(url string, body string, timeouts ...time.Duration) ([]byte, error) {
	opts := map[string]string{
		"OptDisableAutoSetPostContentType": "1",
		"OptTlsSipVerify":                  "1", // https请求，跳过ssl检查
	}
	header := map[string]string{
		"Content-Type": "application/json",
	}
	var resp httpclient.HttpResponse
	var err error
	if len(timeouts) == 2 {
		resp, err = httpclient.Post(url, header, timeouts[0], timeouts[1], body, opts)
	} else {
		resp, err = httpclient.Post(url, header, ReqConnTimeoutMs, ReqRWTimeoutMs, body, opts)
	}
	if err != nil {
		return nil, err
	}
	if resp.StatusCode >= 400 {
		return nil, fmt.Errorf("failed with status code %d err: %s", resp.StatusCode, string(resp.Body))
	}
	return resp.Body, nil
}
