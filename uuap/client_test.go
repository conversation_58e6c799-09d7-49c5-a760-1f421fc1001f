package uuap

import (
	"encoding/json"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

var (
	Endpoint  = "https://itebeta.baidu.com/uic"
	AppKey    = "uuapclient-683335188899016705-3XWTl-beta"
	SecretKey = "a7af69ae5778437f994d2f"
)

func TestCheckIfInternalUser(t *testing.T) {
	client := NewUuapClient(Endpoint, AppKey, <PERSON>Key)
	uname := "回家的柳絮"
	intUser, err := client.CheckIfInternalUser(uname)
	assert.Nil(t, err)
	fmt.Println(intUser)
}

func TestGetUserInfoByPassName(t *testing.T) {
	client := NewUuapClient(Endpoint, AppKey, SecretKey)
	uname := "回家的柳絮"
	user, err := client.GetUserInfoByPassName(uname)
	assert.Nil(t, err)
	fmt.Printf("%#v", user)
}

func TestGetUserInfoByUserEmail(t *testing.T) {
	client := NewUuapClient(<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	uname := "<EMAIL>"
	user, err := client.GetUserInfoByUserEmail(uname)
	assert.Nil(t, err)
	fmt.Printf("%#v", user)
}

func TestGetUserInfoByUserName(t *testing.T) {
	client := NewUuapClient(Endpoint, AppKey, SecretKey)
	uname := "wangzhuo12"
	user, err := client.GetUserInfoByUserName(uname)
	assert.Nil(t, err)
	fmt.Printf("%#v", user)
}

func TestGetAllUsers(t *testing.T) {
	client := NewUuapClient(Endpoint, AppKey, SecretKey)
	userList, err := client.GetAllUsers()
	assert.Nil(t, err)
	fmt.Printf("%v", userList)
	fmt.Println(len(userList))
}

func TestUserBigBaidu(t *testing.T) {
	userStr := `{
		"username":"wangzhuo12",
		"name":"yy",
		"inHq":"N",
		}`
	var user User
	json.Unmarshal([]byte(userStr), &user)
	fmt.Println(user.InGroupBaidu())
}

func TestUserType(t *testing.T) {
	client := NewUuapClient(Endpoint, AppKey, SecretKey)
	userList, err := client.GetAllUsers()
	if err != nil {
		fmt.Println("get all user failed", err)
		return
	}
	hqNum, baiduNum := 0, 0
	fmt.Println("start solving users")
	for i, user := range userList {
		// 加个时间，避免高并发拖垮服务器
		time.Sleep(10 * time.Millisecond)
		// 外包无法查询数据，直接跳过
		if strings.HasPrefix(user.UserName, "v_") {
			fmt.Printf("\nuser %s is not in hq, skipped\n", user.UserName)
		}
		u, err := client.GetUserInfoByUserName(user.UserName)
		if err != nil {
			fmt.Printf("\nget user %s failed %v\n", user.UserName, err)
			continue
		}
		fmt.Printf("%d,", i)
		if u.InHQ() {
			hqNum++
		}
		if u.InGroupBaidu() {
			baiduNum++
		}
	}
	fmt.Println("total user", len(userList))
	fmt.Println("hq user", hqNum)
	fmt.Println("baidu user", baiduNum)
}
