package uuap

var (
	UserInfoReturnField = []string{"username", "name", "hiNumber", "employeeType", "businessGroupId", "inHq", "positionMajorSeq", "positionMinorSeq"}
)

type CheckUserBindResponse struct {
	Code   int         `json:"code"`
	Desc   string      `json:"desc"`
	Result interface{} `json:"result"`
}

type GetUserInfoResponse struct {
	Code   int    `json:"code"`
	Desc   string `json:"desc"`
	Result *User  `json:"result"`
}

type GetAllUserResponse struct {
	Code   int         `json:"code"`
	Desc   string      `json:"desc"`
	Result interface{} `json:"result"`
}
