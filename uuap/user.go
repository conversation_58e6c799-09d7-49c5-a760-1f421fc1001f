package uuap

import "encoding/json"

const (
	UserSeqT      = "T"      // 研发序列
	UserSeqP      = "P"      // 产品开发&产品运营
	UserSeqU      = "U"      // 用户体验研究&用户界面视觉和交互设计
	UserSeqE      = "E"      // 政企行业解决方案和服务
	UserSeqNormal = "NORMAL" //其他通用类型
)

const (
	MajorSeqRD  = "研发"
	MajorSeqEnt = "政企行业解决方案和服务"
	MajorSeqPM  = "产品"
	MinorSeqPM  = "产品开发"
	MinorSeqOM  = "产品运营"
	MinorSeqUI  = "用户体验研究"
	MinorSeqUX  = "用户界面视觉和交互设计"
)

const (
	InHQ         = "Y" // 是总部用户
	GroupIdBaidu = 0   //大百度groupId
)

const (
	EmployeeTypeInternship = "实习"
)

type User struct {
	// 用户登录名
	UserName string `json:"username,omitempty"`
	// 用户姓名
	Name string `json:"name,omitempty"`
	// 百度Passport登录名
	HiNumber string `json:"hiNumber,omitempty"`
	// 用户职称大序列，如 研发、管理(string)
	PositionMajorSeq string `json:"positionMajorSeq,omitempty"`
	// 职位小序列，如 开发、测试、运维、过程管理
	PositionMinorSeq string `json:"positionMinorSeq,omitempty"`
	// 是否总部 如：Y/N
	InHq string `json:"inHq,omitempty"`
	// 业务组ID(int) 如:2959
	BusinessGroupId *int `json:"businessGroupId,omitempty"`
	//员工类型(string) 如：员工/专用账号/项目外包/人力外包/劳务/实习/Apollo/自有账号
	EmployeeType string `json:"employeeType,omitempty"`
}

func (u *User) String() string {
	userBytes, err := json.Marshal(u)
	if err != nil {
		return ""
	}
	return string(userBytes)
}

// GetUserSeq 获取用户所属序列
func (u *User) GetUserSeq() string {
	switch u.PositionMajorSeq {
	case MajorSeqRD:
		return UserSeqT
	case MajorSeqEnt:
		return UserSeqE
	case MajorSeqPM:
		switch u.PositionMinorSeq {
		case MinorSeqPM, MinorSeqOM:
			return UserSeqP
		case MinorSeqUI, MinorSeqUX:
			return UserSeqU
		}
	}
	return UserSeqNormal
}

// InHQ 是否是总部用户
func (u *User) InHQ() bool {
	return u.InHq == InHQ
}

// InGroupBaidu 属于大总部用户
func (u *User) InGroupBaidu() bool {
	if u.BusinessGroupId == nil {
		return false
	}
	return *(u.BusinessGroupId) == GroupIdBaidu
}
