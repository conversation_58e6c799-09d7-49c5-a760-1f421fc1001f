package warden

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/httpclient"
)

type Warden struct {
	addr *addr.Addr
}

func NewWarden(adr *addr.Addr) *Warden {
	return &Warden{adr}
}

func (t *Warden) CreateTask(param *WDCreateTaskParam) (*BaseResp, string, error) {
	if param == nil || !param.IsValid() {
		return nil, "", errors.New("param error")
	}
	if err := t.isInit(); err != nil {
		return nil, "", err
	}

	resp, reqUrl, err := t.doRequest(WardenApiCreate, param.UrlEncode())
	if err != nil {
		return nil, reqUrl, fmt.Errorf("request warden fail.err:%v", err)
	}

	var result BaseResp
	err = json.Unmarshal([]byte(resp), &result)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("json unmarshal warden response fail.resp:%s err:%v", resp, err)
	}

	return &result, reqUrl, nil
}

func (t *Warden) FinishTask(taskId int64) (*BaseResp, string, error) {
	if err := t.isInit(); err != nil {
		return nil, "", err
	}

	v := url.Values{}
	v.Set("task_id", fmt.Sprintf("%d", taskId))
	body := v.Encode()

	resp, reqUrl, err := t.doRequest(WardenApiFinish, body)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("request warden fail.err:%v", err)
	}

	var result BaseResp
	err = json.Unmarshal([]byte(resp), &result)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("json unmarshal warden response fail.resp:%s err:%v", resp, err)
	}

	return &result, reqUrl, nil
}

func (t *Warden) CancelTask(taskId int64) (*BaseResp, string, error) {
	if err := t.isInit(); err != nil {
		return nil, "", err
	}

	v := url.Values{}
	v.Set("task_id", fmt.Sprintf("%d", taskId))
	body := v.Encode()

	resp, reqUrl, err := t.doRequest(WardenApiCancel, body)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("request warden fail.err:%v", err)
	}

	var result BaseResp
	err = json.Unmarshal([]byte(resp), &result)
	if err != nil {
		return nil, reqUrl, fmt.Errorf("json unmarshal warden response fail.resp:%s err:%v", resp, err)
	}

	return &result, reqUrl, nil
}

// post. data is json string
func (t *Warden) doRequest(api string, data string) (string, string, error) {
	var resp httpclient.HttpResponse
	var err error
	var reqUrl string

	header := make(map[string]string)
	header["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8"

	var hostInfo *addr.HostInfo
	for retry := 0; retry < 3; retry++ {
		hostInfo, err = t.addr.GetAddr()
		if err != nil {
			continue
		}

		reqUrl = fmt.Sprintf("http://%s:%s%s", hostInfo.Host, hostInfo.Port, api)
		resp, err = httpclient.Post(reqUrl, header, ReqConnTimeoutMs, ReqRWTimeoutMs, data, nil)
		if err == nil {
			t.addr.SetOk(hostInfo)
			break
		}
		t.addr.SetFail(hostInfo)
	}

	if err != nil {
		return "", reqUrl, err
	}
	if resp.StatusCode != 200 {
		return "", reqUrl, fmt.Errorf("request warden status not 200.http_code:%d", resp.StatusCode)
	}

	return string(resp.Body), reqUrl, nil
}

func (t *Warden) isInit() error {
	if t.addr == nil {
		return errors.New("param error")
	}

	return nil
}
