package warden

import (
	"encoding/json"
	"fmt"
	"net/url"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
)

// 按模块分配标记
const (
	WardenOpModHorae = 100100
	WardenOpModUI    = 200100
	WardenOpModLab   = 200200
)

// 任务类型
const (
	WDTaskTypePlayback = 1
)

const (
	WardenErrSucc      = 0
	WardenErrParamErr  = 40001
	WardenErrInternal  = 50000
	WardenErrDatabase  = 50001
	WardenErrPubFail   = 50003
	WardenErrExists    = 20100
	WardenErrNotExists = 20101
)

const (
	ReqConnTimeoutMs = 200
	ReqRWTimeoutMs   = 1200
)

const (
	WardenApiCreate = "/internal/warden/v1/create"
	WardenApiFinish = "/internal/warden/v1/finish"
	WardenApiCancel = "/internal/warden/v1/cancel"
)

type BaseResp struct {
	RequestId string `json:"request_id"`
	Errno     int    `json:"errno"`
	Errmsg    string `json:"errmsg"`
}

// create task param
type WDCreateTaskParam struct {
	TaskId     int64  `json:"task_id"`
	OPMod      int    `json:"op_mod"`
	TaskType   int    `json:"task_type"`
	TaskDesc   string `json:"task_desc"`
	TaskNotice string `json:"task_notice"`
	TaskBody   string `json:"task_body"`
}

// check create task param isvalid
func (t *WDCreateTaskParam) IsValid() bool {
	if t.TaskId < 1 || t.OPMod < 1 || t.TaskType < 1 || len(t.TaskDesc) < 1 {
		return false
	}

	if len(t.TaskDesc) > 2000 || !t.checkNotice() || len(t.TaskBody) > 2000 {
		return false
	}

	return true
}

func (t *WDCreateTaskParam) checkNotice() bool {
	if len(t.TaskNotice) < 1 {
		return true
	}
	if len(t.TaskNotice) > 500 {
		return false
	}

	var notice WDPBTypeTaskNotice
	err := json.Unmarshal([]byte(t.TaskNotice), &notice)
	if err != nil {
		return false
	}

	switch notice.Method {
	case "BNS":
		_, err = addr.NewAddrByBns(notice.Endpoint)
	case "HOST":
		var hosts []string
		err = json.Unmarshal([]byte(notice.Endpoint), &hosts)
		if err == nil {
			_, err = addr.NewAddrByHost(hosts)
		}
	}
	if err != nil {
		return false
	}

	return true
}

func (t *WDCreateTaskParam) UrlEncode() string {
	v := url.Values{}
	v.Set("task_id", fmt.Sprintf("%d", t.TaskId))
	v.Set("op_mod", fmt.Sprintf("%d", t.OPMod))
	v.Set("task_type", fmt.Sprintf("%d", t.TaskType))
	v.Set("task_desc", t.TaskDesc)
	if t.TaskNotice != "" {
		v.Set("task_notice", t.TaskNotice)
	}
	if t.TaskBody != "" {
		v.Set("task_body", t.TaskBody)
	}
	return v.Encode()
}

// warden playback type task retry_rule format
type WDPBTRetryRule struct {
	Interval  int `json:"interval"`
	Expire    int `json:"expire"`
	MaxRetrys int `json:"max_retrys"`
	RetryMod  int `json:"retry_mod"`
}

// warden playback type task task_desc format
type WDPBTypeTaskDesc struct {
	Version   int               `json:"version"`
	RetryRule WDPBTRetryRule    `json:"retry_rule"`
	Query     map[string]string `json:"query"`
}

// warden playback type task task_notice format
type WDPBTypeTaskNotice struct {
	Method   string `json:"method"`
	Endpoint string `json:"endpoint"`
	Uri      string `json:"uri"`
}
