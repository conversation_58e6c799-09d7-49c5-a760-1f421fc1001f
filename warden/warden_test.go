package warden

import (
	"encoding/json"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/blockchain/xasset-golib/addr"
	"icode.baidu.com/baidu/blockchain/xasset-golib/utils"
)

var testHost []string = []string{"127.0.0.1:8383"}

func TestCreateTask(t *testing.T) {
	param := genCreataParam()
	res, host, err := getWarden().CreateTask(param)
	if err != nil {
		t.Errorf("create task failed.err:%v", err)
		return
	}

	fmt.Printf("create task succ.url:%s,resp:%v\n", host, res)
}

func TestFinishTask(t *testing.T) {
	param := genCreataParam()
	res, host, err := getWarden().CreateTask(param)
	if err != nil {
		t.Errorf("create task failed.err:%v", err)
		return
	}
	fmt.Printf("create task succ.url:%s,resp:%v\n", host, res)

	res, host, err = getWarden().FinishTask(param.TaskId)
	if err != nil {
		t.Errorf("finish task failed.err:%v", err)
		return
	}

	fmt.Printf("finish task succ.url:%s,resp:%v\n", host, res)
}

func TestCancelTask(t *testing.T) {
	param := genCreataParam()
	res, host, err := getWarden().CreateTask(param)
	if err != nil {
		t.Errorf("create task failed.err:%v", err)
		return
	}
	fmt.Printf("create task succ.url:%s,resp:%v\n", host, res)

	res, host, err = getWarden().CancelTask(param.TaskId)
	if err != nil {
		t.Errorf("cancel task failed.err:%v", err)
		return
	}

	fmt.Printf("cancel task succ.url:%s,resp:%v\n", host, res)
}

func getWarden() *Warden {
	adr, _ := addr.NewAddrByHost(testHost)
	return NewWarden(adr)
}

func genCreataParam() *WDCreateTaskParam {
	tDesc := &WDPBTypeTaskDesc{
		Version: 1,
		RetryRule: WDPBTRetryRule{
			Interval:  60,
			Expire:    3600,
			MaxRetrys: 6,
			RetryMod:  2,
		},
		Query: map[string]string{
			"test": "test",
		},
	}
	desc, _ := json.Marshal(tDesc)

	hosts := []string{"127.0.0.1:8383"}
	hstr, _ := json.Marshal(hosts)
	tNotice := &WDPBTypeTaskNotice{
		Method:   "HOST",
		Endpoint: string(hstr),
		Uri:      "/internal/warden/v1/checkalive",
	}
	notice, _ := json.Marshal(tNotice)

	param := &WDCreateTaskParam{
		TaskId:     utils.GenNonce(),
		OPMod:      WardenOpModHorae,
		TaskType:   WDTaskTypePlayback,
		TaskDesc:   string(desc),
		TaskNotice: string(notice),
		TaskBody:   "test body",
	}

	return param
}
